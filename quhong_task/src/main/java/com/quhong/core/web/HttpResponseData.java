package com.quhong.core.web;

public class HttpResponseData<T> {
    private int status;
    private T body;

    public HttpResponseData() {

    }

    public boolean isOK() {
        return status == 0 || status == 200;
    }

    public boolean isError() {
        return !isOK();
    }

    public boolean is2xxSuccessful() {
        return status >= 200 && status < 300;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }
}
