package com.quhong.core.utils;

import com.quhong.datas.DayTimeData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DateHelper {
    private static final Logger logger = LoggerFactory.getLogger(DateHelper.class);

    public static final DateHelper UTC = new DateHelper(TimeZone.getTimeZone("UTC"));
    public static final DateHelper INDIAN = new DateHelper(TimeZone.getTimeZone("GMT+5:30"));
    public static final DateHelper ARABIAN = new DateHelper(TimeZone.getTimeZone("GMT+3:00"));
    public static final DateHelper BEIJING = new DateHelper(TimeZone.getTimeZone("GMT+8"));

    public static final DateHelper DEFAULT = DateHelper.ARABIAN;

    public static int getNowSeconds() {
        return (int) (System.currentTimeMillis() / 1000);
    }

    public static Date formatDate(long timeSecond) {
        return new Date(timeSecond * 1000L);
    }

    private TimeZone timeZone;

    private ThreadLocalDateFormat tableSuffixFormat;
    private ThreadLocalDateFormat tableDaySuffixFormat;
    private ThreadLocalCalendar localCalendar;
    private ThreadLocalDateFormat dateFormat;
    private ThreadLocalDateFormat monthFormat;
    private ThreadLocalDateFormat dateHourFormat;
    private ThreadLocalDateFormat hourFormat;
    private ThreadLocalDateFormat dateFormat2;
    private ThreadLocalDateFormat dateTimeFormat;
    private ThreadLocalDateFormat hourFormat2;
    private ThreadLocalDateFormat hourFormat3;
    private ThreadLocalDateFormat thinkingDataFormat;
    private ThreadLocalDateFormat monthFormat2;
    private ThreadLocalDateFormat dateHourMinuteFormat;

    public DateHelper(TimeZone timeZone) {
        this.timeZone = timeZone;
        this.localCalendar = new ThreadLocalCalendar(this.timeZone);
        this.tableSuffixFormat = new ThreadLocalDateFormat("yyyy_MM", this.timeZone);
        this.tableDaySuffixFormat = new ThreadLocalDateFormat("yyyy_MM_dd", this.timeZone);
        this.dateFormat = new ThreadLocalDateFormat("yyyy-MM-dd", this.timeZone);
        this.monthFormat = new ThreadLocalDateFormat("yyyy-MM", this.timeZone);
        this.dateHourFormat = new ThreadLocalDateFormat("yyyy-MM-dd#HH", this.timeZone);
        this.hourFormat = new ThreadLocalDateFormat("HH:mm:ss", TimeZone.getTimeZone("UTC"));

        this.dateFormat2 = new ThreadLocalDateFormat("yyyyMMdd", this.timeZone);
        this.dateTimeFormat = new ThreadLocalDateFormat("yyyy-MM-dd HH:mm:ss", this.timeZone);
        this.hourFormat2 = new ThreadLocalDateFormat("HH:mm", this.timeZone);
        this.hourFormat3 = new ThreadLocalDateFormat("HH:mm:ss", this.timeZone);
        this.thinkingDataFormat = new ThreadLocalDateFormat("yyyy-MM-dd HH:mm:ss.SSS", this.timeZone);
        this.monthFormat2 = new ThreadLocalDateFormat("yyyyMM", this.timeZone);
        this.dateHourMinuteFormat = new ThreadLocalDateFormat("yyyy-MM-dd#HH:mm", this.timeZone);
    }

    public String getTableSuffix() {
        return getTableSuffix(new Date());
    }

    public String getTableSuffix(Date date) {
        return tableSuffixFormat.get().format(date);
    }

    public String getDayTableSuffix(Date date) {
        return tableDaySuffixFormat.get().format(date);
    }

    public long parseTableSuffix(String dateStr) {
        try {
            return tableSuffixFormat.get().parse(dateStr).getTime();
        } catch (Exception e) {
            logger.error("parse dateStr error.dataStr={} {}", dateStr, e.getMessage(), e);
        }
        return -1;
    }

    public List<String> getTableSuffixList(int start, int end) {
        return getTableSuffixList(start * 1000L, end * 1000L);
    }

    public List<String> getTableSuffixList(long start, long end) {
        Date startDate = new Date(start);
        Date endDate = new Date(end);
        return getTableSuffixList(startDate, endDate);
    }

    public List<String> getTableSuffixList(Date startDate, Date endDate) {
        List<String> suffixList = new ArrayList<>();
        Calendar calendar = setMonthStartCalendar(startDate);
        while (startDate.getTime() < endDate.getTime()) {
            suffixList.add(tableSuffixFormat.get().format(startDate));
            calendar.setTime(startDate);
            //加一个月
            calendar.add(Calendar.MONTH, 1);
            Date nextDate = calendar.getTime();
            //将其设置为当月的第一天
            calendar = setMonthStartCalendar(nextDate);
            // 获取增加后的日期
            startDate = calendar.getTime();
        }
        return suffixList;
    }

    /**
     * 设置当前月第一天 Calendar
     *
     * @param date 时间对象
     * @return
     */
    public Calendar setMonthStartCalendar(Date date) {
        Calendar calendar = localCalendar.get();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1); //1号
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.setTimeZone(timeZone);
        return calendar;
    }

    public Calendar setTodayStartTime() {
        Calendar calendar = localCalendar.get();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    public long getDateTime() {
        Calendar calendar = localCalendar.get();
        calendar.setTime(new Date());
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTimeInMillis();
    }

    public long getTodayStartTime() {
        Calendar calendar = setTodayStartTime();
        return calendar.getTimeInMillis();
    }

    public long getDayOffset(int deltaDay) {
        Calendar calendar = setTodayStartTime();
        calendar.add(Calendar.DAY_OF_MONTH, deltaDay);
        return calendar.getTimeInMillis();
    }

    public long getDayOffset(long time, int deltaDay) {
        Calendar calendar = setTodayStartTime();
        calendar.setTime(new Date(time));
        calendar.add(Calendar.DAY_OF_MONTH, deltaDay);
        return calendar.getTimeInMillis();
    }

    public long getMonthOffset(int deltaMonth) {
        Calendar calendar = setTodayStartTime();
        calendar.add(Calendar.MONTH, deltaMonth);
        return calendar.getTimeInMillis();
    }

    public long getMonthOffset(long time, int deltaMonth) {
        Calendar calendar = setTodayStartTime();
        calendar.setTime(new Date(time));
        calendar.add(Calendar.MONTH, deltaMonth);
        return calendar.getTimeInMillis();
    }

    public Date getLastMonthData() {
        return getLastMonthDate(new Date());
    }

    public Date getLastMonthDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendar.MONTH, -1);//月份减一
        return calendar.getTime();
    }

    public long dateAddDate(long time, int num, int unit) {
        Calendar calendar = localCalendar.get();
        calendar.setTimeInMillis(time);
        calendar.add(unit, num);
        return calendar.getTimeInMillis();
    }

    public String getYesterdayStr(Date date) {
        try {
            return dateFormat.get().format(new Date(date.getTime() - 86400000L));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public String formatDateInHour() {
        return this.dateHourFormat.get().format(new Date());
    }

    public String formatDateInHour(Date date) {
        return this.dateHourFormat.get().format(date);
    }

    public String formatDateInDay() {
        return dateFormat.get().format(new Date());
    }

    public String formatTimeInDay(Date date) {
        return hourFormat2.get().format(date);
    }

    public String formatTimeInDay3(Date date) {
        return hourFormat3.get().format(date);
    }

    public Date parseDate(String dateStr) {
        try {
            return dateFormat.get().parse(dateStr);
        } catch (ParseException e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    public String formatDateInDay(Date date) {
        return dateFormat.get().format(date);
    }

    public String formatDateTime(Date date) {
        return dateTimeFormat.get().format(date);
    }

    public String formatDateInDay2() {
        return dateFormat2.get().format(new Date());
    }

    public String formatDateInDay2(Date date) {
        return dateFormat2.get().format(date);
    }

    public String formatDateInMonth() {
        return monthFormat.get().format(new Date());
    }

    public String formatDateInMonth(Date date) {
        return monthFormat.get().format(date);
    }

    public String formatDateInMonth2() {
        return monthFormat2.get().format(new Date());
    }

    public String formatDateInMonth2(Date date) {
        return monthFormat2.get().format(date);
    }

    public String formatDateInHourMinute() {
        return this.dateHourMinuteFormat.get().format(new Date());
    }

    public String formatDateInHourMinute(Date date) {
        return this.dateHourMinuteFormat.get().format(date);
    }

    public long parseDateInDay(String time) {
        try {
            return dateFormat.get().parse(time).getTime();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return -1;
    }


    public DayTimeData getContinuesDays(String startDay) {
        long todayStartTime = getTodayStartTime();
        DayTimeData timeData = new DayTimeData();
        try {
            Date startDate = dateFormat.get().parse(startDay);
            timeData.setDate(dateFormat.get().format(startDate));
            timeData.setTime((int) (startDate.getTime() / 1000));
            if (startDate.getTime() >= todayStartTime) {
//                logger.info("next day more then today start time. {} nextdayTime= {} todayStartTime={}", timeData.getDate(), startDate.getTime(), todayStartTime);
                timeData.setMoreThenToday(true);
            }
            Date nextDay = getNextDay(startDate.getTime());
            timeData.setEndTime((int) (nextDay.getTime() / 1000));
            return timeData;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public List<DayTimeData> getContinuesDays(int startOffset, int endOffset) {
        Date startDate = new Date(getDayOffset(startOffset));
        Date endDate = new Date(getDayOffset(endOffset));
        return getContinuesDays(startDate, endDate);
    }


    /**
     * 获取时间区间
     *
     * @param startDay
     * @param endDay
     * @return
     */
    public List<DayTimeData> getContinuesDays(String startDay, String endDay) {
        try {
            Date startDate = dateFormat.get().parse(startDay);
            Date endDate = dateFormat.get().parse(endDay);
            return getContinuesDays(startDate, endDate);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    public List<DayTimeData> getContinuesDays(Date startDate, Date endDate) {
        List<DayTimeData> dayList = new ArrayList<>();
        long todayStartTime = getTodayStartTime();
        try {
            if (endDate.getTime() <= startDate.getTime()) {
                endDate.setTime(startDate.getTime());
            }
            Date nextDay = startDate;
            while (nextDay.getTime() <= endDate.getTime()) {
                DayTimeData timeData = new DayTimeData();
                timeData.setDate(dateFormat.get().format(nextDay));
                timeData.setTime((int) (nextDay.getTime() / 1000));
                if (nextDay.getTime() >= todayStartTime) {
//                    logger.info("next day more then today start time. {} nextdayTime= {} todayStartTime={}", timeData.getDate(), nextDay.getTime(), todayStartTime);
                    timeData.setMoreThenToday(true);
                }
                nextDay = getNextDay(nextDay.getTime());
                timeData.setEndTime((int) (nextDay.getTime() / 1000));
                dayList.add(timeData);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return dayList;
    }

    public Date getNextDay(long time) {
        Calendar calendar = setTodayStartTime();
        calendar.setTimeInMillis(time);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 以0时区为准，只是求一个偏移值
     *
     * @param time
     * @return
     */
    public long parseHour(String time) {
        try {
            return hourFormat.get().parse(time).getTime();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 获取连续阶段的分表list
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public List<String> getContinuesTableSuffix(Date startDate, Date endDate) {
        List<String> dayList = new ArrayList<>();
        try {
            if (endDate.getTime() <= startDate.getTime()) {
                endDate.setTime(startDate.getTime());
            }
            Date nextDay = startDate;
            long endTime = endDate.getTime();
            while (nextDay.getTime() <= endTime) {
                String suffix = tableSuffixFormat.get().format(nextDay);
                dayList.add(suffix);
                nextDay = getNextDay(nextDay.getTime());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return dayList;
    }

    public Integer[] getTimeWhereRange(String statDate) {
        Integer startTime = stringDateToStampSecond(statDate);
        int endTime = startTime + (24 * 60 * 60);
        return new Integer[]{startTime, endTime};
    }

    /**
     * 将字符串日期转换成秒数
     *
     * @param strDate “2020-03-23”
     * @return 秒时间戳
     */
    public Integer stringDateToStampSecond(String strDate) {
        try {
            long time = dateFormat.get().parse(strDate).getTime();
            return new Long(time / 1000).intValue();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return -1;
    }

    /**
     * 将字符串日期时间转换成秒数
     *
     * @param strDateTime “2020-03-23 12:12:12”
     * @return 秒时间戳
     */
    public Integer stringDateTimeToStampSecond(String strDateTime) {
        try {
            long time = dateTimeFormat.get().parse(strDateTime).getTime();
            return new Long(time / 1000).intValue();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return -1;
    }

    public String formatDateByThinkingData() {
        return thinkingDataFormat.get().format(new Date());
    }

    public String formatDateByThinkingData(int timestamp) {
        return thinkingDataFormat.get().format(new Date(timestamp * 1000L));
    }

    public String getWeekStartDate() {
        //一周的第一天是周日
        Calendar start = localCalendar.get();
        start.setTime(new Date());
        start.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return dateFormat.get().format(start.getTime());
    }

    public String getWeekEndDate() {
        //一周的最后一天是周六
        Calendar end = localCalendar.get();
        end.setTime(new Date());
        end.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        return dateFormat.get().format(end.getTime());
    }

    public String getPreWeekStartDate() {
        //上一周的周日
        Calendar start = localCalendar.get();
        start.setTime(new Date());
        start.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        start.add(Calendar.DAY_OF_WEEK, -7);
        return dateFormat.get().format(start.getTime());
    }

    public int getWeekEndDateTime(){
        //本周周六的最后时间点
        Calendar end = localCalendar.get();
        end.setTime(new Date());
        end.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY);
        end.set(Calendar.HOUR_OF_DAY, 24);
        end.set(Calendar.MINUTE, 0);
        end.set(Calendar.SECOND, 0);
        return stringDateToStampSecond(dateFormat.get().format(end.getTime()));
    }

    public long getMonthFirstDayTimestamp() {
        Calendar calendar = localCalendar.get();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为当月第一天
        calendar.set(Calendar.HOUR_OF_DAY, 0);  // 设置时间为0点
        calendar.set(Calendar.MINUTE, 0);       // 设置分钟为0
        calendar.set(Calendar.SECOND, 0);       // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0);  // 设置毫秒为0
        return calendar.getTimeInMillis();      // 返回时间戳
    }

}
