package com.quhong.http;

import com.quhong.core.web.HttpResponseData;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.conn.NoopIOSessionStrategy;
import org.apache.http.nio.conn.SchemeIOSessionStrategy;
import org.apache.http.nio.conn.ssl.SSLIOSessionStrategy;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InterruptedIOException;
import java.security.KeyStore;
import java.util.Map;

@Component
public class AsyncHttpClient {
    private static final Logger logger = LoggerFactory.getLogger(AsyncHttpClient.class);

    private CloseableHttpAsyncClient httpclient;
    private RequestConfig config;

    public AsyncHttpClient() {
        init();
    }

    public AsyncHttpClient(int connectTimeout, int socketTimeout) {
        init(connectTimeout,socketTimeout);
    }
    public void init() {
        init(2000, 5000);
    }

    /**
     * @param connectTimeout 连接超时 ms
     * @param socketTimeout  阅读超时 ms
     */
    public void init(int connectTimeout, int socketTimeout) {
        PoolingNHttpClientConnectionManager connManager
                = createConnectionManager();

        if (connectTimeout < 2000) {
            connectTimeout = 2000;
        }
        if (socketTimeout < 3000) {
            socketTimeout = 3000;
        }
        httpclient = HttpAsyncClients.custom().setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                .setConnectionManager(connManager)
                .build();
        httpclient.start();
        config = RequestConfig.custom()
                .setConnectTimeout(connectTimeout).setConnectionRequestTimeout(connectTimeout)
                .setSocketTimeout(socketTimeout).setCookieSpec(CookieSpecs.IGNORE_COOKIES).build();
    }

    private PoolingNHttpClientConnectionManager createConnectionManager() {
        Registry<SchemeIOSessionStrategy> registry = null;
        // 指定信任密钥存储对象和连接套接字工厂
        try {
            KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(trustStore, (arg0, arg1) -> true).build();
            SchemeIOSessionStrategy sslioSessionStrategy = new SSLIOSessionStrategy(sslContext, null, null, (arg0, arg1) -> true);
            RegistryBuilder<SchemeIOSessionStrategy> registryBuilder = RegistryBuilder.create();
            registry = registryBuilder.register("http", NoopIOSessionStrategy.INSTANCE).register("https", sslioSessionStrategy).build();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        IOReactorConfig ioReactorConfig = IOReactorConfig.custom().
                setIoThreadCount(20)
                .setSoKeepAlive(true)
                .build();
        ConnectingIOReactor ioReactor = null;
        try {
            ioReactor = new DefaultConnectingIOReactor(ioReactorConfig);
        } catch (IOReactorException e) {
            logger.error(e.getMessage(), e);
            return null;
        }
        PoolingNHttpClientConnectionManager connManager = new PoolingNHttpClientConnectionManager(ioReactor, registry);
        connManager.setMaxTotal(300);
        connManager.setDefaultMaxPerRoute(100);
        return connManager;
    }

    public void get(String url, Map<String, String> headerMap, HttpCallBack<String> httpCallBack) {
        try {
            HttpGet httpget = new HttpGet(url);
            httpget.setConfig(config);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    httpget.addHeader(entry.getKey(), entry.getValue());
                }
            }
            FutureCallback<HttpResponse> callback = new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse response) {
                    try {
                        HttpEntity entity = response.getEntity();
                        String retString = EntityUtils.toString(response.getEntity());
                        EntityUtils.consume(entity);
                        HttpResponseData<String> responseData = new HttpResponseData<>();
                        responseData.setStatus(response.getStatusLine().getStatusCode());
                        responseData.setBody(retString);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception e) {
                            logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                        }
                    } catch (IOException e) {
                        logger.error(e.getMessage(), e);
                        HttpResponseData<String> responseData = new HttpResponseData<>();
                        responseData.setStatus(AsyncHttpCode.SERVER_ERROR);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception ex) {
                            logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                        }
                    }
                }

                @Override
                public void failed(Exception e) {
                    HttpResponseData<String> responseData = new HttpResponseData<>();
                    responseData.setStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception ex) {
                        logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                    }
                }

                @Override
                public void cancelled() {
                    HttpResponseData<String> responseData = new HttpResponseData<>();
                    responseData.setStatus(AsyncHttpCode.CANCELED);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception e) {
                        logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                    }
                }
            };
            httpclient.execute(httpget, callback);
            ;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void getBytes(String url, HttpCallBack<byte[]> httpCallBack) {
        try {
            HttpGet httpget = new HttpGet(url);
            httpget.setConfig(config);
            FutureCallback<HttpResponse> callback = new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse httpResponse) {
                    try {
                        HttpEntity entity = httpResponse.getEntity();
                        byte[] bytes = EntityUtils.toByteArray(entity);
                        EntityUtils.consume(entity);
                        HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                        responseData.setStatus(httpResponse.getStatusLine().getStatusCode());
                        if (httpResponse.getStatusLine().getStatusCode() == AsyncHttpCode.STATUS_OK) {
                            responseData.setBody(bytes);
                        } else {
                            logger.error("http get bytes error path={} status={}", httpResponse.getLocale().getDisplayScript(), httpResponse.getStatusLine());
                        }
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception e) {
                            logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                        responseData.setStatus(AsyncHttpCode.SERVER_ERROR);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception ex) {
                            logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                        }
                    }
                }

                @Override
                public void failed(Exception e) {
                    logger.error("request failed. url={} {}", url, e.getMessage(), e);
                    HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                    responseData.setStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR);
                    httpCallBack.completed(responseData);
                }

                @Override
                public void cancelled() {
                    HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                    responseData.setStatus(AsyncHttpCode.CANCELED);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception e) {
                        logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                    }
                }
            };
            httpclient.execute(httpget, callback);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void post(String url, String json, Map<String, String> headerMap, HttpCallBack<String> httpCallBack) {
        post(url, json, headerMap, httpCallBack, 3);
    }

    public void post(String url, String json, Map<String, String> headerMap, HttpCallBack<String> httpCallBack, int sendLimit) {
        HtpFailedData failedData = new HtpFailedData();
        if (sendLimit < 1) {
            failedData.executeLimit = 1;
        } else {
            failedData.executeLimit = sendLimit;
        }
        try {
            HttpPost post = new HttpPost(url);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    post.setHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity reqEntity = new StringEntity(json, ContentType.APPLICATION_JSON);
            post.setEntity(reqEntity);
            post.setConfig(config);
            FutureCallback<HttpResponse> callback = new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse response) {
                    try {
                        HttpEntity entity = response.getEntity();
                        String retString = EntityUtils.toString(response.getEntity());
                        EntityUtils.consume(entity);
                        HttpResponseData<String> responseData = new HttpResponseData<>();
                        responseData.setStatus(response.getStatusLine().getStatusCode());
                        responseData.setBody(retString);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception e) {
                            logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        HttpResponseData<String> responseData = new HttpResponseData<>();
                        responseData.setStatus(AsyncHttpCode.SERVER_ERROR);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception ex) {
                            logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                        }
                    }
                }

                @Override
                public void failed(Exception e) {
                    if (e instanceof InterruptedIOException) {
                        logger.error("request failed for retry. exeCount={} url={} {}", failedData.executeCount, url, e.getMessage(), e);
                        failedData.executeCount++;
                        if (failedData.executeCount < failedData.executeLimit) {
                            httpclient.execute(post, this);
                            return;
                        }
                    }
                    logger.error("request failed. exeCount={} url={} {}", failedData.executeCount, url, e.getMessage(), e);
                    HttpResponseData<String> responseData = new HttpResponseData<>();
                    responseData.setStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception ex) {
                        logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                    }
                }

                @Override
                public void cancelled() {
                    HttpResponseData<String> responseData = new HttpResponseData<>();
                    responseData.setStatus(AsyncHttpCode.CANCELED);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception e) {
                        logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                    }
                }
            };
            // logger.info("async http request. json={} url={}", json, url);
            httpclient.execute(post, callback);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void postBytes(String url, byte[] body, HttpCallBack<byte[]> httpCallBack) {
        try {
            HttpPost post = new HttpPost(url);
            ByteArrayEntity reqEntity = new ByteArrayEntity(body, ContentType.APPLICATION_OCTET_STREAM);
            post.setEntity(reqEntity);
            FutureCallback<HttpResponse> callback = new FutureCallback<HttpResponse>() {
                @Override
                public void completed(HttpResponse response) {
                    try {
                        HttpEntity entity = response.getEntity();
                        byte[] retArr = EntityUtils.toByteArray(response.getEntity());
                        EntityUtils.consume(entity);
                        HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                        responseData.setStatus(response.getStatusLine().getStatusCode());
                        responseData.setBody(retArr);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception e) {
                            logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                        HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                        responseData.setStatus(AsyncHttpCode.SERVER_ERROR);
                        try {
                            httpCallBack.completed(responseData);
                        } catch (Exception ex) {
                            logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                        }
                    }
                }

                @Override
                public void failed(Exception e) {
                    logger.error("request failed. url={} {}", url, e.getMessage(), e);
                    HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                    responseData.setStatus(HttpStatus.SC_INTERNAL_SERVER_ERROR);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception ex) {
                        logger.error("async http client. call back error. url={} {}", url, ex.getMessage(), ex);
                    }
                }

                @Override
                public void cancelled() {
                    HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                    responseData.setStatus(AsyncHttpCode.CANCELED);
                    try {
                        httpCallBack.completed(responseData);
                    } catch (Exception e) {
                        logger.error("async http client. call back error. url={} {}", url, e.getMessage(), e);
                    }
                }
            };
            logger.info("send post to url:{}, body.length={}", url, body.length);
            httpclient.execute(post, callback);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void close() {
        try {
            httpclient.close();
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        }
    }

    private static class HtpFailedData {
        int executeCount = 0;
        int executeLimit = 1;
    }
}
