package com.quhong.data;

import java.util.Set;

public class SendGiftData {
    private String from_uid;
    private String to_uid; // 注意：该字段随着不同场景会变化
    private int gid;
    private String gname;
    private Integer gptype;
    private int price;
    private int number;
    private long act_id;
    private int earn_beans;
    private String roomId;
    private int send_type; // 1 指定用户发送，2一对一礼物发送（私信礼物），3全房间发送
    private int msg_type; // 1 全房间发送钻石礼物，3 全房间发送vip礼物，5全房间发送背包礼物
    private Set<String> aid_list;
    private String ctime;

    private String pid; // pk id
    private int os;
    private int versioncode;
    private int gplatform;
    private int gift_cost_type;

    // 1、发普通礼物给个人或麦位用户
    // 2、发vip礼物给个人或麦位用户
    // 3、发普通礼物给全房间用户
    // 4、发vip礼物给全房间用户
    // 5、发背包礼物给个人或麦位用户
    // 6、发背包礼物给全房间用户
    // 7、发私信礼物给个人用户
    // 8 守护页发礼物给个人用户
    // 9 礼物墙发礼物给个人用户
    // 10 朋友圈送礼给个人用户
    private int giving_type; // 数数统计区分

    // 盲盒接收者获得礼物信息
    // 不是盲盒下面值为0
    private int receiveGiftId;
    private int receivePrice;

    public String getFrom_uid() {
        return from_uid;
    }

    public void setFrom_uid(String from_uid) {
        this.from_uid = from_uid;
    }

    public String getTo_uid() {
        return to_uid;
    }

    public void setTo_uid(String to_uid) {
        this.to_uid = to_uid;
    }

    public int getGid() {
        return gid;
    }

    public void setGid(int gid) {
        this.gid = gid;
    }

    public String getGname() {
        return gname;
    }

    public void setGname(String gname) {
        this.gname = gname;
    }

    public Integer getGptype() {
        return gptype;
    }

    public void setGptype(Integer gptype) {
        this.gptype = gptype;
    }

    public int getPrice() {
        return price;
    }

    public void setPrice(int price) {
        this.price = price;
    }

    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    public long getAct_id() {
        return act_id;
    }

    public void setAct_id(long act_id) {
        this.act_id = act_id;
    }

    public int getEarn_beans() {
        return earn_beans;
    }

    public void setEarn_beans(int earn_beans) {
        this.earn_beans = earn_beans;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public int getSend_type() {
        return send_type;
    }

    public void setSend_type(int send_type) {
        this.send_type = send_type;
    }

    public Set<String> getAid_list() {
        return aid_list;
    }

    public int getGplatform() {
        return gplatform;
    }

    public void setGplatform(int gplatform) {
        this.gplatform = gplatform;
    }

    public void setAid_list(Set<String> aid_list) {
        this.aid_list = aid_list;
    }

    public String getCtime() {
        return ctime;
    }

    public void setCtime(String ctime) {
        this.ctime = ctime;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public int getVersioncode() {
        return versioncode;
    }

    public void setVersioncode(int versioncode) {
        this.versioncode = versioncode;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public int getMsg_type() {
        return msg_type;
    }

    public void setMsg_type(int msg_type) {
        this.msg_type = msg_type;
    }

    public int getGift_cost_type() {
        return gift_cost_type;
    }

    public void setGift_cost_type(int gift_cost_type) {
        this.gift_cost_type = gift_cost_type;
    }

    public int getGiving_type() {
        return giving_type;
    }

    public void setGiving_type(int giving_type) {
        this.giving_type = giving_type;
    }

    public int getReceiveGiftId() {
        return receiveGiftId;
    }

    public void setReceiveGiftId(int receiveGiftId) {
        this.receiveGiftId = receiveGiftId;
    }

    public int getReceivePrice() {
        return receivePrice;
    }

    public void setReceivePrice(int receivePrice) {
        this.receivePrice = receivePrice;
    }
}
