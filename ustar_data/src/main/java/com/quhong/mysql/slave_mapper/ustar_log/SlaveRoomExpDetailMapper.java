package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.data.RoomExpDetailData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SlaveRoomExpDetailMapper extends ShardingMapper {

    List<RoomExpDetailData> getAllByDay(@Param("dateMonth") String dateMonth,
                                      @Param("dateStr") String dateStr);
}
