package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.data.HeartRecordData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SlaveHeartRecordMapper extends ShardingMapper {


    @Select({
            "<script>",
            "SELECT id,changed,title,remark,c_time AS ctime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT id,changed,title,remark,c_time FROM t_heart_record_${tableSuffix} ",
            "WHERE uid=#{uid} ",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC,id DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<HeartRecordData> getDetails(@Param("uid") String uid, @Param("start") int start, @Param("size") int size, @Param("suffixList") List<String> suffixList);


}
