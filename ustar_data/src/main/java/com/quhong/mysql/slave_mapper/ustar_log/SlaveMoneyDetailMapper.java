package com.quhong.mysql.slave_mapper.ustar_log;

import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SlaveMoneyDetailMapper extends ShardingMapper {

    @Select({
            "<script>",
            "SELECT IFNULL(sum(changed),0) as sumChanged FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT IFNULL(sum(changed),0) as changed FROM t_money_detail_${tableSuffix} ",
            "WHERE uid=#{uid} and atype in <foreach collection = 'typeList' open = '(' item = 'item' separator = ',' close = ') '> #{item} </foreach> ",
            "and <![CDATA[ ctime >= #{startTime} and ctime <= #{endTime}]]>",
            "</foreach>",
            ") as stat",
            "</script>"
    })
    long getTotalBeanByType(@Param("uid") String uid, @Param("typeList") List<Integer> typeList, @Param("startTime") int startTime, @Param("endTime") int endTime, @Param("suffixList") List<String> suffixList);

    @Select({
            "<script>",
            "SELECT IFNULL(sum(changed),0) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT IFNULL(sum(changed),0) as changed from t_money_detail_${tableSuffix} ",
            "where uid = #{uid} ",
            "AND atype = #{aType} ",
            "AND <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    int calculateTotalBeanSum(@Param("suffixList") List<String> retList, @Param("uid") String uid, @Param("aType") int aType, @Param("startTime") int startTime, @Param("endTime") int endTime);

    @Select("SELECT count(*) FROM t_money_detail_${tableSuffix} WHERE atype = #{aType} and mtime >= #{startTime} and mtime <= #{endTime}")
    int getRecordCount(@Param("tableSuffix") String tableSuffix, @Param("aType") int aType, @Param("startTime") int startTime, @Param("endTime") int endTime);
}
