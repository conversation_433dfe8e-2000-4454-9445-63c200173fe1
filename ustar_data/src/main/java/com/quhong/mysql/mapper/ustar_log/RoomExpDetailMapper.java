package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.RoomExpDetailData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RoomExpDetailMapper extends ShardingMapper {
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") RoomExpDetailData userExpDetailData);

    void update(@Param("tableSuffix") String tableSuffix, @Param("item") RoomExpDetailData userExpDetailData);

    RoomExpDetailData getByRoomId(@Param("tableSuffix") String tableSuffix, @Param("room_id") String roomId, @Param("dateStr") String dateStr);

    List<RoomExpDetailData> getAllIds(@Param("tableSuffixList") List<String> tableSuffixList,
                                      @Param("room_id") String roomId,
                                      @Param("startTime") int startTime,
                                      @Param("endTime") int endTime);

    List<RoomExpDetailData> getAllByDay(@Param("dateMonth") String dateMonth,
                                      @Param("dateStr") String dateStr);
}
