package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.DAUData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DAUMapper extends ShardingMapper {
    @Select("select id,user_id,fb_gender,channel,package_name,is_new,os,ctime,mtime from s_dau_${tableSuffix} where user_id=#{userId} and ctime >= #{startTime} and ctime < #{endTime}")
    DAUData getItem(@Param("tableSuffix")String tableSuffix, @Param("userId")String userId, @Param("startTime")long startTime, @Param("endTime")long endTime);

    @Insert("insert into s_dau_${tableSuffix} (user_id,fb_gender,channel,package_name,is_new,os,ctime,mtime) values (#{item.userId},#{item.fbGender},#{item.channel},#{item.packageName},#{item.isNew},#{item.os},#{item.ctime},#{item.mtime})")
    void insert(@Param("tableSuffix")String tableSuffix, @Param("item") DAUData onlineData);

    @Select({
            "<script>",
            "SELECT count(*) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT user_id from s_dau_${tableSuffix} ",
            "where user_id= #{uid} AND <![CDATA[ ctime >= #{startTime}]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Integer getSdauCount(@Param("suffixList") List<String> suffixList, @Param("uid") String uid, @Param("startTime")long startTime);

}
