package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.NearbyPostData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface NearbyPostMapper extends BaseMapper<NearbyPostData> {

    @Select("select id, moment_id, region, country_code, intervene_weight, exposure_weight, " +
            "case when #{scoreBoost} = 1 and #{currentTime} - ctime <= 2 * 3600 then score * 1.5 else score end as score, " +
            "like_score, comment_score, comment_user, ctime, mtime, decay_date, " +
            "case when country_code = #{countryCode} then 1 else 0 end as region_weight " +
            "from t_nearby_post " +
            "WHERE region=#{region} " +
            "and (#{queryCondition} = 1 and ctime >= #{queryTime} or #{queryCondition} = 0 and ctime < #{queryTime}) and status = 1 " +
            "order by region_weight desc, intervene_weight desc, exposure_weight desc, score desc limit #{size} offset #{start}")
    List<NearbyPostData> getNearbyPostByPage(@Param("countryCode") String countryCode, @Param("region") String region,
                                               @Param("currentTime") int currentTime,  @Param("scoreBoost") int scoreBoost,
                                               @Param("queryCondition") int queryCondition, @Param("queryTime") int queryTime,
                                               @Param("start") int start, @Param("size") int size);
}
