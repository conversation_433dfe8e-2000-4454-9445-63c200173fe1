package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.FamilyRequestData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface FamilyRequestMapper extends BaseMapper<FamilyRequestData> {

    @Select("SELECT family_id AS myKey,COUNT(1) AS count FROM `t_family_request`" +
            " WHERE status= 1 AND ctime >= #{start} GROUP BY family_id")
    List<CountData> familyNewAdd(@Param("start") Integer start);
}
