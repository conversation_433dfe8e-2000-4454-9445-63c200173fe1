package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.FamilyMemberData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;


public interface FamilyMemberMapper extends BaseMapper<FamilyMemberData> {

    @Update("UPDATE t_family_member SET devote = devote + #{devote} WHERE uid=#{uid} AND family_id=#{familyId}")
    void incrDevote(@Param("uid") String uid, @Param("familyId") int familyId, @Param("devote") int devote);
}
