package com.quhong.mysql.mapper.ustar;


import com.quhong.mysql.data.TnWhiteConfigData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface TnWhiteConfigMapper {


    @Select("select tnId from t_tn_white_config where tnId=#{tnId} limit 1")
    String queryItem(@Param("tnId") String tnId);

    @Insert({
            "<script>",
            "insert t_tn_white_config (uid,tnId,sType,cTime) values ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.uid},#{item.tnId},#{item.sType},#{item.cTime})",
            "</foreach>",
            "</script>"
    })
    void insertList(@Param("list") List<TnWhiteConfigData> list);


    @Select({
            "<script>",
            "SELECT tnId from t_tn_white_config ",
            "group by tnId ",
            "</script>"
    })
    List<String> getAllTnId();


    @Select("select * from t_tn_white_config order by id desc limit #{size} offset #{start}")
    List<TnWhiteConfigData> getDataByPage(@Param("start") int start, @Param("size") int size);

    @Select("select * from t_tn_white_config WHERE uid=#{uid} order by id desc limit #{size} offset #{start}")
    List<TnWhiteConfigData> getDataByPageByUid(@Param("uid") String uid, @Param("start") int start, @Param("size") int size);

    @Select("select * from t_tn_white_config WHERE tnId=#{tnId} order by id desc limit #{size} offset #{start}")
    List<TnWhiteConfigData> getDataByPageByTnId(@Param("tnId") String tnId, @Param("start") int start, @Param("size") int size);

    @Delete("delete from t_tn_white_config where tnId=#{tnId}")
    int deleteItem(@Param("tnId") String tnId);

    @Select({
            "<script>",
            "SELECT uid from t_tn_white_config ",
            "group by uid ",
            "</script>"
    })
    List<String> getAllUid();
}
