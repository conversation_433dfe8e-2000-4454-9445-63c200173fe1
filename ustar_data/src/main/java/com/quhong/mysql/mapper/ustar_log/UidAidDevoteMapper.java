package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.UidAidDevoteData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UidAidDevoteMapper extends ShardingMapper {

    @Insert("insert into t_uid_aid_devote_log_${tableSuffix} (roomId,uid,aid,beans,cTime) " +
            "values (#{item.roomId},#{item.uid},#{item.aid},#{item.beans},#{item.cTime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") UidAidDevoteData uidAidDevoteData);


    @Insert({
            "<script>",
            "insert into t_uid_aid_devote_log_${tableSuffix} (roomId,uid,aid,beans,cTime) values ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.roomId},#{item.uid},#{item.aid},#{item.beans},#{item.cTime})",
            "</foreach>",
            "</script>"
    })
    void insertList(@Param("tableSuffix") String tableSuffix, @Param("list") List<UidAidDevoteData> list);


    @Select({
            "<script>",
            "SELECT roomId as myKey,sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT roomId,sum(beans) as t_beans  from t_uid_aid_devote_log_${tableSuffix} ",
            "where <![CDATA[ cTime >= #{start} AND cTime < #{end} ]]> group by roomId  ",
            "</foreach>",
            " ) as stat ",
            "group by roomId ",
            "order by count desc ",
            "limit 100 ",
            "</script>"
    })
    List<CountData> getRoomRank (@Param("suffixList") List<String> suffixList, @Param("start") int start, @Param("end") int end);


    @Select({
            "<script>",
            "SELECT uid as myKey,sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid,sum(beans) as t_beans from t_uid_aid_devote_log_${tableSuffix} ",
            "where <![CDATA[ cTime >= #{start} AND cTime < #{end} ]]> group by uid ",
            "</foreach>",
            " ) as stat ",
            "group by uid ",
            "order by count desc ",
            "limit 100 ",
            "</script>"
    })
    List<CountData> getSendRank (@Param("suffixList") List<String> suffixList, @Param("start") int start, @Param("end") int end);


    @Select({
            "<script>",
            "SELECT aid as myKey,sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT aid,sum(beans) as t_beans from t_uid_aid_devote_log_${tableSuffix} ",
            "where <![CDATA[ cTime >= #{start} AND cTime < #{end} ]]> group by aid  ",
            "</foreach>",
            " ) as stat ",
            "group by aid ",
            "order by count desc ",
            "limit 100 ",
            "</script>"
    })
    List<CountData> getReceiveRank(@Param("suffixList") List<String> suffixList, @Param("start") int start, @Param("end") int end);


    @Select({
            "<script>",
            "SELECT uid as myKey,sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid,sum(beans) as t_beans from t_uid_aid_devote_log_${tableSuffix} ",
            "where roomId= #{roomId} and <![CDATA[ cTime >= #{start} AND cTime < #{end} ]]> group by uid ",
            "</foreach>",
            " ) as stat ",
            "group by uid ",
            "order by count desc ",
            "limit #{limit} ",
            "</script>"
    })
    List<CountData> getUserRankByRoom (@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                                       @Param("start") int start,@Param("end") int end, @Param("limit") int limit);


    @Select({
            "<script>",
            "SELECT sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT sum(beans) as t_beans from t_uid_aid_devote_log_${tableSuffix} ",
            "where roomId= #{roomId} AND uid= #{uid} AND <![CDATA[ cTime >= #{start} AND cTime < #{end} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Long getMyRoomDevoteByTime (@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                                @Param("uid") String uid,@Param("start") int start,@Param("end") int end);


    @Select({
            "<script>",
            "SELECT sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT sum(beans) as t_beans from t_uid_aid_devote_log_${tableSuffix} ",
            "where roomId= #{roomId} AND <![CDATA[ cTime >= #{start} AND cTime < #{end} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Long calculateRoomDevote (@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                                @Param("start") int start,@Param("end") int end);

}
