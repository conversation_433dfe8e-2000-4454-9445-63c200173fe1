package com.quhong.mysql.mapper.ustar;

import com.quhong.mysql.data.RoomManageData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface RoomManageMapper {
    RoomManageData getData(@Param("roomId") String roomId);
    void insert(@Param("item") RoomManageData manageData);
    void updateMute(@Param("item")RoomManageData manageData);
}
