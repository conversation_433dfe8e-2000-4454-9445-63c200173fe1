package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.LuckyNumData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface LuckyNumMapper extends ShardingMapper {

    @Insert("insert into t_lucky_num_record_${tableSuffix} (`room_id`,`uid`,`lucky_num`, `ctime`) values (#{item.roomId},#{item.uid},#{item.luckyNum},#{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") LuckyNumData luckyNumData);

    @Select({
            "<script>",
            "SELECT uid, lucky_num, ctime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, lucky_num, ctime FROM t_lucky_num_record_${tableSuffix} ",
            "WHERE room_id=#{roomId} and <![CDATA[ ctime >= #{startTime} and ctime <= #{endTime} ]]> ",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<LuckyNumData> getRecords( @Param("roomId") String roomId, @Param("start") int start,@Param("size") int size,
                                   @Param("suffixList") List<String> suffixList, @Param("startTime") int startTime, @Param("endTime") int endTime);


}
