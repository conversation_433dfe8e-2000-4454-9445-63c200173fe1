package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.ThirdDetectLogData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface ThirdDetectLogMapper {
    @Insert("insert into t_third_detect_log (req_id, content_type, content, content_md5, score," +
            " is_safe,result_data,ctime) " +
            "values (#{item.reqId},#{item.contentType},#{item.content},#{item.contentMd5},#{item.score}," +
            "#{item.isSafe},#{item.resultData},#{item.ctime}) on duplicate key update ctime = values(ctime)")
    void insert( @Param("item") ThirdDetectLogData data);

    @Select("select * from t_third_detect_log where ctime >= #{startTime} and ctime < #{endTime} and content_type= #{contentType}")
    List<ThirdDetectLogData> getThirdDetectLogData( @Param("startTime")int startTime, @Param("endTime")int endTime
            , @Param("contentType")int contentType);

    @Select("select * from t_third_detect_log where ctime >= #{startTime} and ctime < #{endTime}")
    List<ThirdDetectLogData> getAllThirdDetectLogData( @Param("startTime")int startTime, @Param("endTime")int endTime);

}
