package com.quhong.mysql.mapper.ustar;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.UserStatusData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/7
 */
public interface UserStatusMapper extends BaseMapper<UserStatusData> {

    @Insert({
            "<script>",
            "insert into t_user_status (`uid`, `online`, `in_room`) values ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.uid}, #{item.online}, #{item.inRoom})",
            "</foreach>",
            "on duplicate key update online = values(online), in_room = values(in_room)",
            "</script>"
    })
    void updateStatus(@Param("list") List<UserStatusData> list);

    @Delete("DELETE FROM `t_user_status` WHERE `online` <= #{limitTime}")
    void clearOldData(@Param("limitTime") Integer limitTime);
}
