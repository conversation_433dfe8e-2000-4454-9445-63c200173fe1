package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.RoomEventSupportRecordData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 */
public interface RoomEventSupportRecordMapper {

    @Insert("insert into t_room_event_support_record (uid,room_id,rewards,room_event_id,mtime,ctime) values (#{item.uid},#{item.roomId},#{item.rewards},#{item.roomEventId},#{item.mtime},#{item.ctime}) " +
            "on duplicate key update rewards = rewards + #{item.rewards}, mtime = #{item.mtime}")
    void insert(@Param("item") RoomEventSupportRecordData data);

    @Select("select a.*, b.name as eventName, b.start_time as startTime from t_room_event_support_record as a ,t_room_event as b where a.uid=#{uid} and a.ctime>start_time" +
            " and a.room_event_id=b.id")
    List<RoomEventSupportRecordData> getSupportRecord(@Param("uid") String uid,
                                                      @Param("start_time") Integer startTime);

}
