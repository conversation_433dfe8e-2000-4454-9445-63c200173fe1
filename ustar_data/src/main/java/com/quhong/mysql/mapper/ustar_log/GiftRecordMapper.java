package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.GiftRecordData;
import com.quhong.mysql.data.SendGiftTotalBeansData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GiftRecordMapper extends ShardingMapper {


    @Select({
            "<script>",
            "SELECT sum(t_beans) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT sum(gift_number * gift_price) as t_beans from t_gift_send_record_${tableSuffix} ",
            "where room_id= #{roomId} AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Long calculateRoomGiftDevote(@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                                 @Param("start") long start, @Param("end") long end);

    @Select({
            "<script>",
            "SELECT sum(total_num) as count from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT sum(gift_number) as total_num from t_gift_send_record_${tableSuffix} ",
            "where room_id= #{roomId} AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Integer getRoomSendGiftNum(@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                               @Param("start") long start, @Param("end") long end);

    @Select({
            "<script>",
            "SELECT from_uid as fromUid, sum(gift_number * gift_price) as totalBeans from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT from_uid, gift_number, gift_price from t_gift_send_record_${tableSuffix} ",
            "where room_id= #{roomId} AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            "</foreach>",
            " ) as stat GROUP BY stat.from_uid ORDER BY sum(stat.gift_number * stat.gift_price) desc limit #{offset},#{pageSize}",
            "</script>"
    })
    List<SendGiftTotalBeansData> getSendGiftRankList(@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                                                     @Param("start") long start, @Param("end") long end, @Param("offset") int offset, @Param("pageSize") int pageSize);


    @Insert("insert into t_gift_send_record_${tableSuffix} (date_num,gift_id,from_uid,to_uid,room_id,gift_name,gift_number,gift_price,earn_beans,gtype,send_type,os,version_code,channel,app_package_name,uuid_multi,ctime) " +
            "values (#{item.date_num},#{item.gift_id},#{item.from_uid},#{item.to_uid},#{item.room_id}" +
            ",#{item.gift_name},#{item.gift_number},#{item.gift_price},#{item.earn_beans},#{item.gtype},#{item.send_type}" +
            ",#{item.os},#{item.version_code},#{item.channel},#{item.app_package_name},#{item.uuid_multi},#{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") GiftRecordData giftRecordData);


    @Insert({
            "<script>",
            "insert into t_gift_send_record_${tableSuffix} (date_num,gift_id,from_uid,to_uid,room_id,gift_name,gift_number,gift_price,earn_beans,gtype,send_type,os,version_code,channel,app_package_name,uuid_multi,ctime) values ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.date_num},#{item.gift_id},#{item.from_uid},#{item.to_uid},#{item.room_id}",
            ",#{item.gift_name},#{item.gift_number},#{item.gift_price},#{item.earn_beans},#{item.gtype},#{item.send_type}",
            ",#{item.os},#{item.version_code},#{item.channel},#{item.app_package_name},#{item.uuid_multi},#{item.ctime})",
            "</foreach>",
            "</script>"
    })
    void insertList(@Param("tableSuffix") String tableSuffix, @Param("list") List<GiftRecordData> list);


    @Select({
            "<script>",
            "SELECT  from_uid as myKey, sum(gift_number) as count from t_gift_send_record_${tableSuffix} ",
            "where <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> ",
            " GROUP BY from_uid",
            "</script>"
    })
    List<CountData> selectUserGiftCountByHour(@Param("tableSuffix") String tableSuffix, @Param("start") long start, @Param("end") long end);

    @Select({
            "<script>",
            "SELECT count(1) from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT to_uid FROM t_gift_send_record_${tableSuffix} ",
            "where room_id= #{roomId} AND <![CDATA[ ctime >= #{start} AND ctime < #{end} ]]> group by to_uid",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    int calculateRoomGiftReceiverSum(@Param("suffixList") List<String> suffixList, @Param("roomId") String roomId,
                                     @Param("start") long start, @Param("end") long end);

    @Select("SELECT room_id as `myKey`,IFNULL(sum(gift_number * gift_price), 0) as `count` from t_gift_send_record_${tableSuffix} WHERE ctime>#{startTime} GROUP BY room_id")
    List<CountData> getRoomDevote(@Param("tableSuffix") String tableSuffix, @Param("startTime") long startTime);

    @Select("SELECT from_uid as `myKey`,IFNULL(sum(gift_number * gift_price), 0) as `count` from t_gift_send_record_${tableSuffix} WHERE ctime>#{startTime} and gift_id in (589, 590, 1012, 1013) GROUP BY from_uid order by count desc")
    List<CountData> getPlantTreeCount(@Param("tableSuffix") String tableSuffix, @Param("startTime") long startTime);

    @Select("SELECT from_uid as `myKey`,IFNULL(sum(gift_number * gift_price), 0) as `count` from t_gift_send_record_${tableSuffix} WHERE ctime>#{startTime} and gift_id in (594,1091) GROUP BY from_uid order by count desc")
    List<CountData> getSendBellCount(@Param("tableSuffix") String tableSuffix, @Param("startTime") long startTime);

    @Select("SELECT to_uid as `myKey`,IFNULL(sum(gift_number * gift_price), 0) as `count` from t_gift_send_record_${tableSuffix} WHERE ctime>#{startTime} and gift_id in (594,1091) GROUP BY to_uid order by count desc")
    List<CountData> getRecvBellCount(@Param("tableSuffix") String tableSuffix, @Param("startTime") long startTime);

    @Select("SELECT from_uid as `myKey`,IFNULL(sum(gift_number * gift_price), 0) as `count` from t_gift_send_record_${tableSuffix} WHERE room_id= #{roomId} and ctime>#{startTime} GROUP BY from_uid order by count desc")
    List<CountData> getRoomDevoteBySend (@Param("tableSuffix") String tableSuffix, @Param("startTime") long startTime, @Param("roomId") String roomId);

}
