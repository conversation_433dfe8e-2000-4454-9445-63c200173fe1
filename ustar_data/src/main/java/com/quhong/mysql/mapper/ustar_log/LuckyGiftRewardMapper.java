package com.quhong.mysql.mapper.ustar_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.dao.LuckyGiftRewardDao;
import com.quhong.mysql.data.LuckyGiftRewardData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface LuckyGiftRewardMapper extends BaseMapper<LuckyGiftRewardData> {

    @Select("SELECT uid,name,head,sum(reward) as reward FROM `t_lucky_gift_reward` WHERE ctime > #{startTime} GROUP BY uid ORDER BY reward desc LIMIT 50")
    List<LuckyGiftRewardDao.RankInfo> selectRankList(@Param("startTime") int startTime);
}
