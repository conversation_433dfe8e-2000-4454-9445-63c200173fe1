package com.quhong.mysql.mapper.ustar;

import com.quhong.mysql.data.SubscriptionData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SubscriptionMapper {

    /**
     * 新增数据
     *
     * @param data 订阅信息
     * @return 更新数
     */
    int insert(SubscriptionData data);

    /**
     *
     *
     * @param data svip信息
     * @return 更新数
     */
    int update(SubscriptionData data);

    /**
     * 批量把过期的数据设为过期
     *
     * @param uid 当前时间 秒
     * @return 更新记录数
     */
    SubscriptionData getData(@Param("userId") String uid);

    /**
     * 获取最先的orderId
     * @param orderId
     * @return
     */
    SubscriptionData getDataByOriginalOrderId(@Param("originalOrderId") String orderId);

    /**
     *
     * @param os
     * @param endTime 结束时间
     * @param verifyTime 验证时间
     * @return
     */
    List<SubscriptionData> getVerifyList(@Param("os")int os, @Param("endTime")long endTime, @Param("verifyTime")long verifyTime);

    List<SubscriptionData> getExpiredList(@Param("endTime")long endTime);
}
