package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.RegisterOrLoginLogData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface RegisterOrLoginLogMapper extends ShardingMapper {

    @Insert("insert into t_register_login_log_${tableSuffix} (uid,rid,third_uid,tn_id,tn_risk,register_type," +
            "login_type,login_status,login_status_desc,device_rid,device_uid,android_id,ios_key,idfa,distinct_id," +
            "ta_device_id,ctime,pkg_name,v_code,v_name,dynamic_channel,ip,ip_country,account,now_device_account_num," +
            "device_honor_uid,device_level_uid,fb_gender,msg,os,tn_extra_info,tn_use_cache,req_id,shu_mei_id,shu_mei_risk) " +
            "values (#{item.uid},#{item.rid},#{item.thirdUid},#{item.tnId},#{item.tnRisk},#{item.registerType}" +
            ",#{item.loginType},#{item.loginStatus},#{item.loginStatusDesc},#{item.deviceRid},#{item.deviceUid},#{item.androidId},#{item.iosKey},#{item.idfa},#{item.distinctId}" +
            ",#{item.taDeviceId},#{item.ctime},#{item.pkgName},#{item.vCode},#{item.vName},#{item.dynamicChannel},#{item.ip},#{item.ipCountry},#{item.account},#{item.nowDeviceAccountNum}" +
            ",#{item.deviceHonorUid},#{item.deviceLevelUid},#{item.fbGender},#{item.msg},#{item.os},#{item.tnExtraInfo},#{item.tnUseCache},#{item.reqId},#{item.shuMeiId},#{item.shuMeiRisk})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") RegisterOrLoginLogData registerOrLoginLogData);




}
