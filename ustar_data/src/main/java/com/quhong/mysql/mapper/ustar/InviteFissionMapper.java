package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.HotSearchListData;
import com.quhong.mysql.data.InviteFissionData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface InviteFissionMapper extends BaseMapper<InviteFissionData> {


    @Select("SELECT uid AS searchKey,COUNT(1) AS totalP,sum(`total_reward`) AS searchNumSum FROM `t_invite_fission`" +
            " WHERE ctime>= #{startTime} GROUP BY uid ORDER BY totalP DESC,searchNumSum DESC LIMIT 40")
    List<HotSearchListData> getRankListData(@Param("startTime") Integer startTime);

    @Select("SELECT uid AS searchKey,COUNT(1) AS totalP,sum(`total_reward`) AS searchNumSum FROM `t_invite_fission` WHERE uid= #{uid} limit 1")
    HotSearchListData getMyTotalData(@Param("uid") String uid);

}
