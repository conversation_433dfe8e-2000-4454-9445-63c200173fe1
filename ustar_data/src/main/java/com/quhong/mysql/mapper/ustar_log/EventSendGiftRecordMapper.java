package com.quhong.mysql.mapper.ustar_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.EventGiftStatistics;
import com.quhong.mysql.data.EventSendGiftRecordData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2022/12/9
 */
public interface EventSendGiftRecordMapper extends BaseMapper<EventSendGiftRecordData> {

    @Select("SELECT count(1) as totalNum, COALESCE(sum(gift_total_price), 0) as totalPrice from t_event_send_gift_record where room_id = #{roomId} AND event_id = #{eventId}")
    EventGiftStatistics getEventGiftStatistics(@Param("roomId") String roomId,@Param("eventId") Integer eventId);

}
