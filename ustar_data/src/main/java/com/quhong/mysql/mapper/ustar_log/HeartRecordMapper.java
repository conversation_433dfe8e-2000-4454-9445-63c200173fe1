package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.HeartRecordData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

public interface HeartRecordMapper extends ShardingMapper {

    @Insert("insert into t_heart_record_${tableSuffix} (uid, changed, g_balance, r_balance, room_id, aid, title, remark, c_time) " +
            "values (#{item.uid}, #{item.changed}, #{item.gBalance}, #{item.rBalance}, #{item.roomId}, #{item.aid}, #{item.title}, #{item.remark}, #{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") HeartRecordData heartRecordData);

}
