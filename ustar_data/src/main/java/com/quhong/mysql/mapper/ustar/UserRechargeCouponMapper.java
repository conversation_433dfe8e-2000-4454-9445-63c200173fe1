package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.UserRechargeCouponData;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Component
public interface UserRechargeCouponMapper extends BaseMapper<UserRechargeCouponData> {

    @Select("SELECT DISTINCT source FROM `t_user_recharge_coupon` GROUP BY source")
    List<String> selectSourceList();
}
