package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.CountData;
import com.quhong.mysql.data.RoomUserOnlineData;
import com.quhong.mysql.mapper.ShardingMapper;
import com.quhong.vo.OnRoomTimeVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Repository
public interface RoomUserOnlineMapper extends ShardingMapper {
    @Select("select id,room_id,uid,fb_gender,os,is_new,online_time,stat_date,ctime from s_room_user_online_${tableSuffix} where " +
            "room_id=#{roomId} and uid=#{uid} and stat_Date=#{statDate}")
    RoomUserOnlineData getItem(@Param("tableSuffix") String tableSuffix, @Param("roomId") String roomId,
                               @Param("uid") String uid, @Param("statDate") String statDate);

    @Insert("insert into s_room_user_online_${tableSuffix} (room_id,uid,fb_gender,os,is_new,online_time,stat_date,ctime) " +
            "values (#{item.roomId},#{item.uid},#{item.fbGender},#{item.os},#{item.isNew},#{item.onlineTime},#{item.statDate},#{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") RoomUserOnlineData roomUserOnlineData);

    @Update("update s_room_user_online_${tableSuffix} set online_time=#{item.onlineTime} where id=#{item.id}")
    void update(@Param("tableSuffix") String tableSuffix, @Param("item") RoomUserOnlineData roomUserOnlineData);

    @Insert({
            "<script>",
            "insert into s_room_user_online_${tableSuffix} (room_id,uid,fb_gender,os,is_new,online_time,stat_date,ctime) values ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.roomId},#{item.uid},#{item.fbGender},#{item.os},#{item.isNew},#{item.onlineTime},#{item.statDate},#{item.ctime})",
            "</foreach>",
            "</script>"
    })
    void insertList(@Param("tableSuffix") String tableSuffix, @Param("list") List<RoomUserOnlineData> list);

    @Select({
            "<script>",
            "SELECT sum(online_time) from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT sum(online_time) as online_time from s_room_user_online_${tableSuffix} where uid=#{uid}",
            "</foreach>",
            " ) as stat",
            "</script>"
    })
    Integer getTotalOnlineTime(@Param("suffixList") List<String> suffixList, @Param("uid") String uid);

    @Select({
            "<script>",
            "<foreach collection='tableSuffixList' item='tableSuffix' separator='UNION ALL'>",
                "SELECT uid, sum(online_time) as sum from s_room_user_online_${tableSuffix} where ",
                "<![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> and uid in ",
                "<foreach item='item' collection='uidSet' open='(' separator=',' close=')'>",
                    "#{item}",
                "</foreach>",
                "GROUP BY uid",
            "</foreach>",
            "</script>"
    })
    List<OnRoomTimeVO> userOnRoomTimeByUidSet(@Param("tableSuffixList") List<String> tableSuffixList,
                                              @Param("startTime") Integer startTime,
                                              @Param("endTime") Integer endTime,
                                              @Param("uidSet") Set<String> uidSet);



    @Select({
            "<script>",
            "SELECT room_id from ( ",
            "<foreach collection='tableSuffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, room_id, online_time from s_room_user_online_${tableSuffix} where ",
            "<![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> and uid = #{uid} ",
            "</foreach>",
            " ) as stat GROUP BY room_id ORDER BY sum(online_time) LIMIT 1",
            "</script>"
    })
    String getUserLongestStayRoomId(@Param("tableSuffixList") List<String> tableSuffixList,
                                    @Param("uid") String uid,
                                    @Param("startTime") Integer startTime,
                                    @Param("endTime") Integer endTime);

    @Select({
            "<script>",
            "SELECT sum(online_time) from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT sum(online_time) as online_time from s_room_user_online_${tableSuffix} where uid in ",
                "<foreach item='item' collection='uidSet' open='(' separator=',' close=')'>",
                    "#{item}",
                "</foreach>",
            "</foreach>",
            " ) as stat",
            "</script>"
    })
    Integer getTotalOnlineTimeBySet(@Param("suffixList") List<String> suffixList, @Param("uidSet") Set<String> uidSet);

    @Select({
            "<script>",
            "SELECT SUM(retT) as totalCount from ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT SUM(online_time) AS retT from s_room_user_online_${tableSuffix} ",
            "WHERE <![CDATA[ ctime >= #{startTime} AND ctime <= #{endTime} ]]> AND uid in ",
            "<foreach collection = 'uidList' open = '(' item = 'item' separator = ',' close = ')'>",
            "#{item}",
            "</foreach> ",
            "</foreach>",
            " ) as stat ",
            "</script>"
    })
    Long userTotalOnRoomTimeByUidSet(@Param("suffixList") List<String> suffixList,
                                     @Param("startTime") Integer startTime,
                                     @Param("endTime") Integer endTime,
                                     @Param("uidList") Collection<String> uidList);
}
