package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.GuardedData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/19
 */
public interface GuardedMapper {

    @Insert("insert into ${tableName} (uid,guardian,guard_value,mtime) values (#{item.uid},#{item.guardian},#{item.guardValue},#{item.mtime}) on duplicate key update guard_value = guard_value + #{item.guardValue}, mtime = #{item.mtime}")
    void insert(@Param("tableName") String tableName, @Param("item") GuardedData data);

    @Update("update ${tableName} set guard_value = guard_value + #{item.guardValue}, mtime = #{item.mtime} where id = #{item.id}")
    void incGuardValue(@Param("tableName") String tableName, @Param("item") GuardedData data);

    @Select("select id,uid,guardian,guard_value,mtime from ${tableName} where uid=#{uid} and guardian=#{guardian}")
    GuardedData selectByUidAndGuardian(@Param("tableName") String tableName, @Param("uid") String uid, @Param("guardian") String guardian);

    @Select("select id,uid,guardian,guard_value,mtime from ${tableName} where uid=#{uid} order by guard_value desc, mtime asc limit #{length}")
    List<GuardedData> getGuarderRankList(@Param("tableName") String tableName, @Param("uid") String uid, @Param("length") int length);

    @Select({
            "<script>",
            "select id,uid,guardian,guard_value,mtime from ${tableName} where guardian=#{guardian} and uid in ",
            "<foreach item='item' collection='uidList' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"
    })
    List<GuardedData> selectByGuardian(@Param("tableName") String tableName, @Param("guardian") String guardian, @Param("uidList") List<String> uidList);

    @Insert({
            "<script>",
            " insert into ${tableName} (`uid`,`guardian`,`guard_value`,`mtime`) values ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.uid},#{item.guardian},#{item.guardValue},#{item.mtime})",
            "</foreach>",
            "</script>"
    })
    void batchInsert(@Param("tableName") String tableName, @Param("list") List<GuardedData> insertList);

    @Update({
            "<script>",
            "update ${tableName}" ,
                    "    <trim prefix='set' suffixOverrides=','>" ,
                    "        <trim prefix='guard_value = case' suffix='end,'>" ,
                    "            <foreach collection='list' item='i' index='index'>" ,
                    "                when id= #{i.id} then guard_value + #{i.guardValue}" ,
                    "            </foreach>" ,
                    "        </trim>" ,
                    "        <trim prefix='mtime = case' suffix='end,'>" ,
                    "            <foreach collection='list' item='i' index='index'>" ,
                    "                when id= #{i.id} then #{i.mtime}" ,
                    "            </foreach>" ,
                    "        </trim>" ,
                    "    </trim>" ,
                    "    where id in" ,
                    "    <foreach collection='list' item='item' open='(' close=')' separator=','>" ,
                    "        #{item.id}" ,
                    "    </foreach>" ,
            "</script>"
    })
    void batchIncGuardValue(@Param("tableName") String tableName, @Param("list") List<GuardedData> updateList);
}
