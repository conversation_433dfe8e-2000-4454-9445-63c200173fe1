package com.quhong.mysql.mapper.ustar_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.UserRechargeAmountData;
import com.quhong.mysql.data.RechargeDailyInfoData;
import com.quhong.mysql.data.UserTotalRechargeData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public interface RechargeDailyInfoMapper extends BaseMapper<RechargeDailyInfoData> {

    BigDecimal selectUserRechargeAmount(@Param("uid") String uid, @Param("rechargeTime") Integer rechargeTime);

    List<String> selectRechargeUserUid(UserRechargeAmountData userRechargeAmountData);

    int getRechargeTotalNum(@Param("uid") String uid);

    UserTotalRechargeData getUserTotalRecharge(@Param("uid") String uid);

    List<RechargeDailyInfoData> getUserRechargeInfoList(@Param("uid") String uid, @Param("startTime") int startTime, @Param("endTime") int endTime);

    int getHistoryRechargeTotalNum(@Param("uid") String uid, @Param("endTime") Integer endTime);

    Set<String> selectUserRechargeByDay(@Param("startTime") Integer startTime, @Param("endTime") Integer endTime, @Param("rechargeAmount") BigDecimal rechargeAmount);

    int getUserTotalRechargeBean(@Param("uid") String uid, @Param("startTime") int startTime, @Param("endTime") int endTime);

    int getUserTotalRechargeBeanByUidList (@Param("aidSet") List<String> aidSet,@Param("startTime") int startTime, @Param("endTime") int endTime);

    List<RechargeDailyInfoData> getAllRechargeInfoList( @Param("startTime") int startTime);

//    @Select({
//            "<script>"+
//                    "select  " +
//                    "SUM(CASE WHEN recharge_time >= #{last30DayTime} THEN recharge_money ELSE 0 END)  AS last30Day, " +
//                    "SUM(CASE WHEN <![CDATA[ recharge_time >= #{last60DayTime} AND recharge_time<= #{last30DayTime} ]]>THEN recharge_money ELSE 0 END) AS bt30DayTo60Day, " +
//                    "SUM(CASE WHEN <![CDATA[ recharge_time >= #{last90DayTime} AND recharge_time<= #{last60DayTime} ]]> THEN recharge_money ELSE 0 END) AS bt60DayTo90Day, " +
//                    "SUM(CASE WHEN <![CDATA[ recharge_time >= #{last120DayTime} AND recharge_time<= #{last90DayTime} ]]> THEN recharge_money ELSE 0 END) AS bt60DayTo90Day, " +
//                    "SUM(CASE WHEN <![CDATA[ recharge_time >= #{last150DayTime} AND recharge_time<= #{last120DayTime} ]]> THEN recharge_money ELSE 0 END) AS bt60DayTo90Day, " +
//                    "SUM(CASE WHEN <![CDATA[ recharge_time >= #{last180DayTime} AND recharge_time<= #{last150DayTime} ]]> THEN recharge_money ELSE 0 END) AS bt60DayTo90Day, " +
//                    "from t_recharge_daily_info WHERE uid = #{searchUid}"+
//                    "</script>"
//    })
//    List<ss> getStaffBindUserSummaryData(@Param("uid") String uid,
//                                         @Param("last30DayTime") int last30DayTime,
//                                         @Param("last60DayTime") int last60DayTime,
//                                         @Param("last90DayTime") int last90DayTime,
//                                         @Param("last120DayTime") int last120DayTime,
//                                         @Param("last150DayTime") int last150DayTime,
//                                         @Param("last180DayTime") int last180DayTime);

}
