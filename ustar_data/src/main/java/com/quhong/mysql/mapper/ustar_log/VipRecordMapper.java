package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.VipRecordData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface VipRecordMapper extends ShardingMapper {

    @Insert("insert into t_vip_record_${tableSuffix} (`uid`,`vip_level`, `vip_day`, `record_type`, `ctime`, `mtime`) values (#{item.uid},#{item.vipLevel},#{item.vipDay},#{item.recordType},#{item.ctime},#{item.mtime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") VipRecordData vipRecordData);

    @Select({
            "<script>",
            "SELECT uid, vip_level, vip_day, record_type, ctime, mtime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, vip_level, vip_day, record_type, ctime, mtime FROM t_vip_record_${tableSuffix} ",
            "WHERE uid=#{uid} ",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<VipRecordData> getRecords( @Param("uid") String uid, @Param("start") int start,@Param("size") int size, @Param("suffixList") List<String> suffixList);


}
