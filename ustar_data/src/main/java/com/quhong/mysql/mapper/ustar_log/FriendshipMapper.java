package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.FriendshipData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/27
 */
public interface FriendshipMapper {

    @Insert("insert into ${tableName} (uid,aid,source,ctime) values (#{item.uid},#{item.aid},#{item.source},#{item.ctime}) on duplicate key update ctime = values(ctime)")
    void insert(@Param("tableName") String tableName, @Param("item") FriendshipData data);

    @Select("select id,uid,aid,source,ctime from ${tableName} where uid=#{uid} and aid=#{aid}")
    FriendshipData getFriendData(@Param("tableName") String tableName, @Param("uid") String uid, @Param("aid") String aid);

    @Select("select id,uid,aid,source,ctime from ${tableName} where uid=#{uid}")
    List<FriendshipData> getFriendList(@Param("tableName") String tableName, @Param("uid") String uid);

    @Select("select aid from ${tableName} where uid=#{uid}")
    Set<String> getFriendAidList(@Param("tableName") String tableName, @Param("uid") String uid);

    @Delete("delete from ${tableName} where uid=#{uid} and aid=#{aid}")
    int deleteFriend(@Param("tableName") String tableName, @Param("uid") String uid, @Param("aid") String aid);
}
