package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.HotSearchListData;
import com.quhong.mysql.data.HotSearchHistoryData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/27
 */
public interface HotSearchHistoryMapper extends BaseMapper<HotSearchHistoryData> {
    @Insert("insert into t_hot_search_history (uid,search_key,search_type,search_num,mtime) " +
            "values (#{item.uid},#{item.searchKey},#{item.searchType},#{item.searchNum},#{item.mtime}) " +
            "on duplicate key update search_num = search_num + #{item.searchNum},mtime = #{item.mtime}")
    void insertOrUpdate(@Param("item") HotSearchHistoryData data);

    @Update("update t_hot_search_history set search_num = search_num + #{item.searchNum},mtime = #{item.mtime} " +
            "where uid = #{item.uid} and search_key = #{item.searchKey}")
    int incNum(@Param("item") HotSearchHistoryData data);


    // SELECT search_key AS searchKey,COUNT(1) AS totalP,sum(`search_num`) AS searchNumSum FROM `t_hot_search_history` WHERE search_type= #{searchType} AND mtime >= #{start} GROUP BY search_key ORDER BY totalP,searchNumSum DESC LIMIT 30
    @Select("SELECT search_key AS searchKey,COUNT(1) AS totalP,sum(`search_num`) AS searchNumSum FROM `t_hot_search_history`" +
            " WHERE search_type= #{searchType} GROUP BY search_key ORDER BY totalP DESC,searchNumSum DESC LIMIT 30")
    List<HotSearchListData> getHotSearchListData(@Param("searchType") Integer searchType, @Param("start") Integer start);
}
