package com.quhong.mysql.mapper.ustar_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.RoomAllowanceLogData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *
 */
public interface RoomAllowanceLogMapper extends BaseMapper<RoomAllowanceLogData> {

    @Insert("<script>" +
            "INSERT INTO t_room_allowance_log (day, room_id, points, ratio,award,status,ctime) VALUES" +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.day}, #{item.roomId}, #{item.points}, #{item.ratio}, #{item.award}, #{item.status}, #{item.ctime})" +
            "</foreach>" +
            "</script>")
    void batchInsert(List<RoomAllowanceLogData> list);


    @Select("SELECT sum(`award`) AS searchNumSum FROM `t_room_allowance_log`" +
            " WHERE room_id= #{roomId} AND status= #{status}")
    Integer getSumRoomAllowance(@Param("roomId") String roomId, @Param("status") Integer status);


//    @Update("<script>" +
//            "UPDATE t_vote_option set votes_num = votes_num + 1 WHERE vote_id = #{voteId} AND id in (" +
//            "<foreach collection='optionList' item='id' separator=','> #{id} </foreach> )" +
//            "</script>")
//    void incOptionVotesNum(@Param("voteId") int voteId, @Param("optionList") List<Integer> optionList);
}
