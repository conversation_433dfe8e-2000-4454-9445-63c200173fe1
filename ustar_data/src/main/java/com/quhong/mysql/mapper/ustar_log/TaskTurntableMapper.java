package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.TaskTurntableData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface TaskTurntableMapper extends ShardingMapper {

    @Insert("insert into t_task_turntable_${tableSuffix} (`uid`,`draw_key`, `turntable_type`, `amount`, `ctime`) values (#{item.uid},#{item.drawKey},#{item.turntableType},#{item.amount},#{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") TaskTurntableData taskTurntableData);

    @Select({
            "<script>",
            "SELECT uid, draw_key, turntable_type, amount, ctime FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT uid, draw_key, turntable_type, amount, ctime FROM t_task_turntable_${tableSuffix} ",
            "WHERE uid=#{uid} and turntable_type=#{turntableType} and <![CDATA[ ctime >= #{startTime} and ctime <= #{endTime} ]]> ",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<TaskTurntableData> getRecords( @Param("uid") String uid, @Param("start") int start,@Param("size") int size,
                                   @Param("suffixList") List<String> suffixList, @Param("startTime") int startTime, @Param("endTime") int endTime, @Param("turntableType") String turntableType);


}
