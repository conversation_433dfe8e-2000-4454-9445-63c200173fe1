package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.MoneyDetail;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/9
 */
public interface RechargeMoneyDetailMapper {

    @Insert("insert into t_recharge_money_detail (`id`,`uid`,`changed`,`balance`,`title`,`desc`,`atype`,`mtime`,`ctime`) values (#{item.id},#{item.uid},#{item.changed},#{item.balance},#{item.title},#{item.desc},#{item.atype},#{item.mtime},#{item.ctime})")
    void insert(@Param("item") MoneyDetail moneyDetail);

    @Select("select `id`,`uid`,`changed`,`balance`,`title`,`desc`,`atype`,`mtime`,`ctime` from t_recharge_money_detail where uid=#{uid} order by mtime desc limit #{size} offset #{start}")
    List<MoneyDetail> selectPage(@Param("uid") String uid, @Param("start") int start, @Param("size") int size);

    @Select("select IFNULL(sum(`changed`),0) from t_recharge_money_detail where uid=#{uid} and mtime >= #{startTime} and mtime <= #{endTime}")
    int getTotalRechargeBeans(@Param("uid") String uid, @Param("startTime") int startTime, @Param("endTime") int endTime);
}
