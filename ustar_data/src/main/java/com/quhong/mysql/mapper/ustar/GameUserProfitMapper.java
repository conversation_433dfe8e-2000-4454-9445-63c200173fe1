package com.quhong.mysql.mapper.ustar;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.FruitGameTotalData;
import com.quhong.mysql.data.GameUserProfitData;
import com.quhong.mysql.data.QuestionData;
import com.quhong.mysql.data.SendRecvFirstData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/8/27
 */
public interface GameUserProfitMapper extends BaseMapper<GameUserProfitData> {
    @Insert("insert into t_game_user_profit (uid,profit_num,loss_num,profit_count,loss_count,game_type,ctime,mtime) " +
            "values (#{item.uid},#{item.profitNum},#{item.lossNum},#{item.profitCount},#{item.lossCount},#{item.gameType},#{item.ctime},#{item.mtime}) " +
            "on duplicate key update profit_num = profit_num + #{item.profitNum},loss_num = loss_num + #{item.lossNum}," +
            "profit_count = profit_count + #{item.profitCount},loss_count = loss_count + #{item.lossCount}, mtime = #{item.mtime}")
    void insertOrUpdate(@Param("item") GameUserProfitData data);

    @Update("update t_game_user_profit set profit_num = profit_num + #{item.profitNum},loss_num = loss_num + #{item.lossNum}," +
            "profit_count = profit_count + #{item.profitCount},loss_count = loss_count + #{item.lossCount}, mtime = #{item.mtime} " +
            "where uid = #{item.uid} and game_type = #{item.gameType}")
    int incNum(@Param("item") GameUserProfitData data);

    @Select({
            "<script>" +
                    "select SUM(profit_num) AS profitTotalNum,SUM(loss_num) AS lossTotalNum from t_game_user_profit " +
                    "where game_type=1 " +
                    "<if test='uidSet != null and uidSet.size() > 0'>" +
                    " and uid in " +
                    "<foreach collection = 'uidSet' open = '(' item = 'item' separator = ',' close = ')'>" +
                    "#{item}" +
                    "</foreach>" +
                    "</if>" +
                    "</script>"
    })
    FruitGameTotalData getFruitGameTotalByUidSet(@Param("uidSet") Set<String> uidSet);
}
