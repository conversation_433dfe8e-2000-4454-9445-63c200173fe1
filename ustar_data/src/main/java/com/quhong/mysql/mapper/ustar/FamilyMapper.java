package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.FamilyData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


public interface FamilyMapper extends BaseMapper<FamilyData> {

    @Update("UPDATE t_family SET devote = devote + #{devote} WHERE id=#{familyId}")
    void incrDevote(@Param("familyId") int familyId, @Param("devote") int devote);
}
