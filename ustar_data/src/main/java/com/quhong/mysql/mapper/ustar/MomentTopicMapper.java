package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.MomentTopicData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;


public interface MomentTopicMapper extends BaseMapper<MomentTopicData> {

    @Update("update t_moment_topic set followers = followers + #{add}, mtime = #{mtime} " +
            "where id = #{id} and followers>=0")
    int incFollowers(@Param("id") int id, @Param("add") int followers, @Param("mtime") int mtime);

    @Update("update t_moment_topic set moment_num = moment_num + #{momentNum}, mtime = #{mtime} " +
            "where id = #{id} and moment_num>=0")
    int incMomentNum(@Param("id") int id, @Param("momentNum") int momentNum, @Param("mtime") int mtime);

    @Update("update t_moment_topic set gift_beans = gift_beans + #{giftBeans}, mtime = #{mtime} " +
            "where id = #{id}  and gift_beans>=0")
    int incGiftBeans(@Param("id") int id, @Param("giftBeans") int giftBeans, @Param("mtime") int mtime);

}
