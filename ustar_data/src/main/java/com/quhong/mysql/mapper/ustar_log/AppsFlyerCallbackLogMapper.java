package com.quhong.mysql.mapper.ustar_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.AppsFlyerCallbackLogData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/17
 */
public interface AppsFlyerCallbackLogMapper extends BaseMapper<AppsFlyerCallbackLogData> {

    @Select({
            "<script>",
            "select * from t_apps_flyer_callback_log",
            "where <![CDATA[ ctime >= #{startTime} AND ctime < #{endTime} ]]> ",
            "order by id desc limit #{pageSize} offset #{start}",
            "</script>"
    })
    List<AppsFlyerCallbackLogData> findList(@Param("startTime") int startTime, @Param("endTime") int endTime, @Param("start") int start, @Param("pageSize") int pageSize);
}
