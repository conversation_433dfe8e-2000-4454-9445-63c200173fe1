package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.RoomLevelData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;


public interface RoomLevelMapper extends BaseMapper<RoomLevelData> {

    @Insert("insert into t_room_level (room_id,exp,level,ctime,mtime) " +
            "values (#{item.roomId},#{item.exp},#{item.level},#{item.ctime},#{item.mtime})")
    void insertMy( @Param("item") RoomLevelData roomLevelData);


}
