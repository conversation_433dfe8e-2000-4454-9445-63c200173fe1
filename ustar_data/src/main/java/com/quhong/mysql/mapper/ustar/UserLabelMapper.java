package com.quhong.mysql.mapper.ustar;

import com.quhong.mysql.data.LabelConfigData;
import com.quhong.mysql.data.UserLabelData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface UserLabelMapper {

    /**
     * 获取用户兴趣爱好标签
     */
    @Select("SELECT ul.uid,lc.id as labelId,lc.label_name,lc.label_arname FROM t_user_label ul JOIN t_label_config lc ON ul.label_id=lc.id WHERE ul.uid=#{uid}")
    List<UserLabelData> getUserLabel(@Param("uid") String uid);

    /**
     * 获取所有兴趣爱好标签配置
     */
    @Select("SELECT cf.id AS labelId, cf.label_name AS labelName, cf.label_arname AS labelArname, " +
            "ct.type_icon AS typeIcon, ct.type_name AS typeName, ct.type_arname AS typeArname, " +
            "ct.type_color AS typeColor, ct.id AS labelType ,ct.type_order AS typeOrder, cf.label_order AS labelOrder " +
            "FROM t_label_config cf JOIN t_label_category ct ON cf.label_type=ct.id ORDER BY typeOrder, labelOrder")
    List<LabelConfigData> getAllLabelConfig();


    /**
     * 老版本获取用户兴趣爱好标签
     */
    @Select("SELECT id AS labelId, label_name as labelName, label_arname AS labelArname FROM t_label_config WHERE status=1 ORDER BY ctime DESC")
    List<LabelConfigData> getOldAllLabelConfig();
}
