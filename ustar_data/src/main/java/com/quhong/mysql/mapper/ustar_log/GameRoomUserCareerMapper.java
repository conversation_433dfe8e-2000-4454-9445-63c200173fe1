package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.GameRoomUserCareerData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 *
 */
public interface GameRoomUserCareerMapper {

    @Insert("insert into ${tableName} (uid,gift_beans,join_num,win_num,mtime,ctime) values (#{item.uid},#{item.giftBeans},#{item.joinNum},#{item.winNum},#{item.mtime},#{item.ctime}) " +
            "on duplicate key update gift_beans = gift_beans + #{item.giftBeans},join_num = join_num + #{item.joinNum}," +
            "win_num = win_num + #{item.winNum}, mtime = #{item.mtime}")
    void insert(@Param("tableName") String tableName, @Param("item") GameRoomUserCareerData data);

    @Update("update ${tableName} set gift_beans = gift_beans + #{item.giftBeans}," +
            "join_num = join_num + #{item.joinNum},win_num = win_num + #{item.winNum}, " +
            "mtime = #{item.mtime} where uid = #{item.uid}")
    int incNum(@Param("tableName") String tableName, @Param("item") GameRoomUserCareerData data);


    @Select("select * from ${tableName} where uid=#{uid}")
    GameRoomUserCareerData getCareerDataByUid(@Param("tableName") String tableName, @Param("uid") String uid);

}
