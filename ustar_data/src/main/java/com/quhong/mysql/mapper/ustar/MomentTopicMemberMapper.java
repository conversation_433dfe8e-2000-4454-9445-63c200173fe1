package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.UserFollowTopicJoinData;
import com.quhong.mysql.data.MomentTopicMemberData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface MomentTopicMemberMapper extends BaseMapper<MomentTopicMemberData> {

    @Select({
            "<script>" +
                    "select a.uid as uid, a.role as role, b.rid as topicRid," +
                    " b.name as name, b.head as head, b.announce as announce, b.followers as followers ," +
                    " b.moment_num as momentNum, b.gift_beans as giftBeans " +
                    "from t_moment_topic_member as a left join t_moment_topic as b on a.topic_id = b.id " +
                    "where a.uid = #{uid} and <![CDATA[ a.role in (1, 2, 3) ]]> and b.status = 1 " +
                    "order by a.role asc, a.ctime desc " +
                    "limit #{start}, #{pageSize}" +
                    "</script>"
    })
    List<UserFollowTopicJoinData> getUserTopicJoinData(@Param("uid") String uid, @Param("start") int start, @Param("pageSize") int pageSize);

}
