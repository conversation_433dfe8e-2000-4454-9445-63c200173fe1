package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.ShuMeiDeviceAccountData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;


public interface ShuMeiDeviceAccountMapper extends BaseMapper<ShuMeiDeviceAccountData> {

    @Insert("insert into t_shu_mei_device_account (shu_mei_id,shu_mei_text,tn_id,tn_risk" +
            ",uid,ctime,mtime) values " +
            "(#{item.shuMeiId},#{item.shuMeiText},#{item.tnId},#{item.tnRisk}" +
            ",#{item.uid},#{item.ctime},#{item.mtime}) " +
            "on duplicate key update shu_mei_text = #{item.shuMeiText}," +
            " tn_risk = #{item.tnRisk},uid = #{item.uid},mtime = #{item.mtime}")
    void insertOne(@Param("item") ShuMeiDeviceAccountData data);


    @Insert("insert into t_shu_mei_device_account (shu_mei_id,shu_mei_text,tn_id,tn_risk" +
            ",uid,ctime,mtime) values " +
            "(#{item.shuMeiId},#{item.shuMeiText},#{item.tnId},#{item.tnRisk}" +
            ",#{item.uid},#{item.ctime},#{item.mtime}) ")
    void onlyInsertOne(@Param("item") ShuMeiDeviceAccountData data);
}
