package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.RiskDeviceData;
import com.quhong.mysql.data.RiskUserData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;


public interface RiskDeviceMapper extends BaseMapper<RiskDeviceData> {

    @Insert("insert into t_risk_device (key_id,status,key_type,score,ctime,mtime) values " +
            "(#{item.keyId},#{item.status},#{item.keyType},#{item.score},#{item.ctime},#{item.mtime}) " +
            "on duplicate key update score = score + #{item.score}, mtime = #{item.mtime}")
    void insertOrUpdate( @Param("item") RiskDeviceData data);

}
