package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.GatheringFeeTimesData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
public interface GatheringFeeTimesMapper extends BaseMapper<GatheringFeeTimesData> {
    @Update("update t_gathering_fee_times set num = num + #{changeNum}, mtime = #{mtime} where uid = #{uid}")
    int changeFeeTimes(@Param("uid") String uid, @Param("changeNum") int changeNum, @Param("mtime") int mtime);
}
