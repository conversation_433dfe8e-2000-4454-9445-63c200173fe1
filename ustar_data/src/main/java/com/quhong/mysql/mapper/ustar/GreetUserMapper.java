package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.GreetUserData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface GreetUserMapper extends BaseMapper<GreetUserData> {
    @Select("SELECT *, " +
            "(" +
            "  CASE WHEN country_code = #{countryCode} THEN 100 " +
            "       WHEN region = #{region} THEN 60 ELSE 0 END * #{regionWeight} + " +
            "  CASE WHEN online_duration < 300 THEN 100 " +
            "       WHEN online_duration < 1800 THEN 70 " +
            "       WHEN online_duration < 3600 THEN 50 ELSE 10 END * #{onlineWeight} + " +
            "  CASE WHEN interest_count >= 5 THEN 100 " +
            "       WHEN interest_count >= 3 THEN 70 " +
            "       WHEN interest_count >= 1 THEN 50 ELSE 0 END * #{interestWeight} + " +
            "  CASE WHEN reply_rate >= 0.9 THEN 100 WHEN reply_rate >= 0.7 THEN 80 " +
            "       WHEN reply_rate >= 0.6 THEN 70 WHEN reply_rate >= 0.5 THEN 60 " +
            "       WHEN reply_rate >= 0.4 THEN 50 WHEN reply_rate >= 0.3 THEN 40 " +
            "       WHEN reply_rate >= 0.2 THEN 30 WHEN reply_rate >= 0.1 THEN 20 ELSE 10 END * #{replyWeight} + " +
            "  CASE WHEN profile_process >= 0.8 THEN 100 WHEN profile_process >= 0.5 THEN 70 " +
            "       WHEN profile_process >= 0.3 THEN 50 WHEN profile_process >= 0.1 THEN 30 ELSE 10 END * #{profileWeight}" +
            ") AS score " +
            "FROM (" +
            "  SELECT *, #{currentTime} - online_time AS online_duration, BIT_COUNT(LEFT(CONCAT(label_bit, REPEAT(0x00, 13)), 13) & LEFT(CONCAT(#{interestBit}, REPEAT(0x00, 13)), 13)) AS interest_count " +
            "  FROM t_greet_user " +
            ") t " +
            "ORDER BY score DESC LIMIT #{size} OFFSET #{start}")
    List<GreetUserData> getGreetUserByPage(@Param("currentTime") int currentTime,
                                           @Param("region") int region, @Param("countryCode") String countryCode, @Param("interestBit") byte[] interestBit,
                                           @Param("registerWeight") double registerWeight, @Param("regionWeight") double regionWeight,
                                           @Param("onlineWeight") double onlineWeight, @Param("interestWeight") double interestWeight,
                                           @Param("replyWeight") double replyWeight, @Param("profileWeight") double profileWeight,
                                           @Param("start") int start, @Param("size") int size);

    @Select("SELECT *, " +
            "(" +
            "  CASE WHEN country_code = #{countryCode} THEN 100 " +
            "       WHEN region = #{region} THEN 60 ELSE 0 END * #{regionWeight} + " +
            "  CASE WHEN online_duration < 300 THEN 100 " +
            "       WHEN online_duration < 1800 THEN 70 " +
            "       WHEN online_duration < 3600 THEN 50 ELSE 10 END * #{onlineWeight} + " +
            "  CASE WHEN interest_count >= 5 THEN 100 " +
            "       WHEN interest_count >= 3 THEN 70 " +
            "       WHEN interest_count >= 1 THEN 50 ELSE 0 END * #{interestWeight} + " +
            "  CASE WHEN reply_rate >= 0.9 THEN 100 WHEN reply_rate >= 0.7 THEN 80 " +
            "       WHEN reply_rate >= 0.6 THEN 70 WHEN reply_rate >= 0.5 THEN 60 " +
            "       WHEN reply_rate >= 0.4 THEN 50 WHEN reply_rate >= 0.3 THEN 40 " +
            "       WHEN reply_rate >= 0.2 THEN 30 WHEN reply_rate >= 0.1 THEN 20 ELSE 10 END * #{replyWeight} + " +
            "  CASE WHEN profile_process >= 0.8 THEN 100 WHEN profile_process >= 0.5 THEN 70 " +
            "       WHEN profile_process >= 0.3 THEN 50 WHEN profile_process >= 0.1 THEN 30 ELSE 10 END * #{profileWeight}" +
            ") AS score " +
            "FROM (" +
            "  SELECT *, #{currentTime} - online_time AS online_duration, BIT_COUNT(LEFT(CONCAT(label_bit, REPEAT(0x00, 13)), 13) & LEFT(CONCAT(#{interestBit}, REPEAT(0x00, 13)), 13)) AS interest_count " +
            "  FROM t_greet_user WHERE (#{filterCountryCode} = 'ALL' OR country_code = #{filterCountryCode})" +
            ") t " +
            "ORDER BY score DESC LIMIT #{size} OFFSET #{start}")
    List<GreetUserData> getGreetUserByCountryCodePage(@Param("currentTime") int currentTime,
                                           @Param("region") int region, @Param("countryCode") String countryCode, @Param("filterCountryCode") String filterCountryCode, @Param("interestBit") byte[] interestBit,
                                           @Param("registerWeight") double registerWeight, @Param("regionWeight") double regionWeight,
                                           @Param("onlineWeight") double onlineWeight, @Param("interestWeight") double interestWeight,
                                           @Param("replyWeight") double replyWeight, @Param("profileWeight") double profileWeight,
                                           @Param("start") int start, @Param("size") int size);

}
