package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.DiscernTextData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


public interface DiscernTextMapper {
    @Insert("insert into ${tableName} (text, text_md5, score, is_safe, result_data, ctime) values (#{item.text},#{item.textMd5},#{item.score},#{item.isSafe},#{item.resultData},#{item.ctime}) on duplicate key update ctime = values(ctime)")
    void insert(@Param("tableName") String tableName, @Param("item") DiscernTextData data);

    @Select("select id, text, text_md5, score, is_safe, result_data, ctime from ${tableName} where text_md5=#{textMd5}")
    DiscernTextData getDiscernTextData(@Param("tableName") String tableName, @Param("textMd5") String textMd5);

}
