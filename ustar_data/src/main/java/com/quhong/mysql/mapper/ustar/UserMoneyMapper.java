package com.quhong.mysql.mapper.ustar;

import com.quhong.mysql.data.UserMoneyData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

public interface UserMoneyMapper {
    void updateMoney(@Param("userId") String userId, @Param("mleft") int mleft, @Param("mtime") Date mtime);

    @Insert("insert into t_user_money (userid,status,ctime,mtime,mtotal,mleft) " +
            "values (#{item.userid},#{item.status},#{item.ctime},#{item.mtime},#{item.mtotal},#{item.mleft})")
    void insert( @Param("item") UserMoneyData userMoneyData);


    @Select("select MAX(rid) from t_user_money")
    int getMaxRid();

}
