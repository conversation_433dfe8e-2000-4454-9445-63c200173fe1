package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.RoomEventSubData;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/7
 */
public interface RoomEventSubMapper {

    @Insert("insert into ${tableName} (`event_id`,`uid`,`is_robot`,`start_time`,`end_time`,`ctime`) values (#{item.eventId},#{item.uid},#{item.isRobot},#{item.startTime},#{item.endTime},#{item.ctime})")
    void insert(@Param("tableName") String tableName, @Param("item") RoomEventSubData data);

    @Select("SELECT id, event_id, uid, is_robot, ctime FROM ${tableName} where event_id=#{eventId} and uid!=#{uid}  order by ctime desc limit #{offset},#{pageSize} ")
    List<RoomEventSubData> selectList(@Param("tableName") String tableName, @Param("uid") String uid, @Param("eventId") int eventId, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT id, event_id, uid, is_robot, ctime FROM ${tableName} where event_id=#{eventId} and uid=#{uid}")
    RoomEventSubData selectOne(@Param("tableName") String tableName, @Param("eventId") int eventId, @Param("uid") String uid);

    @Delete("delete from ${tableName}  where event_id=#{eventId} and uid=#{uid}")
    void delete(@Param("tableName") String tableName, @Param("eventId") int eventId, @Param("uid") String uid);

    @Select("SELECT event_id as eventId FROM ( " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_0 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_1 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_2 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_3 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_4 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_5 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_6 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_7 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_8 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_9 where uid=#{uid} and end_time>#{nowTime} ) as stat order by start_time limit #{offset},#{pageSize}")
    List<Integer> getMineSubList(@Param("uid") String uid, @Param("nowTime") int nowTime, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Select("SELECT event_id as eventId FROM ( " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_0 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_1 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_2 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_3 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_4 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_5 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_6 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_7 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_8 where uid=#{uid} and end_time>#{nowTime} UNION ALL " +
            "SELECT id,event_id,uid,is_robot,start_time,end_time,ctime FROM t_room_event_sub_9 where uid=#{uid} and end_time>#{nowTime} ) as stat ")
    List<Integer> getAllMineSubList(@Param("uid") String uid, @Param("nowTime") int nowTime);

    @Delete("delete from ${tableName} where event_id=#{eventId}")
    void batchDeleteByEventId(@Param("tableName") String tableName, @Param("eventId") int eventId);

    @Select("SELECT id, event_id, uid, is_robot, ctime FROM ${tableName} where event_id=#{eventId} and is_robot = 1")
    List<RoomEventSubData> selectRobotSubList(@Param("tableName") String tableName, @Param("eventId") int eventId);

    @Select("SELECT id, event_id, uid, is_robot, ctime FROM ${tableName} where event_id=#{eventId} and is_robot = 0")
    List<RoomEventSubData> selectAllSubList(@Param("tableName") String tableName, @Param("eventId") int eventId);
}
