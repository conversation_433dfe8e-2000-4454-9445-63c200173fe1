package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.DetectUserRecordData;
import com.quhong.mysql.mapper.ShardingMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface DetectUserRecordMapper extends ShardingMapper {

    @Insert("insert into t_detect_user_record_${tableSuffix} (`uid`, `source_type`, `detect_type`, `detect_info`, `score`, `result_data`, `ctime`) values " +
            "(#{item.uid}, #{item.sourceType}, #{item.detectType}, #{item.detectInfo}, #{item.score}, #{item.resultData}, #{item.ctime})")
    void insert(@Param("tableSuffix") String tableSuffix, @Param("item") DetectUserRecordData detectUserRecordData);

    @Update("UPDATE t_detect_user_record_${tableSuffix} SET `uid` = #{item.uid}, `source_type` = #{item.sourceType}, `detect_type` = #{item.detectType}, `detect_info` = #{item.detectInfo}, `score` = #{item.score}, `result_data` = #{item.resultData}, `status` = #{item.status}, `ctime` = #{item.ctime} " +
            "WHERE `id` = #{item.id}")
    void update(@Param("tableSuffix") String tableSuffix, @Param("item") DetectUserRecordData recordData);

    @Select({
            "<script>",
            "SELECT * FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT * FROM t_detect_user_record_${tableSuffix} ",
            "WHERE detect_type=#{detectType}",
            "<when test='uid != null'> AND uid = #{uid} </when>",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "LIMIT #{size} ",
            "OFFSET #{start}",
            "</script>"
    })
    List<DetectUserRecordData> getRecords(@Param("uid") String uid, @Param("detectType") Integer detectType, @Param("start") int start, @Param("size") int size,  @Param("suffixList") List<String> suffixList);

    @Select({
            "<script>",
            "SELECT * FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT * FROM t_detect_user_record_${tableSuffix} ",
            "WHERE detect_type=#{detectType}",
            "<when test='uid != null'> AND uid = #{uid} </when>",
            "</foreach>",
            " ) AS stat",
            "ORDER BY ctime DESC ",
            "</script>"
    })
    List<DetectUserRecordData> getReportRecords(@Param("uid") String uid, @Param("detectType") Integer detectType, @Param("suffixList") List<String> suffixList);


    @Select({
            "<script>",
            "SELECT count(*) FROM ( ",
            "<foreach collection='suffixList' item='tableSuffix' separator='UNION ALL'>",
            "SELECT * FROM t_detect_user_record_${tableSuffix} ",
            "WHERE detect_type=#{detectType}",
            "<when test='uid != null'> AND uid = #{uid} </when>",
            "</foreach>",
            " ) AS stat",
            "</script>"
    })
    int getTotalRecord(@Param("uid") String uid, @Param("detectType") Integer detectType, @Param("suffixList") List<String> suffixList);

    @Select("SELECT `id`, `uid`, `source_type`, `detect_type`, `detect_info`, `score`, `result_data`, `status`, `ctime` FROM t_detect_user_record_${tableSuffix} WHERE `id` = #{id} and `ctime` = #{ctime}")
    DetectUserRecordData selectById(@Param("tableSuffix") String tableSuffix, @Param("id") int id, @Param("ctime") int ctime);
}
