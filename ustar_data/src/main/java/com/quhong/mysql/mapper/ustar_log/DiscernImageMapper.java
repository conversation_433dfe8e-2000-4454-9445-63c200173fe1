package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.DiscernImageData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;


public interface DiscernImageMapper {
    @Insert("insert into ${tableName} (image_url, image_md5, score, is_safe, result_data, ctime) values (#{item.imageUrl},#{item.imageMd5},#{item.score}, #{item.isSafe}, #{item.resultData}, #{item.ctime}) on duplicate key update ctime = values(ctime)")
    void insert(@Param("tableName") String tableName, @Param("item") DiscernImageData data);

    @Select("select id, image_url, image_md5, score, is_safe, result_data, ctime from ${tableName} where image_md5=#{imageMd5}")
    DiscernImageData getDiscernImageData(@Param("tableName") String tableName, @Param("imageMd5") String imageMd5);

    @Update("update ${tableName} set image_url = #{item.imageUrl}, image_md5 = #{item.imageMd5}, score = #{item.score}, is_safe = #{item.isSafe}, result_data = #{item.resultData}, ctime = #{item.ctime} where id = #{item.id}")
    void update(@Param("tableName") String tableName, @Param("item") DiscernImageData discernImageData);
}
