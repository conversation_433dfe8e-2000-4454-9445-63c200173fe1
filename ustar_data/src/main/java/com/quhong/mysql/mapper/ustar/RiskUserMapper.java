package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.RiskUserData;
import com.quhong.mysql.data.SendRecvFirstData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;


public interface RiskUserMapper extends BaseMapper<RiskUserData> {



    @Insert("insert into t_risk_user (uid,third_uid,status,login_type,score,ctime,mtime) values " +
            "(#{item.uid},#{item.thirdUid},#{item.status},#{item.loginType},#{item.score},#{item.ctime},#{item.mtime}) " +
            "on duplicate key update score = score + #{item.score}, mtime = #{item.mtime}")
    void insertOrUpdate( @Param("item") RiskUserData data);
}
