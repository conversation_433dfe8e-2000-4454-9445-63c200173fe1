package com.quhong.mysql.mapper.ustar;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.data.UserReportJoinData;
import com.quhong.mysql.data.UserReportData;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


public interface UserReportMapper extends BaseMapper<UserReportData> {


    @Select({
            "<script>" +
                    "select a.*, b.* from t_user_report as a left join t_user_report_num as b on a.target_id = b.aid " +
                    "where <![CDATA[ a.ctime >= #{startTime} and a.ctime <= #{endTime} ]]> " +
                    "<if test='searchUid != null' >" +
                    "and a.uid = #{searchUid} " +
                    "</if>" +
                    "<if test='origin != null' >" +
                    "and a.origin = #{origin}" +
                    "</if>" +
                    "<if test='reasonSelect != null' >" +
                    "and a.reason_select = #{reasonSelect}" +
                    "</if>" +
                    "<if test='reportNum gt 0 and reportNum lt 4' >" +
                    "and b.total = #{reportNum}" +
                    "</if>" +
                    "<if test='reportNum gte 4' >" +
                    "and <![CDATA[ b.total >= #{reportNum} ]]>" +
                    "</if>" +
                    "order by a.ctime desc " +
                    "limit #{start}, #{pageSize}" +
                    "</script>"
    })
    List<UserReportJoinData> getUserReportLeftJoinData(@Param("startTime") Integer startTime, @Param("endTime") Integer endTime, @Param("searchUid") String searchUid,
                                                       @Param("origin") String origin, @Param("reasonSelect") String reasonSelect, @Param("reportNum") int reportNum,
                                                       @Param("start") int start, @Param("pageSize") int pageSize);

    @Select({
            "<script>" +
                    "select count(*) from t_user_report as a left join t_user_report_num as b on a.target_id = b.aid " +
                    "where <![CDATA[ a.ctime >= #{startTime} and a.ctime <= #{endTime} ]]> " +
                    "<if test='searchUid != null' >" +
                    "and a.uid = #{searchUid} " +
                    "</if>" +
                    "<if test='origin != null' >" +
                    "and a.origin = #{origin}" +
                    "</if>" +
                    "<if test='reasonSelect != null' >" +
                    "and a.reason_select = #{reasonSelect}" +
                    "</if>" +
                    "<if test='reportNum gt 0 and reportNum lt 4' >" +
                    "and b.total = #{reportNum}" +
                    "</if>" +
                    "<if test='reportNum gte 4' >" +
                    "and <![CDATA[ b.total >= #{reportNum} ]]>" +
                    "</if>" +
                    "</script>"
    })
    Integer getUserReportLeftJoinCountData(@Param("startTime") Integer startTime, @Param("endTime") Integer endTime, @Param("searchUid") String searchUid,
                                                       @Param("origin") String origin, @Param("reasonSelect") String reasonSelect, @Param("reportNum") int reportNum);
}
