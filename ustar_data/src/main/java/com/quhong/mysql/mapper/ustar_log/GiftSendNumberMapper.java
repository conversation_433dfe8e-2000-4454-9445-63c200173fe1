package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.GiftSendNumberData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;


public interface GiftSendNumberMapper {
    @Insert("insert into ${tableName} (uid, gift_id, gift_num, mtime, ctime) values (#{item.uid},#{item.giftId},#{item.giftNum},#{item.mtime},#{item.ctime}) on duplicate key update gift_num = gift_num + #{item.giftNum}, mtime = #{item.mtime}")
    void insertOrUpdate(@Param("tableName") String tableName, @Param("item") GiftSendNumberData data);

    @Select("select id, uid, gift_id, gift_num, mtime, ctime from ${tableName} where uid=#{uid} and gift_id=#{giftId}")
    GiftSendNumberData getGiftSendNumberData(@Param("tableName") String tableName, @Param("uid") String uid, @Param("giftId") Integer giftId);

    @Select({
            "<script>",
            "select id, uid, gift_id, gift_num, mtime, ctime from ${tableName} where uid=#{uid} and gift_id in ",
            "<foreach item='item' collection='giftIdSet' open='(' separator=',' close=')'>#{item}</foreach>",
            "</script>"
    })
    List<GiftSendNumberData> getGiftSendNumberDataByGiftList(@Param("tableName") String tableName, @Param("uid") String uid, @Param("giftIdSet") Collection<Integer> giftIdSet);

    // 覆盖原来的数量
    @Insert("insert into ${tableName} (uid, gift_id, gift_num, mtime, ctime) values (#{item.uid},#{item.giftId},#{item.giftNum},#{item.mtime},#{item.ctime}) on duplicate key update gift_num = #{item.giftNum}, mtime = #{item.mtime}, ctime = #{item.ctime}")
    void insertOrUpdateV2(@Param("tableName") String tableName, @Param("item") GiftSendNumberData data);


    @Insert("insert into ${tableName} (uid, gift_id, gift_num, mtime, ctime) values (#{item.uid},#{item.giftId},#{item.giftNum},#{item.mtime},#{item.ctime}) on duplicate key update gift_num = #{item.giftNum}")
    void insertOrUpdateV3(@Param("tableName") String tableName, @Param("item") GiftSendNumberData data);

    @Select("select id, uid, gift_id, gift_num, mtime, ctime from ${tableName} where uid=#{uid}")
    List<GiftSendNumberData> getAllGiftSendNumberData(@Param("tableName") String tableName, @Param("uid") String uid);

    @Select("select sum(gift_num) as sumGift from ${tableName} where uid=#{uid}")
    Integer getSumAllSendGift(@Param("tableName") String tableName, @Param("uid") String uid);


}
