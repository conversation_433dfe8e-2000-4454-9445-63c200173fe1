package com.quhong.mysql.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@MapperScan(value = "com.quhong.mysql.slave_mapper.ustar_log", sqlSessionFactoryRef = "ustar_slave_log_sqlSession")
@Configuration
public class DBSlaveLogMysqlBean extends BaseMysqlBean {
    public static final String JDBC_PRE = "jdbc.ustar";
    public static final String DATABASE = "ustar_log";

    public static final String USTAR_TRANSACTION = "ustar_slave_log_sqlSession";

    @Bean(name = "ustar_slave_log_sqlSession")
    public MybatisSqlSessionFactoryBean sqlSessionFactory() throws Exception {
        return createSlaveSqlSessionFactoryBean(JDBC_PRE, DATABASE, DATABASE);
    }

    @Bean(name = USTAR_TRANSACTION)
    public DataSourceTransactionManager transactionManager() {
        return createTransactionManager(JDBC_PRE, DATABASE, DATABASE);
    }
}
