package com.quhong.mysql.config.dynamic;

import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IdModHandler implements TableNameHandler {
    private static final Logger logger = LoggerFactory.getLogger(IdModHandler.class);

    private static final ThreadLocal<String> id = new ThreadLocal<>();
    private final Integer mod;

    public IdModHandler(Integer modValue) {
        mod = modValue;
    }

    public static void setId(String idValue) {
        id.set(idValue);
    }

    /**
     * 根据id的hashCode取余获取表名
     *
     * @param sql       当前执行 SQL
     * @param tableName 表名
     * @return 动态表名
     */
    @Override
    public String dynamicTableName(String sql, String tableName) {
        String idValue = id.get();
        if (idValue == null) {
            logger.error("dynamicTableName id is null sql={} tableName={}", sql, tableName);
            throw new RuntimeException("id is null");
        } else {
            String suffix = String.valueOf(Math.abs(idValue.hashCode()) % mod);
            id.set(null);
            return tableName + "_" + suffix;
        }
    }
}
