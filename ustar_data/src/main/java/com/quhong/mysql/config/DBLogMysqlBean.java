package com.quhong.mysql.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@MapperScan(value = "com.quhong.mysql.mapper.ustar_log", sqlSessionFactoryRef = "ustar_log_sqlSession")
@Configuration
public class DBLogMysqlBean extends BaseMysqlBean {
    public static final String JDBC_PRE = "jdbc.ustar";
    public static final String DATABASE = "ustar_log";

    public static final String USTAR_LOG_TRANSACTION = "ustar_log_transaction";


    @Bean(name = "ustar_log_sqlSession")
    public MybatisSqlSessionFactoryBean sqlSessionFactory() throws Exception {
        return createSqlSessionFactoryBean(JDBC_PRE, DATABASE, DATABASE);
    }

    @Bean(name = USTAR_LOG_TRANSACTION)
    public DataSourceTransactionManager transactionManager() {
        return createTransactionManager(JDBC_PRE, DATABASE, DATABASE);
    }
}
