package com.quhong.mysql.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

@MapperScan(value = "com.quhong.mysql.mapper.ustar", sqlSessionFactoryRef = "ustar_sqlSession")
@Configuration
public class DBMysqlBean extends BaseMysqlBean {
    public static final String JDBC_PRE = "jdbc.ustar";
    public static final String DATABASE = "ustar";

    public static final String USTAR_TRANSACTION = "ustar_transaction";


    @Bean(name = "ustar_sqlSession")
    public MybatisSqlSessionFactoryBean sqlSessionFactory() throws Exception {
        return createSqlSessionFactoryBean(J<PERSON><PERSON>_<PERSON>E, DATABASE, null);
    }

    @Bean(name = USTAR_TRANSACTION)
    public DataSourceTransactionManager transactionManager() {
        return createTransactionManager(JDBC_PRE, DATABASE, null);
    }
}
