package com.quhong.mysql.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.quhong.core.utils.SpringUtils;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.interceptor.MyBatisMonitorInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.ResourcePatternUtils;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.net.URLEncoder;

public class BaseMysqlBean {
    private static final Logger logger = LoggerFactory.getLogger(BaseMysqlBean.class);

    protected DataSource dataSource;
    @Autowired
    private MonitorSender monitorSender;
    @Value("${spring.application.name:unknown}")
    public String applicationName;

    protected MybatisSqlSessionFactoryBean createSqlSessionFactoryBean(String pre, String dbName, String propName) throws Exception {
        MysqlData mysqlData = createMysqlData(pre, dbName, propName);
        return createSqlSessionFactoryBean(mysqlData);
    }

    protected MybatisSqlSessionFactoryBean createSlaveSqlSessionFactoryBean(String pre, String dbName, String propName) throws Exception {
        MysqlData mysqlData = createSlaveMysqlData(pre, dbName, propName);
        return createSqlSessionFactoryBean(mysqlData);
    }

    protected MybatisSqlSessionFactoryBean createSqlSessionFactoryBean(MysqlData mysqlData) throws Exception {
        MybatisSqlSessionFactoryBean factoryBean = new MybatisSqlSessionFactoryBean();
        DataSource dataSource = createDataSource(mysqlData);
        factoryBean.setDataSource(dataSource);
        ApplicationContext context = SpringUtils.getApplicationContext();
        Resource[] mapperLocations = ResourcePatternUtils.getResourcePatternResolver(context).getResources(getMapperLocation(mysqlData));
        factoryBean.setMapperLocations(mapperLocations);
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        factoryBean.setConfiguration(configuration);
        // MyBatisPlus分页插件支持
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        // 动态表名处理
//        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
//        HashMap<String, TableNameHandler> map = new HashMap<>();
//        map.put("test_dynamic", new IdModHandler(10));
//        dynamicTableNameInnerInterceptor.setTableNameHandlerMap(map);
//        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
        factoryBean.setPlugins(interceptor, new MyBatisMonitorInterceptor(monitorSender, applicationName.contains("operation")));
        return factoryBean;
    }

    protected String getMapperLocation(MysqlData mysqlData) {
        return "classpath*:/sqlmap/" + mysqlData.getPreName() + "/*.xml";
    }

    protected MysqlData createMysqlData(String pre, String dbName, String propName) {
        return createMysqlData(pre, dbName, propName, "host");
    }

    protected MysqlData createSlaveMysqlData(String pre, String dbName, String propName) {
        return createMysqlData(pre, dbName, propName, "slave.host");
    }

    protected MysqlData createMysqlData(String pre, String dbName, String propName, String hostName) {
        String host = SpringUtils.getProperty(pre + "." + hostName);
        if (StringUtils.isEmpty(host)) {
            throw new RuntimeException("can not find mysql host. pre=" + pre);
        }
        host = host.trim();

        String userName = SpringUtils.getProperty(pre + ".user");
        if (StringUtils.isEmpty(userName)) {
            throw new RuntimeException("can not find mysql user. pre=" + pre);
        }
        userName = userName.trim();

        String password = SpringUtils.getProperty(pre + ".password");
        if (StringUtils.isEmpty(password)) {
            throw new RuntimeException("can not find mysql password. pre=" + pre);
        }
        password = password.trim();

        String timeZone = SpringUtils.getProperty(pre + ".timeZone");
        if (StringUtils.isEmpty(timeZone)) {
            throw new RuntimeException("can not find mysql timeZone. pre=" + pre);
        }
        timeZone = timeZone.trim();

        String dbKey;
        if (propName == null) {
            dbKey = pre + ".dataBase";
        } else {
            dbKey = pre + "." + propName + ".dataBase";
        }
        String dataBase = SpringUtils.getProperty(dbKey);
        if (StringUtils.isEmpty(dataBase)) {
            throw new RuntimeException("can not find mysql database. key=" + dbKey);
        }
        dataBase = dataBase.trim();

        int port = SpringUtils.getPropertyInt(pre + ".port");
        if (port <= 0) {
            throw new RuntimeException("can not find mysql port. pre=" + pre);
        }
        MysqlData mysqlData = new MysqlData();
        mysqlData.setHost(host);
        mysqlData.setPort(port);
        mysqlData.setUserName(userName);
        mysqlData.setPassword(password);
        mysqlData.setTimeZone(timeZone);
        mysqlData.setDataBase(dataBase);
        mysqlData.setPreName(dbName);
        mysqlData.setPropName(propName);
        return mysqlData;
    }

    public DataSourceTransactionManager createTransactionManager(String pre, String dbName, String propName) {
        MysqlData mysqlData = createMysqlData(pre, dbName, propName);
        return new DataSourceTransactionManager(createDataSource(mysqlData));
    }

    protected DataSource createDataSource(MysqlData mysqlData) {
        if (dataSource != null) {
            return dataSource;
        }
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        String url = createUrl(mysqlData);
        dataSource.setUrl(url);
        dataSource.setUsername(mysqlData.getUserName());
        dataSource.setPassword(mysqlData.getPassword());

        // 配置初始化大小、最小、最大
        dataSource.setInitialSize(1);
        dataSource.setMinIdle(10);
        dataSource.setMaxActive(200);

        // 配置获取连接等待超时的时间
        dataSource.setMaxWait(60 * 1000);
        // 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        dataSource.setTimeBetweenEvictionRunsMillis(60 * 1000L);
        // 配置一个连接在池中最小生存的时间，单位是毫秒
        dataSource.setMinEvictableIdleTimeMillis(300 * 1000);

        dataSource.setValidationQuery("SELECT 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        // 打开PSCache，并且指定每个连接上PSCache的大小
        dataSource.setPoolPreparedStatements(true);
        dataSource.setMaxPoolPreparedStatementPerConnectionSize(20);
        try {
            dataSource.setFilters("stat");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        this.dataSource = dataSource;
        return dataSource;
    }

    protected String createUrl(MysqlData mysqlData) {
        StringBuilder url = new StringBuilder();
        url.append("jdbc:mysql://");
        url.append(mysqlData.getHost());
        url.append(":");
        url.append(mysqlData.getPort());
        url.append("/");
        url.append(mysqlData.getDataBase());
        url.append("?useUnicode=true&useSSL=false&characterEncoding=UTF-8&allowPublicKeyRetrieval=true&serverTimezone=");
        String timeZone = null;
        try {
            timeZone = URLEncoder.encode(mysqlData.getTimeZone(), "UTF-8");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            timeZone = mysqlData.getTimeZone();
        }
        url.append(timeZone);
        return url.toString();
    }

    public static class MysqlData {
        String host;
        int port;
        String userName;
        String password;
        String dataBase;
        String timeZone;
        String preName;
        String propName;

        public MysqlData() {

        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getDataBase() {
            return dataBase;
        }

        public void setDataBase(String dataBase) {
            this.dataBase = dataBase;
        }

        public String getTimeZone() {
            return timeZone;
        }

        public void setTimeZone(String timeZone) {
            this.timeZone = timeZone;
        }

        public String getPreName() {
            return preName;
        }

        public void setPreName(String preName) {
            this.preName = preName;
        }

        public String getPropName() {
            return propName;
        }

        public void setPropName(String propName) {
            this.propName = propName;
        }
    }
}
