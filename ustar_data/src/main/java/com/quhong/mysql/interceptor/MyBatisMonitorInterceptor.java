package com.quhong.mysql.interceptor;

import com.quhong.monitor.MonitorSender;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
})
public class MyBatisMonitorInterceptor implements Interceptor {
    private static final Logger logger = LoggerFactory.getLogger(MyBatisMonitorInterceptor.class);
    private MonitorSender monitorSender;
    private int minSize = 10000;
    private long minQueryTime = 5 * 1000L;
    private final static List<List<String>> NO_WARN_SQL_LIST = Arrays.asList(Arrays.asList("s_msg_record", "UNION ALL"));

    public MyBatisMonitorInterceptor() {
    }

    public MyBatisMonitorInterceptor(MonitorSender monitorSender, boolean isOperation) {
        this.monitorSender = monitorSender;
        if (isOperation) {
            minQueryTime = 10 * 1000L;
            minSize = 10 * 10000;
        }
    }


    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = invocation.proceed();
        long queryTime = System.currentTimeMillis() - startTime;
        if (result instanceof ArrayList) {
            ArrayList<?> resultList = (ArrayList<?>) result;
            boolean slaveMapper = false;
            if (invocation.getArgs().length > 0 && invocation.getArgs()[0] instanceof MappedStatement) {
                // 从库放宽告警条件
                MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
                slaveMapper = mappedStatement.getId().contains("slave_mapper");
            }
            if (resultList.size() > (slaveMapper ? minSize * 10 : minSize)) {
                logger.error("sql query result list size={}. queryTime={} sql={}", resultList.size(), queryTime, getSql(invocation));
                monitorSender.info("ustar_java_exception", "MySQL查询结果集: " + resultList.size() + ", 查询时间: " + queryTime, getSql(invocation));
            }
            if (queryTime > (slaveMapper ? minQueryTime * 10 : minQueryTime)) {
                String sql = getSql(invocation);
                if (noWarnBySqlTime(sql, queryTime, minQueryTime * 2)) {
                    return result;
                }
                logger.error("sql query time gt 10s. queryTime={} sql={}", queryTime, sql);
                monitorSender.info("ustar_java_exception", "MySQL查询时间: " + queryTime + ", 结果集: " + resultList.size(), sql);
            }
        }
        return result;
    }

    private boolean noWarnBySqlTime(String sql, long queryTime, long minQueryTime) {
        for (List<String> itemSqlList : NO_WARN_SQL_LIST) {
            if (itemSqlList == null || itemSqlList.isEmpty()) {
                continue;
            }
            // 使用 Set 提高查找效率
            Set<String> itemSqlSet = new HashSet<>(itemSqlList);
            int size = itemSqlSet.size();
            int count = 0;
            for (String itemSql : itemSqlSet) {
                if (queryTime < minQueryTime&&sql.contains(itemSql)) {
                    count++;
                }
                if (count == size) {
                    return true;
                }
            }
        }
        return false;
    }


    private String getSql(Invocation invocation) {
        Object target = invocation.getTarget();
        Object[] args = invocation.getArgs();
        String sql = null;
        if (target instanceof Executor) {
            boolean isUpdate = args.length == 2;
            MappedStatement ms = (MappedStatement) args[0];
            if (!isUpdate && ms.getSqlCommandType() == SqlCommandType.SELECT) {
                BoundSql boundSql;
                if (args.length == 4) {
                    boundSql = ms.getBoundSql(args[1]);
                } else {
                    boundSql = (BoundSql) args[5];
                }
                sql = boundSql.getSql();
            }
        }
        return sql;
    }
}
