package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_lucky_draw_log")
public class LuckyDrawActivityData {

    @TableId(type = IdType.AUTO)
    private Integer id;


    private String activityId;
    private String uid;
    private String rewardIndex;
    private String rewardType;

    private String rewardNameEn;
    private String rewardNameAr;
    private String rewardIcon;

    private int sourceId;
    private int rewardNum;
    private int rewardTime;
    private int ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRewardIndex() {
        return rewardIndex;
    }

    public void setRewardIndex(String rewardIndex) {
        this.rewardIndex = rewardIndex;
    }

    public String getRewardType() {
        return rewardType;
    }

    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    public String getRewardNameEn() {
        return rewardNameEn;
    }

    public void setRewardNameEn(String rewardNameEn) {
        this.rewardNameEn = rewardNameEn;
    }

    public String getRewardNameAr() {
        return rewardNameAr;
    }

    public void setRewardNameAr(String rewardNameAr) {
        this.rewardNameAr = rewardNameAr;
    }

    public String getRewardIcon() {
        return rewardIcon;
    }

    public void setRewardIcon(String rewardIcon) {
        this.rewardIcon = rewardIcon;
    }

    public int getSourceId() {
        return sourceId;
    }

    public void setSourceId(int sourceId) {
        this.sourceId = sourceId;
    }

    public int getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(int rewardNum) {
        this.rewardNum = rewardNum;
    }

    public int getRewardTime() {
        return rewardTime;
    }

    public void setRewardTime(int rewardTime) {
        this.rewardTime = rewardTime;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
