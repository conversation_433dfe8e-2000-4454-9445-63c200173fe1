package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 游戏房用户生涯数据
 */
@TableName("t_game_room_user_career")
public class GameRoomUserCareerData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * uid
     */
    private String uid;

    /**
     * 发送的礼物钻石数
     */
    private Long giftBeans;

    /**
     * 玩的总局数
     */
    private Long joinNum;

    /**
     * 赢的总局数
     */
    private Long winNum;

    /**
     * 修改时间
     */
    private Integer mtime;

    /**
     * 创建时间
     */
    private Integer ctime;

    public GameRoomUserCareerData() {

    }

    public GameRoomUserCareerData(String uid, Long giftBeans, Long joinNum, Long winNum, Integer mtime, Integer ctime) {
        this.uid = uid;
        this.giftBeans = giftBeans;
        this.joinNum = joinNum;
        this.winNum = winNum;
        this.mtime = mtime;
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Long getGiftBeans() {
        return giftBeans;
    }

    public void setGiftBeans(Long giftBeans) {
        this.giftBeans = giftBeans;
    }

    public Long getJoinNum() {
        return joinNum;
    }

    public void setJoinNum(Long joinNum) {
        this.joinNum = joinNum;
    }

    public Long getWinNum() {
        return winNum;
    }

    public void setWinNum(Long winNum) {
        this.winNum = winNum;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
