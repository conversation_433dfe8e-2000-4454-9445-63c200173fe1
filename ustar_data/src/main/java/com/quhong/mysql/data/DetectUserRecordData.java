package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <AUTHOR>
 * bigo检测文本、图片记录
 */
public class DetectUserRecordData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;            // 提交uid
    private String sourceType;    // 检测来源
    private Integer detectType;    // 0:文本  1:图片
    private String detectInfo;     // 文本或者图片链接
    private String score;
    private String resultData;
    private Integer status; // 0未放行 1已放行
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSourceType() {
        return sourceType;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getDetectType() {
        return detectType;
    }

    public void setDetectType(Integer detectType) {
        this.detectType = detectType;
    }

    public String getDetectInfo() {
        return detectInfo;
    }

    public void setDetectInfo(String detectInfo) {
        this.detectInfo = detectInfo;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getResultData() {
        return resultData;
    }

    public void setResultData(String resultData) {
        this.resultData = resultData;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
