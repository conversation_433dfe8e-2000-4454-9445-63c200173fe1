package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_room_allowance_log")
public class RoomAllowanceLogData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String day; // yyyy-MM-dd
    private String roomId;
    private Integer points; // 积分
    private Integer ratio; // 3为3% 10为10%
    private Integer award; // 奖励
    private Integer status; // 0 未领取 1 已领取 2 已过期
    private Integer ctime;

    public RoomAllowanceLogData() {
    }

    public RoomAllowanceLogData(String day, String roomId, Integer points, Integer ratio, Integer award, Integer status, Integer ctime) {
        this.day = day;
        this.roomId = roomId;
        this.points = points;
        this.ratio = ratio;
        this.award = award;
        this.status = status;
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getRatio() {
        return ratio;
    }

    public void setRatio(Integer ratio) {
        this.ratio = ratio;
    }

    public Integer getAward() {
        return award;
    }

    public void setAward(Integer award) {
        this.award = award;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
