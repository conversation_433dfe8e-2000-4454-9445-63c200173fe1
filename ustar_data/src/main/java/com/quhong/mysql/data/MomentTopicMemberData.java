package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quhong.constant.MomentConstant;

/**
 * 话题成员
 */
@TableName("t_moment_topic_member")
public class MomentTopicMemberData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;
    private Integer topicId; // t_moment_topic主键id
    private Integer role; // 1 话题创建者 2 话题管理员 3 话题关注用户 4 话题仅使用用户  -1 话题拉黑用户
    private Integer ctime; // 创建时间
    private Integer useTime; // 话题使用时间,最近一次使用时间，空或者0为未使用

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public boolean isAdmin() {
        return role == MomentConstant.TOPIC_ROLE_OWNER || role == MomentConstant.TOPIC_ROLE_ADMIN;
    }

    public boolean isOwner() {
        return role == MomentConstant.TOPIC_ROLE_OWNER;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getTopicId() {
        return topicId;
    }

    public void setTopicId(Integer topicId) {
        this.topicId = topicId;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }


    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getUseTime() {
        return useTime;
    }

    public void setUseTime(Integer useTime) {
        this.useTime = useTime;
    }
}
