package com.quhong.mysql.data;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 广告系列游戏映射数据模型
 * 用于管理广告来源用户到指定游戏房的引导映射关系
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-30
 */
@TableName("t_ad_campaign_game")
public class AdCampaignGameData {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 广告系列 (必填)：来自广告平台的唯一标识符
     */
    private String campaign;

    /**
     * 房间类型
     * 1:语音房, 2:游戏房
     */
    private Integer roomType;

    /**
     * 对应游戏Type
     */
    private Integer gameType;
    
    /**
     * 状态 (启用/禁用)：控制该映射是否生效
     * 1: 启用, 0: 禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private Integer ctime;
    
    /**
     * 更新时间
     */
    private Integer mtime;

    public AdCampaignGameData() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCampaign() {
        return campaign;
    }

    public void setCampaign(String campaign) {
        this.campaign = campaign;
    }

    public Integer getRoomType() {
        return roomType;
    }

    public void setRoomType(Integer roomType) {
        this.roomType = roomType;
    }

    public Integer getGameType() {
        return gameType;
    }

    public void setGameType(Integer gameType) {
        this.gameType = gameType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
