package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 房间活动奖励记录
 */
@TableName("t_room_event_support_record")
public class RoomEventSupportRecordData {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * uid
     */
    private String uid;
    /**
     * roomId
     */
    private String roomId;

    /**
     * 房间活动id
     */
    private Integer roomEventId;

    /**
     * 活动总奖励
     */
    private Long rewards;

    /**
     * 修改时间
     */
    private Integer mtime;

    /**
     * 创建时间
     */
    private Integer ctime;


    /**
     * 活动开始时间
     */
    @TableField(exist = false)
    private Integer startTime;

    /**
     * 活动名称
     */
    @TableField(exist = false)
    private String eventName;

    public RoomEventSupportRecordData() {

    }

    public RoomEventSupportRecordData(String roomId, Integer roomEventId, Long rewards, Integer mtime, Integer ctime) {
        this.roomId = roomId;
        this.roomEventId = roomEventId;
        this.rewards = rewards;
        this.mtime = mtime;
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getRoomEventId() {
        return roomEventId;
    }

    public void setRoomEventId(Integer roomEventId) {
        this.roomEventId = roomEventId;
    }

    public Long getRewards() {
        return rewards;
    }

    public void setRewards(Long rewards) {
        this.rewards = rewards;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getStartTime() {
        return startTime;
    }

    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }
}
