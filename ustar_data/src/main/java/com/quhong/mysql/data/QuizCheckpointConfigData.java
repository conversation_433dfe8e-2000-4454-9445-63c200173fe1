package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 *  答题关卡配置表
 *
 * <AUTHOR>
 * @date 2022/12/27
 */
@TableName(value = "t_quiz_checkpoint_config")
public class QuizCheckpointConfigData {

    /**
     *  主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 关卡号
     */
    private Integer checkpointNo;

    /**
     * 关卡名称
     */
    private String name;

    /**
     * 关卡名称阿语
     */
    private String nameAr;

    /**
     * 关卡图片
     */
    private String pictureUrl;

    /**
     * 关卡图片阿语
     */
    private String pictureUrlAr;

    /**
     * 闯关次数
     */
    private Integer timesLimit;

    /**
     * 0次/天 1次
     */
    private Integer timesType;

    /**
     * 出题数量
     */
    private Integer questionNum;

    /**
     * 题库id
     */
    private String gid;

    /**
     * 阿语题库id
     */
    private String arGid;

    /**
     * 闯关门槛类型 0分值通过 1无错通关
     */
    private Integer limitType;

    /**
     * 通关分数
     */
    private Integer limitScore;

    /**
     * 复活条件 0钻/次 1金币/次
     */
    private Integer againConditionType;

    /**
     * 复活需要的钻石或金币的数量
     */
    private Integer againCostNum;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public Integer getCheckpointNo() {
        return checkpointNo;
    }

    public void setCheckpointNo(Integer checkpointNo) {
        this.checkpointNo = checkpointNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public Integer getTimesLimit() {
        return timesLimit;
    }

    public void setTimesLimit(Integer timesLimit) {
        this.timesLimit = timesLimit;
    }

    public Integer getTimesType() {
        return timesType;
    }

    public void setTimesType(Integer timesType) {
        this.timesType = timesType;
    }

    public Integer getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(Integer questionNum) {
        this.questionNum = questionNum;
    }

    public String getGid() {
        return gid;
    }

    public void setGid(String gid) {
        this.gid = gid;
    }

    public String getArGid() {
        return arGid;
    }

    public void setArGid(String arGid) {
        this.arGid = arGid;
    }

    public Integer getLimitType() {
        return limitType;
    }

    public void setLimitType(Integer limitType) {
        this.limitType = limitType;
    }

    public Integer getLimitScore() {
        return limitScore;
    }

    public void setLimitScore(Integer limitScore) {
        this.limitScore = limitScore;
    }

    public Integer getAgainConditionType() {
        return againConditionType;
    }

    public void setAgainConditionType(Integer againConditionType) {
        this.againConditionType = againConditionType;
    }

    public Integer getAgainCostNum() {
        return againCostNum;
    }

    public void setAgainCostNum(Integer againCostNum) {
        this.againCostNum = againCostNum;
    }

    public String getPictureUrl() {
        return pictureUrl;
    }

    public void setPictureUrl(String pictureUrl) {
        this.pictureUrl = pictureUrl;
    }

    public String getPictureUrlAr() {
        return pictureUrlAr;
    }

    public void setPictureUrlAr(String pictureUrlAr) {
        this.pictureUrlAr = pictureUrlAr;
    }
}
