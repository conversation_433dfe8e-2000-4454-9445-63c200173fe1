package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 召集免费次数
 *
 * <AUTHOR>
 * @date 2023/2/13
 */
@TableName("t_gathering_fee_times")
public class GatheringFeeTimesData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 剩余召集次数
     */
    private Integer num;

    /**
     * 修改时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }
}
