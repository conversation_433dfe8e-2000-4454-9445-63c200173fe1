package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_user_friend_level")
public class UserFriendLevelData {
    @TableId(type = IdType.AUTO)
    private Integer rid;
    private String uid;
    private Long exp;
    private Integer level;
    private Integer ctime;
    private Integer mtime;
    private String riskDesc; // 图灵顿风险，没有为空字符串,默认为空字符串

    @TableField(exist = false)
    private Integer createStatus; // 非数据库字段，是否第一次创建记录 1是 ，其他不是

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Long getExp() {
        return exp;
    }

    public void setExp(Long exp) {
        this.exp = exp;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getRiskDesc() {
        return riskDesc;
    }

    public void setRiskDesc(String riskDesc) {
        this.riskDesc = riskDesc;
    }

    public Integer getCreateStatus() {
        return createStatus;
    }

    public void setCreateStatus(Integer createStatus) {
        this.createStatus = createStatus;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("UserFriendLevelData{");
        sb.append("rid=").append(rid);
        sb.append(", uid='").append(uid).append('\'');
        sb.append(", exp=").append(exp);
        sb.append(", level=").append(level);
        sb.append(", ctime=").append(ctime);
        sb.append(", mtime=").append(mtime);
        sb.append(", riskDesc='").append(riskDesc).append('\'');
        sb.append(", createStatus=").append(createStatus);
        sb.append('}');
        return sb.toString();
    }
}
