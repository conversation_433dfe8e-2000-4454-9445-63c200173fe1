package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quhong.core.utils.DateHelper;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@TableName("t_upload_background")
public class UploadBackgroundData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 背景图片url
     */
    private String bgUrl;

    /**
     * 主麦位图标
     */
    private String mIcon;

    /**
     * 子麦位图标
     */
    private String cIcon;

    /**
     * 列表预览图
     */
    private String preview;

    /**
     * 主题开关
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Integer ctime;

    public UploadBackgroundData() {
    }

    public UploadBackgroundData(String uid, String bgUrl, String preview) {
        this.uid = uid;
        this.bgUrl = bgUrl;
        this.preview = preview;
        this.mIcon = "https://cdn3.qmovies.tv/youstar/vipseat5.png";
        this.cIcon = "https://cdn3.qmovies.tv/youstar/sitdown5.png";
        this.status = 1;
        this.ctime = DateHelper.getNowSeconds();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getBgUrl() {
        return bgUrl;
    }

    public void setBgUrl(String bgUrl) {
        this.bgUrl = bgUrl;
    }

    public String getmIcon() {
        return mIcon;
    }

    public void setmIcon(String mIcon) {
        this.mIcon = mIcon;
    }

    public String getcIcon() {
        return cIcon;
    }

    public void setcIcon(String cIcon) {
        this.cIcon = cIcon;
    }

    public String getPreview() {
        return preview;
    }

    public void setPreview(String preview) {
        this.preview = preview;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
