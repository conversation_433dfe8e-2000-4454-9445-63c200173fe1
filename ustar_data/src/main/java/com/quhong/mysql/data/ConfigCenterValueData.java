package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_config_center_value")
public class ConfigCenterValueData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String configKey;                 // 对应key
    private String configValue;               // 设置value
    private Integer platform;                 // -1: 默认 0:安卓  1: ios
    private Integer newUser;                  // -1: 默认 0:否  1: 是
    private Integer payUser;                  // -1: 默认 0:否  1: 是
    private Integer gteVersion;               // -1: 默认 具体版本号
    private Integer lteVersion;               // -1: 默认 具体版本号
    private Integer valid;
    private Integer mtime;
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getNewUser() {
        return newUser;
    }

    public void setNewUser(Integer newUser) {
        this.newUser = newUser;
    }

    public Integer getPayUser() {
        return payUser;
    }

    public void setPayUser(Integer payUser) {
        this.payUser = payUser;
    }

    public Integer getGteVersion() {
        return gteVersion;
    }

    public void setGteVersion(Integer gteVersion) {
        this.gteVersion = gteVersion;
    }

    public Integer getLteVersion() {
        return lteVersion;
    }

    public void setLteVersion(Integer lteVersion) {
        this.lteVersion = lteVersion;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
