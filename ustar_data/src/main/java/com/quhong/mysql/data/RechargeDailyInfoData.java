package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

@TableName("t_recharge_daily_info")
public class RechargeDailyInfoData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("recharge_date")
    private Date rechargeDate;

    /**
     * 1: GP 支付、 2:Apple 支付 3: Tap 支付、 4:Admin 支付、 5:JollychicCharge、6：huawei_pay 7:OPayCharge  8: 中台支付
     */
    @TableField("pay_type")
    private Integer payType;

    private String uid;

    @TableField("recharge_money")
    private BigDecimal rechargeMoney;

    @TableField("recharge_diamond")
    private Integer rechargeDiamond;

    @TableField("s_type")
    private Integer sType;

    @TableField("register_time")
    private Integer registerTime;

    @TableField("recharge_time")
    private Integer rechargeTime;

    private Integer os;

    @TableField("first_charge")
    private Integer firstCharge;

    @TableField("sub_type")
    private String subType;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getRechargeDate() {
        return rechargeDate;
    }

    public void setRechargeDate(Date rechargeDate) {
        this.rechargeDate = rechargeDate;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public BigDecimal getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(BigDecimal rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public Integer getRechargeDiamond() {
        return rechargeDiamond;
    }

    public void setRechargeDiamond(Integer rechargeDiamond) {
        this.rechargeDiamond = rechargeDiamond;
    }

    public Integer getsType() {
        return sType;
    }

    public void setsType(Integer sType) {
        this.sType = sType;
    }

    public Integer getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Integer registerTime) {
        this.registerTime = registerTime;
    }

    public Integer getRechargeTime() {
        return rechargeTime;
    }

    public void setRechargeTime(Integer rechargeTime) {
        this.rechargeTime = rechargeTime;
    }

    public Integer getOs() {
        return os;
    }

    public void setOs(Integer os) {
        this.os = os;
    }

    public Integer getFirstCharge() {
        return firstCharge;
    }

    public void setFirstCharge(Integer firstCharge) {
        this.firstCharge = firstCharge;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }
}
