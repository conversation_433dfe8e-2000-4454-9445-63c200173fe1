package com.quhong.mysql.data;

/**
 * 用户每日获得的友好积分经验明细，暂时不写mysql
 */
public class UserFriendExpDetailData {
    private String uid;
    private String dateStr;//年-月-日 格式的字符串
    // 加分项
    private int upMicInvite;//邀请新用户上麦的积分
    private String upMicInviteIds = "";// 邀请新用户上麦人数一天内排重
    private int sendGiftPerson;//送礼人数的积分
    private int sendGiftNewPerson;//送礼新用户人数的积分
    private String sendGiftPersonIds = "";// 送礼人数一天内排重
    private int receiverGiftPerson;//收礼人数的积分
    private int receiverGiftNewPerson;//收礼新用户人数的积分
    private String receiverGiftPersonIds = "";// 收礼人数一天内排重
    private int welcomePerson;//欢迎进房用户积分
    private String welcomePersonIds = "";// 欢迎进房用户一天内排重
    private int dailyLogin;// vip日常登入积分
    private int chargeDiamonds;// 充值钻石积分
    private int todayAddExp;//当日获得积分

    // 减分项
    private String kickGt200UserIds = "";// 踢友好度>200的用户一天内排重
    private int kickGt200User;// 踢友好度>200的用户出房间 减积分
    private int kickGt200UserPre;// 上一次踢友好度>200的用户出房间的减分
    private int deviceAccount;// 设备关联账号被封禁
    private int msgViolation;// 房间、私信聊天内容被用户举报
    private int userProfileViolation;// 个人资料违规
    private int momentViolation;// 动态发布、评论内容违规
    private int roomProfileViolation;// 房间资料内容违规（房间封面、名称、公告）
    private int deviceRiskViolation;// 非法改装设备登录，设备使用作弊软件
    private int todayReduceExp;//当日减掉积分

    private int ctime;
    private int mtime;

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public int getUpMicInvite() {
        return upMicInvite;
    }

    public void setUpMicInvite(int upMicInvite) {
        this.upMicInvite = upMicInvite;
    }

    public String getUpMicInviteIds() {
        return upMicInviteIds;
    }

    public void setUpMicInviteIds(String upMicInviteIds) {
        this.upMicInviteIds = upMicInviteIds;
    }

    public int getSendGiftPerson() {
        return sendGiftPerson;
    }

    public void setSendGiftPerson(int sendGiftPerson) {
        this.sendGiftPerson = sendGiftPerson;
    }

    public String getSendGiftPersonIds() {
        return sendGiftPersonIds;
    }

    public void setSendGiftPersonIds(String sendGiftPersonIds) {
        this.sendGiftPersonIds = sendGiftPersonIds;
    }

    public int getReceiverGiftPerson() {
        return receiverGiftPerson;
    }

    public void setReceiverGiftPerson(int receiverGiftPerson) {
        this.receiverGiftPerson = receiverGiftPerson;
    }

    public String getReceiverGiftPersonIds() {
        return receiverGiftPersonIds;
    }

    public void setReceiverGiftPersonIds(String receiverGiftPersonIds) {
        this.receiverGiftPersonIds = receiverGiftPersonIds;
    }

    public int getWelcomePerson() {
        return welcomePerson;
    }

    public void setWelcomePerson(int welcomePerson) {
        this.welcomePerson = welcomePerson;
    }

    public String getWelcomePersonIds() {
        return welcomePersonIds;
    }

    public void setWelcomePersonIds(String welcomePersonIds) {
        this.welcomePersonIds = welcomePersonIds;
    }

    public int getDailyLogin() {
        return dailyLogin;
    }

    public void setDailyLogin(int dailyLogin) {
        this.dailyLogin = dailyLogin;
    }

    public int getChargeDiamonds() {
        return chargeDiamonds;
    }

    public void setChargeDiamonds(int chargeDiamonds) {
        this.chargeDiamonds = chargeDiamonds;
    }

    public int getTodayAddExp() {
        return todayAddExp;
    }

    public void setTodayAddExp(int todayAddExp) {
        this.todayAddExp = todayAddExp;
    }

    public String getKickGt200UserIds() {
        return kickGt200UserIds;
    }

    public void setKickGt200UserIds(String kickGt200UserIds) {
        this.kickGt200UserIds = kickGt200UserIds;
    }

    public int getKickGt200User() {
        return kickGt200User;
    }

    public void setKickGt200User(int kickGt200User) {
        this.kickGt200User = kickGt200User;
    }

    public int getKickGt200UserPre() {
        return kickGt200UserPre;
    }

    public void setKickGt200UserPre(int kickGt200UserPre) {
        this.kickGt200UserPre = kickGt200UserPre;
    }

    public int getDeviceAccount() {
        return deviceAccount;
    }

    public void setDeviceAccount(int deviceAccount) {
        this.deviceAccount = deviceAccount;
    }

    public int getMsgViolation() {
        return msgViolation;
    }

    public void setMsgViolation(int msgViolation) {
        this.msgViolation = msgViolation;
    }

    public int getUserProfileViolation() {
        return userProfileViolation;
    }

    public void setUserProfileViolation(int userProfileViolation) {
        this.userProfileViolation = userProfileViolation;
    }

    public int getMomentViolation() {
        return momentViolation;
    }

    public void setMomentViolation(int momentViolation) {
        this.momentViolation = momentViolation;
    }

    public int getRoomProfileViolation() {
        return roomProfileViolation;
    }

    public void setRoomProfileViolation(int roomProfileViolation) {
        this.roomProfileViolation = roomProfileViolation;
    }

    public int getDeviceRiskViolation() {
        return deviceRiskViolation;
    }

    public void setDeviceRiskViolation(int deviceRiskViolation) {
        this.deviceRiskViolation = deviceRiskViolation;
    }

    public int getTodayReduceExp() {
        return todayReduceExp;
    }

    public void setTodayReduceExp(int todayReduceExp) {
        this.todayReduceExp = todayReduceExp;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }

    public int getSendGiftNewPerson() {
        return sendGiftNewPerson;
    }

    public void setSendGiftNewPerson(int sendGiftNewPerson) {
        this.sendGiftNewPerson = sendGiftNewPerson;
    }

    public int getReceiverGiftNewPerson() {
        return receiverGiftNewPerson;
    }

    public void setReceiverGiftNewPerson(int receiverGiftNewPerson) {
        this.receiverGiftNewPerson = receiverGiftNewPerson;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("UserFriendExpDetailData{");
        sb.append("uid='").append(uid).append('\'');
        sb.append(", dateStr='").append(dateStr).append('\'');
        sb.append(", upMicInvite=").append(upMicInvite);
        sb.append(", upMicInviteIds='").append(upMicInviteIds).append('\'');
        sb.append(", sendGiftPerson=").append(sendGiftPerson);
        sb.append(", sendGiftNewPerson=").append(sendGiftNewPerson);
        sb.append(", sendGiftPersonIds='").append(sendGiftPersonIds).append('\'');
        sb.append(", receiverGiftPerson=").append(receiverGiftPerson);
        sb.append(", receiverGiftNewPerson=").append(receiverGiftNewPerson);
        sb.append(", receiverGiftPersonIds='").append(receiverGiftPersonIds).append('\'');
        sb.append(", welcomePerson=").append(welcomePerson);
        sb.append(", welcomePersonIds='").append(welcomePersonIds).append('\'');
        sb.append(", dailyLogin=").append(dailyLogin);
        sb.append(", chargeDiamonds=").append(chargeDiamonds);
        sb.append(", todayAddExp=").append(todayAddExp);
        sb.append(", kickGt200UserIds='").append(kickGt200UserIds).append('\'');
        sb.append(", kickGt200User=").append(kickGt200User);
        sb.append(", kickGt200UserPre=").append(kickGt200UserPre);
        sb.append(", deviceAccount=").append(deviceAccount);
        sb.append(", msgViolation=").append(msgViolation);
        sb.append(", userProfileViolation=").append(userProfileViolation);
        sb.append(", momentViolation=").append(momentViolation);
        sb.append(", roomProfileViolation=").append(roomProfileViolation);
        sb.append(", deviceRiskViolation=").append(deviceRiskViolation);
        sb.append(", todayReduceExp=").append(todayReduceExp);
        sb.append(", ctime=").append(ctime);
        sb.append(", mtime=").append(mtime);
        sb.append('}');
        return sb.toString();
    }
}
