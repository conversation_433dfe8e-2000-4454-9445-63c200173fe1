package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2022/8/27
 */
@TableName("t_hot_search_history")
public class HotSearchHistoryData {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String uid;

    /**
     * 搜索key
     */
    private String searchKey;

    /**
     * 1 房间 2 用户
     */
    private Integer searchType;

    /**
     * 累积搜索次数
     */
    private Integer searchNum;

    /**
     * 修改时间
     */
    private Integer mtime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public Integer getSearchNum() {
        return searchNum;
    }

    public void setSearchNum(Integer searchNum) {
        this.searchNum = searchNum;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("HotSearchHistoryData{");
        sb.append("id=").append(id);
        sb.append(", uid='").append(uid).append('\'');
        sb.append(", searchKey='").append(searchKey).append('\'');
        sb.append(", searchType=").append(searchType);
        sb.append(", searchNum=").append(searchNum);
        sb.append(", mtime=").append(mtime);
        sb.append('}');
        return sb.toString();
    }
}
