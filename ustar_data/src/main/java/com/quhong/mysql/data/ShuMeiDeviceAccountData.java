package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_shu_mei_device_account")
public class ShuMeiDeviceAccountData {
    @TableId(type = IdType.AUTO)
    private Integer rid;
    private String shuMeiId;
    private String shuMeiText;
    private String tnId;
    private String tnRisk;
    private Integer ctime;
    private Integer mtime;
    private String uid ;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getShuMeiId() {
        return shuMeiId;
    }

    public void setShuMeiId(String shuMeiId) {
        this.shuMeiId = shuMeiId;
    }

    public String getShuMeiText() {
        return shuMeiText;
    }

    public void setShuMeiText(String shuMeiText) {
        this.shuMeiText = shuMeiText;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public String getTnRisk() {
        return tnRisk;
    }

    public void setTnRisk(String tnRisk) {
        this.tnRisk = tnRisk;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("ShuMeiDeviceAccountData{");
        sb.append("rid=").append(rid);
        sb.append(", shuMeiId='").append(shuMeiId).append('\'');
        sb.append(", shuMeiText='").append(shuMeiText).append('\'');
        sb.append(", tnId='").append(tnId).append('\'');
        sb.append(", tnRisk='").append(tnRisk).append('\'');
        sb.append(", ctime=").append(ctime);
        sb.append(", mtime=").append(mtime);
        sb.append(", uid='").append(uid).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
