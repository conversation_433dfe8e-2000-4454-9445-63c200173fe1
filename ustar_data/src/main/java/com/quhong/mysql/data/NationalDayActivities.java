package com.quhong.mysql.data;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quhong.utils.CollectionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@TableName("t_national_day_activities_template")
public class NationalDayActivities {
    private static final Logger logger = LoggerFactory.getLogger(NationalDayActivities.class);

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String activityNameEn; // 活动名称英语
    private String activityNameAr; // 活动名称阿语
    private String headUrlEn; // 页面大头页url英语
    private String headUrlAr; // 页面大头页url阿语
    private String activityDescriptionEn; // 活动说明英语
    private String activityDescriptionAr; // 活动说明阿语
    private String giftList; // 活动礼物id列表
    private int startTime; // 活动开始时间, 2022021800，测试服YmdH，正式服Ymd...
    private int endTime; // 活动结束时间, 2022021900，测试服YmdH，正式服Ymd...
    private String activityUrl; // 活动H5地址
    private String previewUrl; // 活动H5预览地址带uid
    private String ctime; // 创建时间, 2022-02-15 08:48:20
    private String displayTitleEn; // 展示国家荣耀标题英语
    private String displayTitleAr; // 展示国家荣耀标题阿语
    private String specialEffectInfo; // 特效展示说明
    private String rewardTitleEn; // 奖励标题英语
    private String rewardTitleAr; // 奖励标题阿语
    private int specialEffect; // 特效选择 1 低阶特效 2 高阶特效
    private int rewardCondition; // 获奖所需次数
    private String rewardRulesEn; // 奖励说明英语
    private String rewardRulesAr; // 奖励说明阿语
    private String rewardAttributes; // 奖励属性信息 1 勋章 2 麦位框 3 气泡框 4 浮屏 5 入场动画 6 背包礼物 7 房间背景图 8 麦位声波


    private transient List<Integer> giftIdList;
    // 解析giftList后的对象
    private transient List<GiftListObj> giftListObjList;
    // 解析specialEffectInfo后的对象
    private transient List<SpecialEffectInfoObj> specialEffectInfoObjList;
    // 解析rewardAttributes后的对象
    private transient List<RewardAttributesObj> rewardAttributesObjList;

    public boolean stringDataToObj() {
        try {
            giftListObjList = JSON.parseArray(giftList, GiftListObj.class);
            specialEffectInfoObjList = JSON.parseArray(specialEffectInfo, SpecialEffectInfoObj.class);
            rewardAttributesObjList = JSON.parseArray(rewardAttributes, RewardAttributesObj.class);
            giftIdList = CollectionUtil.getPropertyList(giftListObjList, GiftListObj::getGift_id, null);
            return true;
        } catch (Exception e) {
            logger.error("string data to obj error, giftList={} specialEffectInfo={} rewardAttributes={}", giftList, specialEffectInfo, rewardAttributes, e);
            return false;
        }
    }

    public static class RewardAttributesObj {
        private int reward_id;
        private int reward_type;
        private int reward_time;
        private int reward_times;
        private int reward_num;
        private boolean disabled;
        private String reward_icon;

        public int getReward_id() {
            return reward_id;
        }

        public void setReward_id(int reward_id) {
            this.reward_id = reward_id;
        }

        public int getReward_type() {
            return reward_type;
        }

        public void setReward_type(int reward_type) {
            this.reward_type = reward_type;
        }

        public int getReward_time() {
            return reward_time;
        }

        public void setReward_time(int reward_time) {
            this.reward_time = reward_time;
        }

        public int getReward_times() {
            return reward_times;
        }

        public void setReward_times(int reward_times) {
            this.reward_times = reward_times;
        }

        public int getReward_num() {
            return reward_num;
        }

        public void setReward_num(int reward_num) {
            this.reward_num = reward_num;
        }

        public boolean isDisabled() {
            return disabled;
        }

        public void setDisabled(boolean disabled) {
            this.disabled = disabled;
        }

        public String getReward_icon() {
            return reward_icon;
        }

        public void setReward_icon(String reward_icon) {
            this.reward_icon = reward_icon;
        }
    }

    public static class SpecialEffectInfoObj {
        private String display_rules_en;
        private String display_rules_ar;
        private String display_url;
        private int display_effect;
        private Integer triggerNumber; // 发送次数，前端展示

        public String getDisplay_rules_en() {
            return display_rules_en;
        }

        public void setDisplay_rules_en(String display_rules_en) {
            this.display_rules_en = display_rules_en;
        }

        public String getDisplay_rules_ar() {
            return display_rules_ar;
        }

        public void setDisplay_rules_ar(String display_rules_ar) {
            this.display_rules_ar = display_rules_ar;
        }

        public String getDisplay_url() {
            return display_url;
        }

        public void setDisplay_url(String display_url) {
            this.display_url = display_url;
        }

        public int getDisplay_effect() {
            return display_effect;
        }

        public void setDisplay_effect(int display_effect) {
            this.display_effect = display_effect;
        }

        public Integer getTriggerNumber() {
            return triggerNumber;
        }

        public void setTriggerNumber(Integer triggerNumber) {
            this.triggerNumber = triggerNumber;
        }
    }

    public static class GiftListObj {
        private int gift_id;
        private String gift_name;
        private String gift_name_ar;
        private int gift_price;
        private String gift_gicon;

        public int getGift_id() {
            return gift_id;
        }

        public void setGift_id(int gift_id) {
            this.gift_id = gift_id;
        }

        public String getGift_name() {
            return gift_name;
        }

        public void setGift_name(String gift_name) {
            this.gift_name = gift_name;
        }

        public String getGift_name_ar() {
            return gift_name_ar;
        }

        public void setGift_name_ar(String gift_name_ar) {
            this.gift_name_ar = gift_name_ar;
        }

        public int getGift_price() {
            return gift_price;
        }

        public void setGift_price(int gift_price) {
            this.gift_price = gift_price;
        }

        public String getGift_gicon() {
            return gift_gicon;
        }

        public void setGift_gicon(String gift_gicon) {
            this.gift_gicon = gift_gicon;
        }
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityNameEn() {
        return activityNameEn;
    }

    public void setActivityNameEn(String activityNameEn) {
        this.activityNameEn = activityNameEn;
    }

    public String getActivityNameAr() {
        return activityNameAr;
    }

    public void setActivityNameAr(String activityNameAr) {
        this.activityNameAr = activityNameAr;
    }

    public String getHeadUrlEn() {
        return headUrlEn;
    }

    public void setHeadUrlEn(String headUrlEn) {
        this.headUrlEn = headUrlEn;
    }

    public String getHeadUrlAr() {
        return headUrlAr;
    }

    public void setHeadUrlAr(String headUrlAr) {
        this.headUrlAr = headUrlAr;
    }

    public String getActivityDescriptionEn() {
        return activityDescriptionEn;
    }

    public void setActivityDescriptionEn(String activityDescriptionEn) {
        this.activityDescriptionEn = activityDescriptionEn;
    }

    public String getActivityDescriptionAr() {
        return activityDescriptionAr;
    }

    public void setActivityDescriptionAr(String activityDescriptionAr) {
        this.activityDescriptionAr = activityDescriptionAr;
    }

    public String getGiftList() {
        return giftList;
    }

    public void setGiftList(String giftList) {
        this.giftList = giftList;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public String getActivityUrl() {
        return activityUrl;
    }

    public void setActivityUrl(String activityUrl) {
        this.activityUrl = activityUrl;
    }

    public String getPreviewUrl() {
        return previewUrl;
    }

    public void setPreviewUrl(String previewUrl) {
        this.previewUrl = previewUrl;
    }

    public String getCtime() {
        return ctime;
    }

    public void setCtime(String ctime) {
        this.ctime = ctime;
    }

    public String getDisplayTitleEn() {
        return displayTitleEn;
    }

    public void setDisplayTitleEn(String displayTitleEn) {
        this.displayTitleEn = displayTitleEn;
    }

    public String getDisplayTitleAr() {
        return displayTitleAr;
    }

    public void setDisplayTitleAr(String displayTitleAr) {
        this.displayTitleAr = displayTitleAr;
    }

    public String getSpecialEffectInfo() {
        return specialEffectInfo;
    }

    public void setSpecialEffectInfo(String specialEffectInfo) {
        this.specialEffectInfo = specialEffectInfo;
    }

    public String getRewardTitleEn() {
        return rewardTitleEn;
    }

    public void setRewardTitleEn(String rewardTitleEn) {
        this.rewardTitleEn = rewardTitleEn;
    }

    public String getRewardTitleAr() {
        return rewardTitleAr;
    }

    public void setRewardTitleAr(String rewardTitleAr) {
        this.rewardTitleAr = rewardTitleAr;
    }

    public int getSpecialEffect() {
        return specialEffect;
    }

    public void setSpecialEffect(int specialEffect) {
        this.specialEffect = specialEffect;
    }

    public int getRewardCondition() {
        return rewardCondition;
    }

    public void setRewardCondition(int rewardCondition) {
        this.rewardCondition = rewardCondition;
    }

    public String getRewardRulesEn() {
        return rewardRulesEn;
    }

    public void setRewardRulesEn(String rewardRulesEn) {
        this.rewardRulesEn = rewardRulesEn;
    }

    public String getRewardRulesAr() {
        return rewardRulesAr;
    }

    public void setRewardRulesAr(String rewardRulesAr) {
        this.rewardRulesAr = rewardRulesAr;
    }

    public String getRewardAttributes() {
        return rewardAttributes;
    }

    public void setRewardAttributes(String rewardAttributes) {
        this.rewardAttributes = rewardAttributes;
    }

    public List<GiftListObj> getGiftListObjList() {
        return giftListObjList;
    }

    public void setGiftListObjList(List<GiftListObj> giftListObjList) {
        this.giftListObjList = giftListObjList;
    }

    public List<SpecialEffectInfoObj> getSpecialEffectInfoObjList() {
        return specialEffectInfoObjList;
    }

    public void setSpecialEffectInfoObjList(List<SpecialEffectInfoObj> specialEffectInfoObjList) {
        this.specialEffectInfoObjList = specialEffectInfoObjList;
    }

    public List<RewardAttributesObj> getRewardAttributesObjList() {
        return rewardAttributesObjList;
    }

    public void setRewardAttributesObjList(List<RewardAttributesObj> rewardAttributesObjList) {
        this.rewardAttributesObjList = rewardAttributesObjList;
    }

    public List<Integer> getGiftIdList() {
        return giftIdList;
    }

    public void setGiftIdList(List<Integer> giftIdList) {
        this.giftIdList = giftIdList;
    }
}
