package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_ai_customized_gift_upload")
public class AiCustomizedGiftUploadData {

    @TableId(type = IdType.AUTO)
    private Integer rid;
    private String uid;
    /**
     * 0待审核 1 通过 2 审核不通过
     */
    private Integer state;

    /**
     * 头像url
     */
    private String imageUrl;

    /**
     * 上传资源的月份
     */
    private String month; // yyyy_mm
    /**
     * 人审的备注消息
     */
    private String descNotice;
    /**
     * 审核不通过次数
     */
    private Integer failCount;
    private Integer mtime;
    private Integer ctime;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDescNotice() {
        return descNotice;
    }

    public void setDescNotice(String descNotice) {
        this.descNotice = descNotice;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public void setFailCount(Integer failCount) {
        this.failCount = failCount;
    }
}
