package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 用户充值记录（用于首充判断）
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
@TableName("t_user_recharge_record")
public class UserRechargeRecordData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * uid
     */
    private String uid;

    /**
     * 充值订单id
     */
    private String orderId;

    /**
     * 充值金额
     */
    private Double rechargeMoney;

    /**
     * 充值钻石数
     */
    private Integer rechargeDiamond;

    /**
     * 充值时间
     */
    private Integer rechargeTime;

    /**
     * 是否领取首充礼包 0否 1是
     */
    private Integer firstCharge;

    /**
     * 是否退款 0没有退款 1已退款 2已退款并回收首充奖励
     */
    private Integer hasRefunded;

    /**
     * tnId
     */
    private String tnId;

    /**
     * 首充增加的钻石
     */
    private Integer firstChargedAdd;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Double getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(Double rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public Integer getRechargeDiamond() {
        return rechargeDiamond;
    }

    public void setRechargeDiamond(Integer rechargeDiamond) {
        this.rechargeDiamond = rechargeDiamond;
    }

    public Integer getRechargeTime() {
        return rechargeTime;
    }

    public void setRechargeTime(Integer rechargeTime) {
        this.rechargeTime = rechargeTime;
    }

    public Integer getFirstCharge() {
        return firstCharge;
    }

    public void setFirstCharge(Integer firstCharge) {
        this.firstCharge = firstCharge;
    }

    public Integer getHasRefunded() {
        return hasRefunded;
    }

    public void setHasRefunded(Integer hasRefunded) {
        this.hasRefunded = hasRefunded;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public Integer getFirstChargedAdd() {
        return firstChargedAdd;
    }

    public void setFirstChargedAdd(Integer firstChargedAdd) {
        this.firstChargedAdd = firstChargedAdd;
    }
}
