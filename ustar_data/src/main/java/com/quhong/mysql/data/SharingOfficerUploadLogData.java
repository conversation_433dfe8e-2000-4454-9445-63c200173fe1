package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_sharing_officer_uploadlog")
public class SharingOfficerUploadLogData {

    @TableId(type = IdType.AUTO)
    private Integer rid;
    private String uid;
    private String tnId;
    /**
     * 0待审核 1 通过 2 审核不通过
     */
    private Integer state;
    /**
     * 1初级  2 中级 3高级
     */
    private Integer levelType;
    /**
     * 1 SNAPCHAT 2 INSTAGRAM 3 TIKTOK
     */
    private Integer channelType;

    private String imageUrl;

    private String videoUrl;
    /**
     * 人审的备注消息
     */
    private String descNotice;
    private Integer mtime;
    private Integer ctime;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getLevelType() {
        return levelType;
    }

    public void setLevelType(Integer levelType) {
        this.levelType = levelType;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getDescNotice() {
        return descNotice;
    }

    public void setDescNotice(String descNotice) {
        this.descNotice = descNotice;
    }

    public Integer getMtime() {
        return mtime;
    }

    public void setMtime(Integer mtime) {
        this.mtime = mtime;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
