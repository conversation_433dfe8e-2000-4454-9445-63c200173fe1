package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

/**
 * 用户的充值优惠卷
 *
 * <AUTHOR>
 * @date 2024/3/15
 */
@TableName(value = "t_user_recharge_coupon")
public class UserRechargeCouponData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 优惠卷类型： 0需要获得 1上线后所以人都有
     */
    private Integer couponType;

    /**
     * 优惠卷id
     */
    private Integer couponId;

    /**
     * 失效时间
     */
    private Integer endTime;

    /**
     * 来源
     */
    private String source;

    /**
     * 状态: 0未使用 1已使用 2已过期
     */
    private Integer status;

    /**
     * 使用时间
     */
    private Integer useTime;

    /**
     * 对应的充值订单id
     */
    private String orderId;

    /**
     * 充值钻石数
     */
    private Integer rechargeDiamonds;

    /**
     * 获得日期
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getCouponType() {
        return couponType;
    }

    public void setCouponType(Integer couponType) {
        this.couponType = couponType;
    }

    public Integer getCouponId() {
        return couponId;
    }

    public void setCouponId(Integer couponId) {
        this.couponId = couponId;
    }

    public Integer getEndTime() {
        return endTime;
    }

    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getUseTime() {
        return useTime;
    }

    public void setUseTime(Integer useTime) {
        this.useTime = useTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getRechargeDiamonds() {
        return rechargeDiamonds;
    }

    public void setRechargeDiamonds(Integer rechargeDiamonds) {
        this.rechargeDiamonds = rechargeDiamonds;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
