package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 话题分类兴趣标签映射表
 */
@TableName("t_moment_category_interest")
public class MomentCategoryInterestData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer categoryId; //t_moment_topic_category主键id
    private Integer interestId; // 兴趣标签id
    private Integer ctime; // 创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getInterestId() {
        return interestId;
    }

    public void setInterestId(Integer interestId) {
        this.interestId = interestId;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }


}
