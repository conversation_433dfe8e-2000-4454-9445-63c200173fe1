package com.quhong.room.redis;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EnterRoomDetailEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.RedisLockUtils;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.DailyTaskMqData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.UserLevelConstant;
import com.quhong.redis.GameRoomRedis;
import com.quhong.redis.RedisBean;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.TurntableGameRedis;
import com.quhong.service.ShuShuAnalyseService;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.video.redis.VideoRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Lazy
@Component
public class RoomPlayerRedis {
    private static final Logger logger = LoggerFactory.getLogger(RoomPlayerRedis.class);

    private static final long EXPIRE_DAY = 2;

    private static final long ACTOR_EXPIRE_SECOND = 20 * 60;

    @Resource(name = RedisBean.ROOM)
    private StringRedisTemplate redisTemplate;
    @Resource
    private UserLevelTaskService userLevelTaskService;
    @Resource
    private RoomActionRedis roomActionRedis;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private VideoRedis videoRedis;
    @Resource
    private ShuShuAnalyseService shuShuAnalyseService;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private GameRoomRedis gameRoomRedis;

    /**
     * 进入房间
     */
    public void enterRoom(String roomId, String uid, boolean isRobot) {
        // 添加到room
        updateActorInZSet(roomId, uid, isRobot);
        // 设置用户状态
        setActorRoomStatus(roomId, uid);
        // 设置用户进入房间时间
        setActorEnterRoom(roomId, uid);
    }

    private void setActorEnterRoom(String roomId, String uid) {
        String key = getEnterRoomKey(roomId, 0);
        try {
            int nowSeconds = DateHelper.getNowSeconds();
            redisTemplate.opsForHash().put(key, uid, String.valueOf(nowSeconds));

            int gameType = 0;
            if (turntableGameRedis.getGameInfoByRoomId(roomId) != null) {
                gameType = 1;
            }
            if (gameType == 0) {
                gameType = sudGameRedis.getGameTypeByRoomId(roomId);
            }
            if (gameType > 0){
                redisTemplate.opsForHash().put(key, getEnterRoomGameTypeHKey(uid), String.valueOf(gameType));
            }
            redisTemplate.expire(key, 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set actor enter room error. roomId={} uid={}  {}", roomId, uid, e.getMessage(), e);
        }
    }

    private String getEnterRoomSourceHKey(String uid){
        return String.format("enterRoomSource:%s", uid);
    }

    public void setEnterRoomSource(String roomId, String uid, String enterRoomSource) {
        try {
            String key = getEnterRoomKey(roomId, 0);
            redisTemplate.opsForHash().put(key, getEnterRoomSourceHKey(uid), enterRoomSource);
            redisTemplate.expire(key, 2, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setEnterRoomSource error. roomId={} uid={}  {}", roomId, uid, e.getMessage(), e);
        }
    }

    public boolean checkInRoom(String roomId, String uid) {
        if (isActorInRoom(roomId, uid)) {
            return true;
        }
//        removeActorRoomStatus(actorRoomId, uid);
        logger.error("actor is not in room. roomId={} uid={}", roomId, uid);
        return false;
    }

    /**
     * 离开房间
     *
     * @param robot    是否机器人
     * @param roomType 1语聊房间 2直播房间
     */
    public void leaveRoom(String roomId, String uid, boolean robot, int roomType, int reason) {
        removeFromActorZSet(roomId, uid);
        removeActorRoomStatus(roomId, uid);
        // 统计用户进入房间时长
        statActorEnterRoom(roomId, uid, roomType, reason);
        if (robot) {
            removeRobotInZSet(roomId, uid);
        }
    }

    private void statActorEnterRoom(String roomId, String uid, int roomType, int reason) {
        try {
            String key = getEnterRoomKey(roomId, 0);
            Object enterRoomTime = redisTemplate.opsForHash().get(key, uid);
            if (!ObjectUtils.isEmpty(enterRoomTime)) {
                redisTemplate.opsForHash().delete(key, uid);
                int enterRoomTimeInt = Integer.parseInt(String.valueOf(enterRoomTime));
                int nowSeconds = DateHelper.getNowSeconds();
                int onlineTime = nowSeconds - enterRoomTimeInt;
                if (onlineTime > 5 * 60 && ActorUtils.isNewRegisterActor(uid, 7)) {
//                    roomActionRedis.incrStayRoomCount(roomId);
                    roomActionRedis.saveStayRoomRecord(roomId,uid);
                }
                // 用户进入房间事件
                EnterRoomDetailEvent event = new EnterRoomDetailEvent();
                event.setUid(uid);
                event.setRoom_id(roomId);
                event.setRoom_type(roomType);
                event.setIs_host(roomId.contains(uid) ? 1 : 0);
                event.setEnter_time(enterRoomTimeInt);
                event.setExit_time(nowSeconds);
                event.setRoom_online_time(onlineTime);
                event.setExit_room_reason(reason);
                if (RoomUtils.isGameRoom(roomId)) {
                    String gameRoomKey = getEnterGameRoomTypeKey(roomId, 0);
                    Object enterGameRoomType = redisTemplate.opsForHash().get(gameRoomKey, uid);
                    if (!ObjectUtils.isEmpty(enterGameRoomType)) {
                        redisTemplate.opsForHash().delete(gameRoomKey, uid);
                        int enterGameRoomTypeInt = Integer.parseInt(String.valueOf(enterGameRoomType));
                        event.setEnter_game_room_type(enterGameRoomTypeInt);
                    }
                }
                Object enterRoomGameType = redisTemplate.opsForHash().get(key, getEnterRoomGameTypeHKey(uid));
                if (!ObjectUtils.isEmpty(enterRoomGameType)) {
                    redisTemplate.opsForHash().delete(key, getEnterRoomGameTypeHKey(uid));
                    int enterRoomGameTypeInt = Integer.parseInt(String.valueOf(enterRoomGameType));
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("enter_room_game_type", enterRoomGameTypeInt);
                    event.setRoom_info(jsonObject.toJSONString());
                }else {
                    if (RoomUtils.isGameRoom(roomId)) {
                        Integer gameType = gameRoomRedis.getGameRoomAdjustCampaign(uid);
                        if (gameType != null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("enter_room_game_type", gameType);
                            event.setRoom_info(jsonObject.toJSONString());
                        }else {
                            logger.info("gameRoomAdjustCampaign is null. uid={} roomId={}", uid,roomId);
                        }
                    }
                }
                if (reason == 6) {
                    event.setKick_out_uid(roomKickRedis.getFromKickUid(roomId, uid));
                }
                String sourceHKey = getEnterRoomSourceHKey(uid);
                Object enterRoomSource = redisTemplate.opsForHash().get(key, sourceHKey);
                if (!ObjectUtils.isEmpty(enterRoomSource)) {
                    redisTemplate.opsForHash().delete(key, sourceHKey);
                }
                event.setEnter_room_source(!ObjectUtils.isEmpty(enterRoomSource)? String.valueOf(enterRoomSource) : "0-0");
                eventReport.track(new EventDTO(event));

                redisTemplate.opsForHash().increment(getRoomStatKey(0), getRoomStatHashKey(roomId, uid), onlineTime);
                userLevelTaskService.stayRoomTask(new UserLevelTaskData(uid, UserLevelConstant.STAY_ROOM, onlineTime / 60));
                if (videoRedis.getVideoList(roomId) != null) {
                    // 每日任务相关
                    dailyTaskService.sendToMq(new DailyTaskMqData(uid, 16, DateHelper.ARABIAN.formatDateInDay2(), onlineTime));
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, roomId, "", "", CommonMqTaskConstant.WATCH_VIDEO_TIME, onlineTime / 60));
                }
            } else {
                // 尝试获取前一天的入房数据
                key = getEnterRoomKey(roomId, -1);
                enterRoomTime = redisTemplate.opsForHash().get(key, uid);
                if (!ObjectUtils.isEmpty(enterRoomTime)) {
                    redisTemplate.opsForHash().delete(key, uid);
                    // 拆分垮天数据
                    long todayStartTimeSec = DateHelper.DEFAULT.getTodayStartTime() / 1000;
                    int todayOnlineTime = (int) (DateHelper.getNowSeconds() - todayStartTimeSec);
                    int yesterdayOnlineTime = (int) (todayStartTimeSec - Integer.parseInt(String.valueOf(enterRoomTime)));
                    String hashKey = getRoomStatHashKey(roomId, uid);
                    redisTemplate.opsForHash().increment(getRoomStatKey(0), hashKey, todayOnlineTime);
                    redisTemplate.opsForHash().increment(getRoomStatKey(-1), hashKey, yesterdayOnlineTime);
                }
            }
            shuShuAnalyseService.closeYouToBeVideoEvent(roomId, uid, reason == 0 ? 1 : reason == 8 ? 4 : 3);
        } catch (Exception e) {
            logger.error("stat actor enter room error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    /**
     * 更新房间
     */
    public void updateRoomStatus(String roomId, String uid, boolean isRobot) {
        updateActorInZSet(roomId, uid, isRobot);
        updateActorRoomStatus(roomId, uid);
    }

    /**
     * 更新玩家房间状态
     */
    public void dismissRoom(String roomId) {
        removeActorZSet(roomId);
    }

    public Set<String> getRoomActors(String roomId) {
        try {
            String key = getZSetKey(roomId);
            long curTime = DateHelper.getNowSeconds();
            long expireTime = DateHelper.getNowSeconds() - ACTOR_EXPIRE_SECOND;
            return redisTemplate.opsForZSet().rangeByScore(key, expireTime, curTime);
        } catch (Exception e) {
            logger.error("get room actors error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    public boolean isActorInRoom(String roomId, String uid) {
        try {
            String key = getZSetKey(roomId);
            long expireTime = DateHelper.getNowSeconds() - ACTOR_EXPIRE_SECOND;
            Double score = redisTemplate.opsForZSet().score(key, uid);
            return score != null && score.intValue() > expireTime;
        } catch (Exception e) {
            logger.error("is actor in room error. roomId={} uid={}", roomId, uid, e);
        }
        return false;
    }

    /**
     * 获取最早进入房间的n个用户(会随着心跳刷新时间。。。)
     */
    public List<String> getEarliestActors(String roomId, int size) {
        List<String> earliestList = new ArrayList<>();
        Set<ZSetOperations.TypedTuple<String>> set = redisTemplate.opsForZSet().rangeWithScores(getZSetKey(roomId), 0, size - 1);
        if (null == set) {
            return earliestList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : set) {
            if (null == rangeWithScore.getValue()) {
                continue;
            }
            earliestList.add(rangeWithScore.getValue());
        }
        return earliestList;
    }

    public int getRoomActorsCount(String roomId) {
        try {
            Long count = redisTemplate.opsForZSet().zCard(getZSetKey(roomId));
            return null == count ? 0 : count.intValue();
        } catch (Exception e) {
            logger.error("get room actors count error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    public int getRoomRobotCount(String roomId) {
        try {
            Long count = redisTemplate.opsForZSet().zCard(getRobotZSetKey(roomId));
            return null == count ? 0 : count.intValue();
        } catch (Exception e) {
            logger.error("get room robot count error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return 0;
    }

    public Set<String> removeExpireActorsFromZSet(String roomId) {
        String key = getZSetKey(roomId);
        try {
            long expireTime = DateHelper.getNowSeconds() - ACTOR_EXPIRE_SECOND;
            Set<String> roomSet = redisTemplate.opsForZSet().rangeByScore(key, 0, expireTime);
            redisTemplate.opsForZSet().removeRangeByScore(key, 0, expireTime);
            return roomSet;
        } catch (Exception e) {
            logger.error("remove expire actors from zset error. roomId={} {}", roomId, e.getMessage(), e);
        }
        return null;
    }

    private void updateActorInZSet(String roomId, String uid, boolean isRobot) {
        try {
            String key = getZSetKey(roomId);
            redisTemplate.opsForZSet().add(key, uid, DateHelper.getNowSeconds());
            redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
            if (isRobot) {
                updateRobotInZSet(roomId, uid);
            }
        } catch (Exception e) {
            logger.error("add to room error.roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    private void updateRobotInZSet(String roomId, String uid) {
        String key = getRobotZSetKey(roomId);
        redisTemplate.opsForZSet().add(key, uid, DateHelper.getNowSeconds());
        redisTemplate.expire(key, EXPIRE_DAY, TimeUnit.DAYS);
    }

    private void removeRobotInZSet(String roomId, String uid) {
        logger.info("redis. remove robot from room. roomId={} uid={}", roomId, uid);
        try {
            String key = getRobotZSetKey(roomId);
            redisTemplate.opsForZSet().remove(key, uid);
        } catch (Exception e) {
            logger.error("remove robot error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    private void removeFromActorZSet(String roomId, String uid) {
        logger.info("redis. remove from room. roomId={} uid={}", roomId, uid);
        try {
            String key = getZSetKey(roomId);
            redisTemplate.opsForZSet().remove(key, uid);
        } catch (Exception e) {
            logger.error("add to room error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void removeActorZSet(String roomId) {
        logger.info("redis. delete room actor zset. roomId={}", roomId);
        try {
            String key = getZSetKey(roomId);
            redisTemplate.delete(key);
            redisTemplate.delete(getRobotZSetKey(roomId));
        } catch (Exception e) {
            logger.error("update room player error. roomId={} {}", roomId, e.getMessage(), e);
        }
    }

    public int getRoomActorCount(String roomId) {
        try {
            Long size = redisTemplate.opsForZSet().size(getZSetKey(roomId));
            return null == size ? 0 : size.intValue();
        } catch (Exception e) {
            logger.error("get room actor count error. roomId={} {}", roomId, e.getMessage(), e);
            return 0;
        }
    }

    private String getZSetKey(String roomId) {
        return "room_actors_zset_" + roomId;
    }

    private String getRobotZSetKey(String roomId) {
        return "room_robot_zset_" + roomId;
    }

    public String getActorRoomStatus(String uid) {
        String key = getActorKey(uid);
        try {
            return redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            logger.error("get player room status error.  uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

    public List<String> getActorRoomStatusSet(Set<String> uidSet) {
        if (CollectionUtils.isEmpty(uidSet)) {
            return Collections.emptyList();
        }
        List<String> uidList = new ArrayList<>(uidSet);
        List<String> keys = uidList.stream().map(this::getActorKey).collect(Collectors.toList());
        List<String> inRoomUidList = new ArrayList<>();
        int keySize = keys.size();
        int toIndex = 1000;
        List<String> list = new ArrayList<>();
        for (int i = 0; i < keySize; i += 1000) {
            if (i + 1000 > keySize) {
                toIndex = keySize - i;
            }
            list.addAll(Objects.requireNonNull(multiGetInRoomUidSet(keys.subList(i, i + toIndex))));
        }
        for (int i = 0; i < uidList.size(); i++) {
            if (list.get(i) != null) {
                inRoomUidList.add(uidList.get(i));
            }
        }
        return inRoomUidList;
    }

    private List<String> multiGetInRoomUidSet(List<String> keys) {
        try {
            return redisTemplate.opsForValue().multiGet(keys);
        } catch (Exception e) {
            logger.error("get player room set error.{}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 判断用户房间状态是否最近N秒写入
     */
    public boolean actorRoomStatusIsNew(String uid, int second) {
        try {
            Long expire = redisTemplate.getExpire(getActorKey(uid), TimeUnit.SECONDS);
            if (null == expire) {
                return false;
            }
            return expire >= ACTOR_EXPIRE_SECOND - second;
        } catch (Exception e) {
            logger.error("get player room status ttl error.  uid={} {}", uid, e.getMessage(), e);
        }
        return false;
    }

    public void setActorRoomStatus(String roomId, String uid) {
//        logger.info("redis. set actor room status. roomId={} uid={}", roomId, uid);
        String key = getActorKey(uid);
        try {
            redisTemplate.opsForValue().set(key, roomId, ACTOR_EXPIRE_SECOND, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("set actor room status error. uid={} roomId={} {}", uid, roomId, e.getMessage(), e);
        }
    }

    private void updateActorRoomStatus(String roomId, String uid) {
        String key = getActorKey(uid);
        try {
            redisTemplate.opsForValue().set(key, roomId, ACTOR_EXPIRE_SECOND, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("update actor room status error.  roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void removeActorRoomStatus(String roomId, String uid) {
//        logger.info("redis. remove actor room status. roomId={} uid={}", roomId, uid);
        String key = getActorKey(uid);
        try {
            RedisLockUtils.getInstance().compareAndDelete(redisTemplate, key, roomId);
//            redisTemplate.delete(key);
        } catch (Exception e) {
            logger.error("set player room status error. roomId={} uid={} {}", roomId, uid, e.getMessage(), e);
        }
    }

    public void setActorEnterGameRoomType(String roomId, String uid, int enterType) {
        String key = getEnterGameRoomTypeKey(roomId, 0);
        try {
            redisTemplate.opsForHash().put(key, uid, String.valueOf(enterType));
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("set actor enter game room type error. roomId={} uid={}  {}", roomId, uid, e.getMessage(), e);
        }
    }

    private String getActorKey(String uid) {
        return "room_actor_" + uid;
    }

    private String getEnterRoomKey(String roomId, int deltaDay) {
        long startTime = DateHelper.DEFAULT.getDayOffset(deltaDay);
        String date = DateHelper.DEFAULT.formatDateInDay(new Date(startTime));
        return "enter_room_" + date + "_" + roomId;
    }

    private String getRoomStatKey(int deltaDay) {
        long startTime = DateHelper.DEFAULT.getDayOffset(deltaDay);
        String date = DateHelper.DEFAULT.formatDateInDay(new Date(startTime));
        return "room_stat_" + date;
    }

    private String getRoomStatHashKey(String roomId, String uid) {
        return roomId + "_" + uid;
    }

    private String getEnterGameRoomTypeKey(String roomId, int deltaDay) {
        long startTime = DateHelper.DEFAULT.getDayOffset(deltaDay);
        String date = DateHelper.DEFAULT.formatDateInDay(new Date(startTime));
        return "enter_game_room_type" + date + "_" + roomId;
    }

    /**
     * 保存进入房间时房间游戏类型
     */
    private String getEnterRoomGameTypeHKey(String uid) {
        return "game_type_" + uid;
    }
}
