package com.quhong.room;

import com.alibaba.fastjson.JSON;
import com.quhong.core.msg.Msg;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.data.LudoMsgData;
import com.quhong.data.dto.HeartDTO;
import com.quhong.data.dto.MasterLeaveRoomDTO;
import com.quhong.datas.ServerData;
import com.quhong.handler.HttpEnvData;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.ProtoHeader;
import com.quhong.utils.RoomUtils;
import com.quhong.web.msg.WebMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 通过web发送给room模块
 */
@Component
@Lazy
public class RoomWebSender {
    private static final Logger logger = LoggerFactory.getLogger(RoomWebSender.class);

    public static final String ALL_ROOM = "all";

    public static final int ONLY_VOICE_ROOM = -1; // -1只发送给语聊房间

    public static final int ONLY_GAME_ROOM = -2; // -2只发送给游戏房间

    @Autowired
    private WebClient webClient;
    @Autowired
    private RoomWebMsgSender msgSender;

    private RoomWebSender() {

    }

    public void sendLeaveRoom(String roomId, String uid) {
        Map<String, String> params = new HashMap<>();
        params.put("room_id", roomId);
        params.put("uid", uid);
        sendPost("leave_room", roomId, params);
    }

    public void sendForceEnterRoom(String roomId, String uid, int rtcType) {
        Map<String, String> params = new HashMap<>();
        params.put("room_id", roomId);
        params.put("uid", uid);
        params.put("rtcType", String.valueOf(rtcType));
        params.put("force", "true");
        sendPost("enter_room", roomId, params);
    }

    public void roomHeart(String roomId, String uid) {
        HeartDTO dto = new HeartDTO();
        dto.setRoomId(roomId);
        dto.setUid(uid);
        sendPost("master/room_heart", roomId, dto);
    }

    public void playerOnline(String roomId, String uid) {
        HttpEnvData dto = new HttpEnvData();
        dto.setRoomId(roomId);
        dto.setUid(uid);
        sendPost("master/player_online", roomId, dto);
    }

    public void playerOffline(String roomId, String uid) {
        HttpEnvData dto = new HttpEnvData();
        dto.setRoomId(roomId);
        dto.setUid(uid);
        sendPost("master/player_offline", roomId, dto);
    }

    public void leaveRoom(String roomId, String uid, int reason) {
        MasterLeaveRoomDTO dto = new MasterLeaveRoomDTO();
        dto.setRoomId(roomId);
        dto.setUid(uid);
        dto.setReason(reason);
        sendPost("master/leave_room", roomId, dto);
    }

    public HttpResponseData<String> sendPost(String path, String roomId, Object data) {
        String url = msgSender.createUrl(path, roomId);
        if (url == null) {
            logger.error("can not find room serverData. path={}", path);
            return null;
        }
        return webClient.sendRestfulPost(url, JSON.toJSONString(data), null, 1);
    }

    public HttpResponseData<byte[]> sendPush(WebMsg webMsg) {
        // 发送给个人
        if (StringUtils.isEmpty(webMsg.getRoomId())) {
            return sendToSingleByUid("push_msg", webMsg.getToUid(), webMsg.encode());
        } else {
            return send("push_msg", webMsg.getRoomId(), webMsg.encode());
        }
    }

    public void sendPushWithVersion(WebMsg webMsg, int version) {
        send("push_msg?version=" + version, webMsg.getRoomId(), webMsg.encode());
    }

    /**
     * ludo游戏数据下发
     */
    public void sendLudoMsg(String roomId, String fromUid, MarsServerMsg msg, Set<String> uidSet) {
        // 要使用目标server的id
        ServerData serverData = msgSender.getServerData(roomId);

        int toServerId = serverData.getServerId();
        long msgId = Msg.generateMsgId(toServerId);
        msg.getHeader().setMsgId(msgId);
        WebMsg webMsg = new WebMsg();
        webMsg.setCmd(msg.getCmd());
        webMsg.setRoomId(roomId);
        webMsg.setResponseAck(true);
        webMsg.setMsgId(msgId);
        webMsg.setFromUid(fromUid);
        fillProtoHeader(webMsg, msg);
        webMsg.setMsgBody(msg.toBody());
        LudoMsgData ludoMsgData = new LudoMsgData();
        ludoMsgData.setBody(webMsg.encode());
        ludoMsgData.setUidSet(uidSet);
        String url = msgSender.createUrl("push_ludo_msg", roomId);
        if (url == null) {
            logger.info("can not find room serverData");
            return;
        }
        webClient.sendRestfulPost(url, JSON.toJSONString(ludoMsgData), null);
    }

    public String sendPost(String path, String roomId, Map<String, String> params) {
        String url = msgSender.createUrl(path, roomId);
        if (url == null) {
            logger.info("can not create url. path={}", path);
            return null;
        }
        return webClient.sendPost(url, params, 1);
    }

    public HttpResponseData<byte[]> send(String path, String roomId, byte[] body) {
        if (roomId.startsWith(ALL_ROOM)) {
            return sendToAll(path, roomId, body);
        } else {
            return sendToSingle(path, roomId, body);
        }
    }

    private HttpResponseData<byte[]> sendToSingle(String path, String roomId, byte[] body) {
        String url = msgSender.createUrl(path, roomId);
        if (url == null) {
            logger.error("can not create url. path={} body={}", path, body.length);
            return null;
        }
        return webClient.sendPostBytes(url, body);
    }

    private HttpResponseData<byte[]> sendToSingleByUid(String path, String uid, byte[] body) {
        String url = msgSender.createUrl(path, uid);
        if (url == null) {
            logger.error("can not create url. path={} body={}", path, body.length);
            return null;
        }
        return webClient.sendPostBytes(url, body);
    }

    private HttpResponseData<byte[]> sendToAll(String path, String roomId, byte[] body) {
        List<String> urlList = msgSender.createAllUrls(path);
        if (urlList.isEmpty()) {
            logger.error("can not find room serverData. path={} body={}", path, body.length);
            return null;
        }
        for (String url : urlList) {
            webClient.sendPostBytes(url, body);
        }
        return null;
    }

    /**
     * 发送方案消息
     *
     * @param roomId      房间id
     * @param fromUid     发送者
     * @param msg         消息
     * @param responseAck 是否需要回包，如果为true, 按保障性消息发送
     */
    public void sendRoomWebMsg(String roomId, String fromUid, MarsServerMsg msg, boolean responseAck) {
        sendRoomWebMsg(roomId, fromUid, msg, responseAck, 0);
    }

    public void sendRoomWebMsg(String roomId, String fromUid, MarsServerMsg msg, boolean responseAck, int version) {
        // 要使用目标server的id
        int toServerId = msgSender.getServerData(roomId).getServerId();
        long msgId = Msg.generateMsgId(toServerId);
        msg.getHeader().setMsgId(msgId);
        WebMsg webMsg = new WebMsg();
        webMsg.setCmd(msg.getCmd());
        webMsg.setRoomId(roomId);
        webMsg.setFromUid(fromUid);
        webMsg.setResponseAck(responseAck);
        webMsg.setMsgId(msgId);
        fillProtoHeader(webMsg, msg);
        webMsg.setMsgBody(msg.toBody());
        if (version != 0) {
            sendPushWithVersion(webMsg, version);
        } else {
            sendPush(webMsg);
        }
    }


    /**
     * @return 大于0为版本控制，-1只发送给语聊房间，-2只发送给游戏房间
     */
    public int getIsolationVersion(String roomId) {
        int isolationVersion = 0;
        if (RoomUtils.isVoiceRoom(roomId)) {
            isolationVersion = ONLY_VOICE_ROOM;
        } else if (RoomUtils.isGameRoom(roomId)) {
            isolationVersion = ONLY_GAME_ROOM;
        }
        return isolationVersion;
    }

    /**
     * 发送全房间消息
     *
     * @param fromRoomId    触发消息的房间
     * @param roomIsolation 是否进行房间类型隔离
     */
    public void sendAllRoomMsg(String fromRoomId, MarsServerMsg msg, boolean roomIsolation) {
        sendRoomWebMsg(ALL_ROOM, null, msg, false, roomIsolation ? getIsolationVersion(fromRoomId) : 0);
    }


    /**
     * 发送全房间消息，密码房触发的房间房间广播，只在触发的房间展示，其他房间不展示。广播类型：大礼物广播、PK广播
     * 此外，还会过滤测试房间发出的全房间消息只会发往测试房间
     *
     * @param fromRoomId    触发消息的房间
     * @param roomIsolation 是否进行房间类型隔离
     * @see com.quhong.controllers.RoomController#sendToAllRooms(WebMsg, Integer)
     */
    public void sendAllRoomMsgPrivately(String fromRoomId, MarsServerMsg msg, boolean roomIsolation) {
        sendRoomWebMsg(ALL_ROOM + "_" + fromRoomId, null, msg, false, roomIsolation ? getIsolationVersion(fromRoomId) : 0);
    }

    /**
     * 发送全服消息
     * sender -> balance room -> all gate -> all player
     *
     * @param fromUid 发送者，不为空时不发送给发送者
     * @param msg     MarsServerMsg
     */
    public void sendGlobalMsg(String fromUid, MarsServerMsg msg) {
        // 发送到随机一个room服务即可
        ServerData serverData = msgSender.getServerData(fromUid);
        if (null == serverData) {
            return;
        }
        int toServerId = serverData.getServerId();
        long msgId = Msg.generateMsgId(toServerId);
        msg.getHeader().setMsgId(msgId);
        WebMsg webMsg = new WebMsg();
        webMsg.setCmd(msg.getCmd());
        webMsg.setFromUid(fromUid);
        webMsg.setMsgId(msgId);
        fillProtoHeader(webMsg, msg);
        webMsg.setMsgBody(msg.toBody());
        String url = "http://" + serverData.getHost() + ":" + serverData.getHttpPort() + "/api/room/global_msg";
        webClient.sendPostBytes(url, webMsg.encode());
    }

    /**
     * 发送给个人的消息
     *
     * @param roomId      房间id
     * @param fromUid     发送者
     * @param toUid       接受者
     * @param msg         消息
     * @param responseAck 是否需要回包，如果为true, 按保障性消息发送
     */
    public void sendPlayerWebMsg(String roomId, String fromUid, String toUid, MarsServerMsg msg, boolean responseAck) {
        // 要使用目标server的id
        int toServerId;
        if (StringUtils.hasLength(roomId)) {
            toServerId = msgSender.getServerData(roomId).getServerId();
        } else {
            toServerId = msgSender.getServerData(toUid).getServerId();
        }
        long msgId = Msg.generateMsgId(toServerId);
        msg.getHeader().setMsgId(msgId);
        WebMsg webMsg = new WebMsg();
        webMsg.setCmd(msg.getCmd());
        webMsg.setFromUid(fromUid);
        webMsg.setToUid(toUid);
        webMsg.setRoomId(roomId);
        webMsg.setResponseAck(responseAck);
        webMsg.setMsgId(msgId);
        fillProtoHeader(webMsg, msg);
        webMsg.setMsgBody(msg.toBody());
        sendPush(webMsg);
    }

    private void fillProtoHeader(WebMsg webMsg, MarsServerMsg msg) {
        ProtoHeader protoHeader = msg.getProtoHeader();
        protoHeader.setFromUid(webMsg.getFromUid());
        protoHeader.setRoomID(getRoomId(webMsg.getRoomId()));
        protoHeader.setResponseAck(webMsg.isResponseAck());
        protoHeader.setMsgId(webMsg.getMsgId());
    }

    private String getRoomId(String roomId) {
        if (null == roomId || roomId.startsWith(ALL_ROOM)) {
            return "";
        }
        return roomId;
    }
}
