<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.quhong</groupId>
    <artifactId>ustar_java_core</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <!-- 并发任务的封装，包含任务队列，线程池，时间服务，分布式锁，分布式凭证等 -->
        <module>quhong_task</module>
        <!-- 基于netty封装的TCP网络框架，包含server和client-->
        <module>quhong_netty</module>
        <!-- mq -->
        <module>quhong-common-mq</module>
        <!-- protobuf文件 -->
        <module>ustar_message</module>
        <!-- ustar基础部分, 包含redis的一些处理，为ustar所有公共部分  -->
        <module>ustar_base</module>
        <!-- web服务的公共基础部分 -->
        <module>ustar_web</module>
        <!-- ustar数据处理部分，包含基于mysql，mongodb的所有公共业务 -->
        <module>ustar_data</module>
        <!-- 客户端接口调用 -->
        <module>ustar_api</module>
        <!-- 客户端接口调用(feign) -->
        <module>ustar_feign</module>
        <!-- 数据报表分析 -->
        <module>ustar_analysis</module>
        <!-- k8s -->
        <module>ustar_k8s</module>
    </modules>
    <name>ustar_java_core</name>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <ustar.core.version>1.0-SNAPSHOT</ustar.core.version>
        <spring.version>5.1.4.RELEASE</spring.version>
        <spring-boot.version>2.2.13.RELEASE</spring-boot.version>
        <fastJson.version>1.2.83</fastJson.version>
        <logback.version>1.1.3</logback.version>
        <spring-data-redis.version>2.1.0.RELEASE</spring-data-redis.version>
        <jedis.version>2.9.0</jedis.version>
        <http.client.version>4.5.7</http.client.version>
        <http.nio.version>4.4.14</http.nio.version>
        <http.nio.client.version>4.1.4</http.nio.client.version>
        <maven-jar-plugin.versioin>2.5</maven-jar-plugin.versioin>
        <maven-install-plugin.version>2.5.2</maven-install-plugin.version>
        <protobuf.version>3.7.1</protobuf.version>
        <netty.version>4.1.33.Final</netty.version>
        <mybatis-spring.version>2.0.6</mybatis-spring.version>
        <druid.version>1.1.4</druid.version>
        <caffeine.version>2.9.0</caffeine.version>
        <mysql.version>8.0.15</mysql.version>
        <mybatis.version>3.5.0</mybatis.version>
        <mongodb.version>2.1.4.RELEASE</mongodb.version>
        <spring.rabbitmq.version>2.2.9.RELEASE</spring.rabbitmq.version>
        <mybatis-plus.version>3.4.2</mybatis-plus.version>
    </properties>
    <build>
        <pluginManagement>
            <plugins>
                <!--设置Java编译级别 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.3</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!--打包生成源码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
