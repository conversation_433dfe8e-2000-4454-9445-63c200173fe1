package com.quhong.analysis;

/**
 * 用户进入房间事件
 */
public class EnterRoomDetailEvent extends UserEvent {

    public String room_id; // 房间id
    public int is_host; // 是否为房主 0为不是，1为是
    public int rookie_status; // 是否为迎新房 0为不是，1为是
    public int enter_time; // 进入房间时间 同时将其作为数数的event_time
    public int exit_time; // 退出房间时间
    public int room_type; // 房间类型 1为语聊房，2为直播房
    public int room_online_time; // 本次房间在线时间
    public int exit_room_reason; // 离开原因
    public String kick_out_uid; // 当本次退出房间原因是被人踢出时，上报踢人的用户uid
    private int enter_game_room_type; // 游戏房进入方式 0 未知
    private String enter_room_source; // 进房来源
    private String room_info; // 房间信息


    @Override
    public String getEventName() {
        return "enter_room_detail";
    }

    @Override
    public int getEventTime() {
        return enter_time;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public int getIs_host() {
        return is_host;
    }

    public void setIs_host(int is_host) {
        this.is_host = is_host;
    }

    public int getRookie_status() {
        return rookie_status;
    }

    public void setRookie_status(int rookie_status) {
        this.rookie_status = rookie_status;
    }

    public int getEnter_time() {
        return enter_time;
    }

    public void setEnter_time(int enter_time) {
        this.enter_time = enter_time;
    }

    public int getExit_time() {
        return exit_time;
    }

    public void setExit_time(int exit_time) {
        this.exit_time = exit_time;
    }

    public int getRoom_type() {
        return room_type;
    }

    public void setRoom_type(int room_type) {
        this.room_type = room_type;
    }

    public int getRoom_online_time() {
        return room_online_time;
    }

    public void setRoom_online_time(int room_online_time) {
        this.room_online_time = room_online_time;
    }

    public int getExit_room_reason() {
        return exit_room_reason;
    }

    public void setExit_room_reason(int exit_room_reason) {
        this.exit_room_reason = exit_room_reason;
    }

    public String getKick_out_uid() {
        return kick_out_uid;
    }

    public void setKick_out_uid(String kick_out_uid) {
        this.kick_out_uid = kick_out_uid;
    }

    public int getEnter_game_room_type() {
        return enter_game_room_type;
    }

    public void setEnter_game_room_type(int enter_game_room_type) {
        this.enter_game_room_type = enter_game_room_type;
    }

    public String getEnter_room_source() {
        return enter_room_source;
    }

    public void setEnter_room_source(String enter_room_source) {
        this.enter_room_source = enter_room_source;
    }
    public String getRoom_info() {
        return room_info;
    }

    public void setRoom_info(String room_info) {
        this.room_info = room_info;
    }
}
