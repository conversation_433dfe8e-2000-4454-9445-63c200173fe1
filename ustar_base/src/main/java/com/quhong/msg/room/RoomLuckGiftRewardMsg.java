package com.quhong.msg.room;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.annotation.Message;
import com.quhong.enums.Cmd;
import com.quhong.msg.MarsServerMsg;
import com.quhong.msg.obj.LuckyGiftRewardObject;
import com.quhong.proto.YoustarProtoLuckyGift;
import com.quhong.proto.YoustarProtoRoom;

import java.util.ArrayList;
import java.util.List;

@Message(cmd = Cmd.ROOM_LUCK_GIFT_REWARD_MESSAGE)
public class RoomLuckGiftRewardMsg extends MarsServerMsg {
    private String aid;
    private int a_type;//是否是坐骑礼物，0否，1是
    private int type;//1幸运礼物，2抽奖礼物
    private List<LuckyGiftRewardObject> lucky_gift_reward;

    @Override
    public void fillFrom(JSONObject data) {
        this.aid = data.getString("aid");
        this.a_type = data.getIntValue("a_type");
        this.type = data.getIntValue("type");
        JSONArray jsonArray = data.getJSONArray("lucky_gift_reward");
        if (jsonArray != null) {
            lucky_gift_reward = new ArrayList<>();
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                LuckyGiftRewardObject luckyGiftRewardObject = new LuckyGiftRewardObject();
                luckyGiftRewardObject.fillFrom(jsonObject);
                lucky_gift_reward.add(luckyGiftRewardObject);
            }
        }
    }

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        YoustarProtoRoom.RoomLuckGiftRewardMessage msg = YoustarProtoRoom.RoomLuckGiftRewardMessage.parseFrom(bytes);
        this.protoHeader.doFromBody(msg.getHeader());
        this.aid = msg.getAid();
        this.a_type = msg.getAType();
        this.type = msg.getType();
        this.lucky_gift_reward = new ArrayList<>();
        for (YoustarProtoLuckyGift.LuckyGiftReward luckyGiftReward : msg.getLuckyGiftRewardList()) {
            LuckyGiftRewardObject luckyGiftRewardObject = new LuckyGiftRewardObject();
            luckyGiftRewardObject.doFromBody(luckyGiftReward);
            this.lucky_gift_reward.add(luckyGiftRewardObject);
        }
    }

    @Override
    protected byte[] doToBody() throws Exception {
        YoustarProtoRoom.RoomLuckGiftRewardMessage.Builder builder = YoustarProtoRoom.RoomLuckGiftRewardMessage.newBuilder();
        builder.setHeader(this.protoHeader.doToBody());
        builder.setAid(aid == null ? "" : aid);
        builder.setAType(a_type);
        builder.setType(type);
        if (this.lucky_gift_reward != null) {
            for (LuckyGiftRewardObject luckyGiftRewardObject : lucky_gift_reward) {
                builder.addLuckyGiftReward(luckyGiftRewardObject.doToBody());
            }
        }
        return builder.build().toByteArray();
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public int getA_type() {
        return a_type;
    }

    public void setA_type(int a_type) {
        this.a_type = a_type;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<LuckyGiftRewardObject> getLucky_gift_reward() {
        return lucky_gift_reward;
    }

    public void setLucky_gift_reward(List<LuckyGiftRewardObject> lucky_gift_reward) {
        this.lucky_gift_reward = lucky_gift_reward;
    }
}
