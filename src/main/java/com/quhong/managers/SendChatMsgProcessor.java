package com.quhong.managers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.*;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.constant.FriendConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.dailyTask.UserLevelTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.NewGreetData;
import com.quhong.data.UserLevelTaskData;
import com.quhong.data.dto.FriendApplyDTO;
import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.data.vo.SendMsgVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.SendGiftDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.fcm.FCMPusher;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFcmService;
import com.quhong.feign.IFriendService;
import com.quhong.feign.IGiftService;
import com.quhong.handler.WebController;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.Actor;
import com.quhong.mongo.data.FriendData;
import com.quhong.mongo.data.MsgListData;
import com.quhong.msg.chat.PrivateChatTipMsg;
import com.quhong.mysql.dao.MysqlMsgRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.redis.BlockRedis;
import com.quhong.redis.NewGreetRedis;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.redis.UserInterceptionRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.service.ActorCommonService;
import com.quhong.service.ActorService;
import com.quhong.service.GreetUserService;
import com.quhong.service.mysql.BadWordService;
import com.quhong.user.CustomerServiceUser;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.MsgUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class SendChatMsgProcessor extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(SendChatMsgProcessor.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    public static final List<Integer> UN_RECEIVE_MSG_TYPE_LIST = Arrays.asList(MsgType.SHARE_ROOM, MsgType.SHARE_ROOM_EVENT, MsgType.SHARE_ACTIVITY);
    public static final List<String> UN_RECEIVE_MSG_TYPE_UID_LIST = ServerConfig.isProduct() ? Arrays.asList("683ed399dfd1490db192756d") : Arrays.asList("6891b09cb0bddc1977a47365", "65e6c0ba368c6b63b860c682");

    public static final int IMG_LIMIT_SIZE = 100 * 1024;
    public static final int THREE_DAY_TIME = 3 * 24 * 3600;

    // 聊天轮次提示推送
    public static final int NEW_GREET_BECOME_FRIEND_ROUND = 2;
    public static final int GREET_SEND_MAX_COUNT = 3;
    public static final int GREET_BECOME_FRIEND_ROUND = 3;
    public static final String CHAT_BECOME_FRIEND_EN = "Continue chatting and automatically become friends, unlocking unlimited chat";
    public static final String CHAT_BECOME_FRIEND_AR = "واصل الدردشة وكن صديقًا تلقائيًا، وافتح دردشة غير محدودة";

    public static final String SEND_HEART_RECORD_TITLE = "send gift to friends";
    public static final String HEART_RECORD_REMARK = "friend gift";
    public static final String RECEIVE_HEART_RECORD_TITLE = "receive gift by friends";
    public static final String PY_SEND_MSG_TO_WS_CHANNEL = "send_msg_to_ws";

    @Autowired
    private UserMonitorDao userMonitorDao;
    @Autowired
    private DailyTaskService dailyTaskService;
    @Autowired
    private UserLevelTaskService userLevelTaskService;
    @Autowired
    private ChatMsgMgr chatMsgMgr;
    @Autowired
    private MysqlMsgRecordDao recordDao;
    @Autowired
    private MsgListDao msgListDao;
    @Resource
    private BlackListDao blackListDao;
    @Resource
    private FriendDao friendDao;
    @Resource
    private ActorDao actorDao;
    @Resource
    private ActorService actorService;
    @Resource
    private BlockRedis blockRedis;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private BadWordService badWordService;
    @Resource
    private IGiftService IGiftService;
    @Resource
    private IDetectService detectService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private UserInterceptionRedis userInterceptionRedis;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private IFcmService iFcmService;
    @Resource
    private ActorCommonService actorCommonService;
    @Resource
    private NewGreetRedis newGreetRedis;
    @Resource
    private IFriendService iFriendService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private GreetUserService greetUserService;

    /**
     * 历史原因。返回异常时不能走加密，需要使用CommonH5Exception
     */
    public SendMsgVO send(MsgSendDTO reqData) {
        // 检查参数
        sendMsgCheck(reqData);


        if (reqData.getMsg_type() == MsgType.SEND_EMOJI) {
            doEmoticonSendEvent(reqData.getUid(), reqData.getMsg_body(), EmoticonSendEvent.SEND_SCENE_3);
        }
        SendMsgVO sendMsgVO = doSendMsg(reqData);
        // 私聊消息的埋点数据
        MsgRecordEvent event = new MsgRecordEvent();
        event.setUid(reqData.getUid());
        event.setFrom_uid(reqData.getUid());
        event.setTo_uid(reqData.getAid());
        event.setMsg_type(reqData.getMsg_type());
        event.setCtime(DateHelper.getNowSeconds());
        if (reqData.getGreetChat() > 0) {
            JSONObject msgInfo = new JSONObject();
            msgInfo.put("send_scene", "social-say hi");
            event.setMsg_info(msgInfo.toJSONString());
        }
        eventReport.track(new EventDTO(event));
        return sendMsgVO;
    }

    public void sendMsgCheck(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        String aid = reqData.getAid();
        int msgType = reqData.getMsg_type();
        if (reqData.getIsSayHello() > 0) {
            logger.info("send chat msg. can not say hello. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (reqData.getMsg_info() == null) {
            reqData.setMsg_info(new JSONObject());
        }
        msgLogger.info("start send chat msg. msgType={} msgBody={} msgInfo={} aid={} uid={} versioncode={}", reqData.msg_type, reqData.msg_body, reqData.msg_info, reqData.aid, reqData.getUid(), reqData.getVersioncode());
        if (reqData.getMsg_body().length() >= 1024) {
            logger.info("send chat msg. msg too long. uid={}", uid);
            reqData.setMsg_body(reqData.getMsg_body().substring(0, 1023) + "...");
        }
        ApiResult<String> block = checkBlock(reqData);
        if (block.isError()) {
            logger.info("send chat msg. actor has been blocked. uid={}", uid);
            throw new CommonH5Exception(block.getCode());
        }
        if (!userMonitorDao.isSendChatMsg(uid)) {
            logger.info("send chat msg. actor has been frozen. or banned. uid={}", uid);
            throw new CommonH5Exception(HttpCode.USER_MONITOR_FREEZE);
        }
        if (!userMonitorDao.isSendChatMsg(reqData.aid)) {
            logger.info("send chat msg. to user has been frozen. or banned. aid={} uid={}", reqData.aid, uid);
            throw new CommonH5Exception(MsgHttpCode.TO_USER_FREEZE);
        }
        if (blackListDao.isBlock(reqData.aid, uid)) {
            logger.info("send chat msg. they are blocked. aid={} uid={}", reqData.aid, uid);
            throw new CommonH5Exception(MsgHttpCode.BLACK);
        }
        boolean isCanChat = null != friendDao.findDataFromCache(reqData.getUid(), reqData.getAid());
        if (CustomerServiceUser.getUid().equals(reqData.getAid())) {
            // 如果是客服，可以聊天
            isCanChat = true;
        }
        // 如果是打招呼消息且被打招呼的人已经更新866版本，可以聊天
        if (reqData.getGreetChat() > 0) {
            if (actorCommonService.getRejectGreetStatus(aid) > 0) {
                throw new CommonH5Exception(MsgHttpCode.REJECT_GREET);
            }
            MsgListData msgListData = msgListDao.findMsgListData(MsgUtils.generateMsgIndex(uid, aid));
            if (msgListData != null) {
                if (msgListData.getNewGreet() == 1) {
                    isCanChat = true;
                } else {
                    reqData.setGreetChat(0);
                }
            } else {
                isCanChat = true;
            }
        }

        if (!isCanChat) {
            logger.info("send chat msg. they are not friend. aid={} uid={}", reqData.aid, uid);
            throw new CommonH5Exception(MsgHttpCode.NOT_FRIEND);
        }
        if (msgType == MsgType.TEXT && badWordService.isContainBadWord(reqData.getMsg_body(), BadWordService.MIN_MATCH_TYPE)) {
            logger.info("send msg contain dirty text. can not send text msg. uid={} aid={} msg_body={}", reqData.getUid(), reqData.getAid(), reqData.getMsg_body());
            throw new CommonH5Exception(MsgHttpCode.MSG_CONTAINS_BAD_WORD);
        }

        boolean sendFlag = msgType == MsgType.TEXT || msgType == MsgType.AUDIO || msgType == MsgType.IMAGE || msgType == MsgType.SEND_EMOJI || msgType == MsgType.SEND_PRIVATE_ACTION;
        // 消息拦截不能发送消息
        if (sendFlag && userInterceptionRedis.getUserInterceptionStatus(reqData.getAid(), uid) > 0) {
            logger.info("send msg UserInterceptionStatus. uid={} aid={} msg_body={}", reqData.getUid(), reqData.getAid(), reqData.getMsg_body());
            throw new CommonH5Exception(MsgHttpCode.AID_REFUSE);
        }

        // 被运营人员禁言不能发送消息
        if (sendFlag && blockRedis.getBlockUserPrivateMsgStatus(uid) != 0) {
            logger.info("send msg getBlockUserPrivateMsgStatus. uid={} aid={} msg_body={}", reqData.getUid(), reqData.getAid(), reqData.getMsg_body());
            throw new CommonH5Exception(MsgHttpCode.ACCOUNT_HAS_DISABLED);
        }
    }


    private void doEmoticonSendEvent(String uid, String iconFile, String scene) {
        EmoticonSendEvent event = new EmoticonSendEvent();
        event.setRoom_id("");
        event.setUid(uid);
        event.setScene(scene);
        event.setCtime(DateHelper.getNowSeconds());
        if (!ObjectUtils.isEmpty(iconFile)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("emoticon_file_name", iconFile);
            event.setOther_info(jsonObject.toJSONString());
        }
        eventReport.track(new EventDTO(event));
    }

    /**
     * 提供给内部使用
     */
    public SendMsgVO doSendMsg(MsgSendDTO reqData) {
        switch (reqData.msg_type) {
            case MsgType.IMAGE:
                return sendImage(reqData);
            case MsgType.AUDIO:
                return sendAudio(reqData);
            case MsgType.GIFT:
                return sendGift(reqData);
            case MsgType.TIP:
                return sendTip(reqData);
            default:
                return doSend(reqData);
        }
    }

    private SendMsgVO sendAudio(MsgSendDTO reqData) {
        checkAndRepairMsgInfo(reqData);
        String uid = reqData.getUid();
        String url = getUploadOssUrl(reqData);
        logger.info("send audio. url={}  uid={}", url, uid);
        reqData.setMsg_body(url);
        return doSend(reqData);
    }

    private SendMsgVO sendGift(MsgSendDTO reqData) {
        return doSend(reqData);
    }

    private SendMsgVO sendTip(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        // 写入数据库
        MysqlMsgRecordData recordData = saveToDB(reqData);
        int unread = getUnReadNum(recordData, uid);
        // im发送消息
        chatMsgMgr.sendMsg(recordData, unread);
        return getMsgRsp(reqData, recordData);
    }

    private SendMsgVO doSend(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        int msgType = reqData.getMsg_type();
        // 普通礼物，内部调用已经处理了礼物发送
        if (!reqData.isInner() && msgType == MsgType.GIFT) {
            HttpResult<?> httpResult = doSendGiftApi(reqData);
            if (null == httpResult) {
                logger.error("msg send_gift error. uid={} aid={}", reqData.getUid(), reqData.getAid());
                throw new CommonH5Exception(MsgHttpCode.SERVER_ERROR);
            }
            if (httpResult.isError()) {
                logger.info("msg send_gift error. uid={} aid={} code={} msg={}", reqData.getUid(), reqData.getAid(), httpResult.getCode(), httpResult.getMsg());
                if (httpResult.getCode() == 63) {
                    // 解决内部调用提示语默认为阿语问题
                    throw new CommonException(MsgHttpCode.VIP_LEVEL_IS_NOT_HIGH_ENOUGH_TO_SEND);
                }
                throw new CommonH5Exception(new HttpCode(httpResult.getCode(), httpResult.getMsg()));
            }
        }
        // 打招呼聊天
        if (reqData.getGreetChat() > 0) {
            doGreetChat(reqData);
        }
        MysqlMsgRecordData recordData = saveToDB(reqData);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                // 每日任务
                dailyTaskService.sendChatMsg(uid);
                if (msgType != MsgType.GIFT) {
                    // 用户等级任务
                    userLevelTaskService.sendTaskDataToMq(new UserLevelTaskData(uid, UserLevelConstant.SEND_MSG, reqData.getAid()));

                    // boolean isReplay = false;
                    // String today = DateHelper.ARABIAN.formatDateInDay();
                    // if (userInterceptionRedis.isDayReplaySetData(today, uid, reqData.getAid())) {
                    //     isReplay = false;
                    // } else {
                    //     String msgIndex = MsgUtils.generateMsgIndex(uid, reqData.aid);
                    //     MysqlMsgRecordData msgRecordData = recordDao.getFirstData(msgIndex, reqData.aid, uid);
                    //     if (msgRecordData != null) {
                    //         long sendTime = msgRecordData.getTimestamp();
                    //         long startTime = DateHelper.ARABIAN.getTodayStartTime();
                    //         long endTime = startTime + TimeUnit.DAYS.toMillis(1);
                    //         if (sendTime >= startTime && sendTime < endTime) {
                    //             isReplay = true;
                    //             userInterceptionRedis.addDayReplaySetData(today, uid, reqData.getAid());
                    //         }
                    //     }
                    // }
                    // if (isReplay) {
                    //     commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", reqData.getAid(), "", CommonMqTaskConstant.REPLAY_PRIVATE_MSG, 1));
                    //     commonTaskService.sendCommonTaskMq(new CommonMqTopicData(reqData.getAid(), "", uid, "", CommonMqTaskConstant.REPLAY_PRIVATE_MSG, 1));
                    // }
                    commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", reqData.getAid(), reqData.getAid(), CommonMqTaskConstant.SEND_PRIVATE_MSG, 1));
                }
                if (!reqData.isBatchSend()) {
                    sendToFCM(recordData);
                }
            }
        });
        // 更新msgList
        int unread = updateMsgList(recordData, reqData);
        // im发送消息
        chatMsgMgr.sendMsg(recordData, unread);
        return getMsgRsp(reqData, recordData);
    }

    /**
     * 处理打招呼业务拦截
     * A msg=3  round=1
     * B msg=3  round=1
     */
    private void doGreetChat(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        String aid = reqData.getAid();
        NewGreetData newGreetData = newGreetRedis.getUserNewGreetConfig(uid, aid);
        boolean isFirstSend = false;
        if (ObjectUtils.isEmpty(newGreetData)) {
            newGreetData = new NewGreetData(ActorUtils.generateMsgIndex(uid, aid), uid, 0, 0, 0, 0, 0, DateHelper.getNowSeconds());
            isFirstSend = true;
        }
        if (uid.compareTo(aid) < 0) {
            // 次数限制
            if (newGreetData.getSendMsgA() >= GREET_SEND_MAX_COUNT) {
                throw new CommonH5Exception(MsgHttpCode.GREET_SEND_MAX);
            }

            // 增加A用户轮次
            if (newGreetData.getRoundNumA() == newGreetData.getConversationNum()) {
                newGreetData.setRoundNumA(newGreetData.getRoundNumA() + 1);
                newGreetData.setSendMsgB(0);
            }

            // 增加回复轮次
            updateConversationData(newGreetData, uid, aid);
            newGreetData.setSendMsgA(newGreetData.getSendMsgA() + 1);
            // 成为好友或保存数据
            handleGreetResult(newGreetData, uid, aid);
        } else {
            // 次数限制
            if (newGreetData.getSendMsgB() >= GREET_SEND_MAX_COUNT) {
                throw new CommonH5Exception(MsgHttpCode.GREET_SEND_MAX);
            }

            // 增加B用户轮次
            if (newGreetData.getRoundNumB() == newGreetData.getConversationNum()) {
                newGreetData.setRoundNumB(newGreetData.getRoundNumB() + 1);
                newGreetData.setSendMsgA(0);
            }

            // 增加回复轮次
            updateConversationData(newGreetData, uid, aid);
            newGreetData.setSendMsgB(newGreetData.getSendMsgB() + 1);
            // 成为好友或保存数据
            handleGreetResult(newGreetData, uid, aid);
        }
        if (isFirstSend) {
            // 主动发私聊打招呼
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", reqData.getAid(), newGreetData.getGreetUid(),
                    CommonMqTaskConstant.GREETING_PRIVATE_MSG, 1));
            doImportantMsgRecordEvent(reqData, "0", null);
        } else {
            // 2 私聊打招呼主人发消息  3 接收人回复打招呼
            int type = uid.equals(newGreetData.getGreetUid()) ? 2 : 3;
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", reqData.getAid(), newGreetData.getGreetUid(),
                    CommonMqTaskConstant.GREETING_PRIVATE_MSG, type));

            if (newGreetData.getConversationNum() == 1
                    && newGreetData.getRoundNumA() == 1
                    && newGreetData.getRoundNumB() == 1
                    && type == 3
                    && newGreetData.getCtime() > 0) {
                boolean isAReplayUser = true;
                if (newGreetData.getGreetUid().compareTo(uid) < 0) {
                    // A 是主人
                    isAReplayUser = false;
                }
                if ((isAReplayUser && newGreetData.getSendMsgA() == 1) || (!isAReplayUser && newGreetData.getSendMsgB() == 1)) {
                    int replyDuration = DateHelper.getNowSeconds() - newGreetData.getCtime();
                    doImportantMsgRecordEvent(reqData, "1", replyDuration);
                }
            }

        }
    }


    private void doImportantMsgRecordEvent(MsgSendDTO reqData, String msgDesc, Integer replyDuration) {
        String uid = reqData.getUid();
        String aid = reqData.getAid();

        ImportantMsgRecordEvent event = new ImportantMsgRecordEvent();
        event.setUid(uid);
        event.setFrom_uid(uid);
        event.setTo_uid(aid);
        event.setMsg_scene("say hi");
        event.setMsg_sub_scene(reqData.getGreetChat() == 2 ? "0" : "1");
        event.setMsg_desc(msgDesc);
        event.setCtime(DateHelper.getNowSeconds());
        if (replyDuration != null) {
            JSONObject msgInfo = new JSONObject();
            msgInfo.put("reply_duration", replyDuration);
            event.setMsg_info(msgInfo.toJSONString());
        }
        eventReport.track(new EventDTO(event));
    }

    /**
     * 处理打招呼结果：成为好友或保存数据
     */
    private void handleGreetResult(NewGreetData newGreetData, String uid, String aid) {
        if (newGreetData.getConversationNum() >= GREET_BECOME_FRIEND_ROUND) {
            // 达到成为好友的轮次，自动成为好友
            FriendApplyDTO dto = new FriendApplyDTO();
            dto.setUid(uid);
            dto.setAid(aid);
            dto.setFriendSource(FriendConstant.SOURCE_SAY_HELLO);
            iFriendService.becomeFriend(dto);
            newGreetRedis.removeUserNewGreet(uid, aid);
            msgListDao.updateNormalChat(uid, aid);
        } else {
            // 保存打招呼数据
            newGreetRedis.setUserNewGreet(uid, aid, JSONObject.toJSONString(newGreetData));
        }
        greetUserService.updateGreetReplyRate(uid, aid);
    }

    private void updateConversationData(NewGreetData newGreetData, String uid, String aid) {
        if (newGreetData.getRoundNumA() > 0 && newGreetData.getRoundNumB() > 0 && newGreetData.getRoundNumA() == newGreetData.getRoundNumB() && !uid.equals(newGreetData.getGreetUid())) {
            newGreetData.setConversationNum(newGreetData.getConversationNum() + 1);
            newGreetData.setSendMsgA(0);
            newGreetData.setSendMsgB(0);
            // 聊天成为好友提示
            if (newGreetData.getConversationNum() == NEW_GREET_BECOME_FRIEND_ROUND) {
                roomWebSender.sendPlayerWebMsg("", uid, uid, new PrivateChatTipMsg(uid, aid, CHAT_BECOME_FRIEND_EN, CHAT_BECOME_FRIEND_AR), false);
                roomWebSender.sendPlayerWebMsg("", aid, aid, new PrivateChatTipMsg(aid, uid, CHAT_BECOME_FRIEND_EN, CHAT_BECOME_FRIEND_AR), false);
            }
        }
    }

    private void sendToFCM(MysqlMsgRecordData recordData) {
        ActorData fromActor = actorDao.getActorDataFromCache(recordData.getFromUid());
        ActorData toActor = actorDao.getActorDataFromCache(recordData.getToUid());
        String fromName = fromActor.getName();
        String fcmMessageId = new ObjectId().toString();
        Map<String, String> paramMap = new HashMap<>();
        // 设置消息体
        String body = getFcmMsgBody(recordData.getMsg(), recordData.getMsgType(), toActor.getSlang());
        // 消息来源配置
        paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, fcmMessageId);
        paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
        paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, fromName);

        // 跳转配置
        paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.PRIVATE_DETAIL_MSG);
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, recordData.getFromUid());
        paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));
        iFcmService.sendFcmMsg(new SendFcmDTO(recordData.getToUid(), paramMap, fromActor.getName(), fromActor.getName(), body, body, ImageUrlGenerator.generateRoomUserUrl(fromActor.getHead())));
    }

    public void batchSendToFCM(String fromUid, Set<String> aidSet, String msg, int msgType) {
        if (ObjectUtils.isEmpty(aidSet)) {
            return;
        }
        ActorData fromActor = actorDao.getActorDataFromCache(fromUid);
        String fromName = fromActor.getName();
        String fromHead = ImageUrlGenerator.generateRoomUserUrl(fromActor.getHead());
        Map<String, String> paramMap = new HashMap<>();
        String fcmMessageId = new ObjectId().toString();
        paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, fcmMessageId);
        paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
        paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, fromName);
        paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.PRIVATE_DETAIL_MSG);
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, fromUid);
        paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));

        String bodyEn = getFcmMsgBody(msg, msgType, SLangType.ENGLISH);
        String bodyAr = getFcmMsgBody(msg, msgType, SLangType.ARABIC);
        iFcmService.sendFcmMsg(new SendFcmDTO(aidSet, paramMap, fromActor.getName(), fromActor.getName(), bodyEn, bodyAr, fromHead));
    }

    private String getFcmMsgBody(String msg, int msgType, int slang) {
        String body;
        switch (msgType) {
            case MsgType.TEXT:
                body = msg;
                break;
            case MsgType.AUDIO:
                body = SLangType.ENGLISH == slang ? "Sent a voice message" : "ارسل رسالة صوتية";
                break;
            case MsgType.IMAGE:
                body = SLangType.ENGLISH == slang ? "Sent a photo" : "ارسل صورة";
                break;
            case MsgType.GIFT:
                body = SLangType.ENGLISH == slang ? "Sent you a gift" : "تم وصول علي الهدية";
                break;
            case MsgType.REPOST_MOMENT:
                body = SLangType.ENGLISH == slang ? "Shared a Moment" : "شارك معك غرفة";
                break;
            case MsgType.SHARE_ROOM:
                body = SLangType.ENGLISH == slang ? "Shared to you a room" : "شارك معك غرفة";
                break;
            case MsgType.SEND_RESOURCES:
                body = "Send you a resource as a gift.";
                break;
            case MsgType.SHARE_FAMILY:
                body = SLangType.ENGLISH == slang ? "Shared a family" : "مشاركة العائلة";
                break;
            case MsgType.SEND_EMOJI:
            case MsgType.SEND_PRIVATE_ACTION:
                body = SLangType.ENGLISH == slang ? "send a emoji message" : "مشاركة تعابير الوجه";
                break;
            case MsgType.SHARE_GAME_ROOM:
                body = SLangType.ENGLISH == slang ? "Shared to you a game room" : "شارك معك غرفة";
                break;
            default:
                body = SLangType.ENGLISH == slang ? "sent a message" : "ارسل ر صوتية";
                break;
        }
        return body;
    }

    private SendMsgVO getMsgRsp(MsgSendDTO reqData, MysqlMsgRecordData recordData) {
        SendMsgVO sendMsgVO = new SendMsgVO(recordData.getMsgId());
        if (reqData.getMsg_type() == MsgType.GIFT || reqData.getMsg_type() == MsgType.HEART_GIFT) {
            Actor actor = actorService.getActor(reqData.getUid());
            int beans = actor.getBeans();
            int heart = actor.getHeartGot();
            sendMsgVO.setBeans(beans);
            sendMsgVO.setHeart(heart);
        }
        return sendMsgVO;
    }

    private void doFcmReportEvent(String fcmMessageId, String uid, String fcmType, String fcmTitle) {
        FcmPushEvent event = new FcmPushEvent();
        event.setCtime(DateHelper.getNowSeconds());
        event.setUid(uid);
        event.setFcm_message_id(fcmMessageId);
        event.setFcm_type(fcmType);
        event.setFcm_title(fcmTitle);
        event.setFcm_action(1001);
        event.setFcm_push_status(1);
        event.setFcm_subtype(FcmMsgTypeConstant.FCM_SUB_6);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 调用py发送礼物接口
     */
    @Deprecated
    private HttpResult<?> doSendGiftApi(MsgSendDTO reqData) {
        JSONObject msgInfo = reqData.getMsg_info();
        SendGiftDTO dto = new SendGiftDTO();
        dto.setUid(reqData.getUid());
        dto.setAid(reqData.getAid());
        dto.setGiftId(msgInfo.getIntValue("giftId"));
        dto.setNumber(msgInfo.getIntValue("sendNum"));
        dto.setSendType(2);
        return IGiftService.sendGift(dto);
    }

    private int updateMsgList(MysqlMsgRecordData recordData, MsgSendDTO reqData) {
        int greetChat = reqData.getGreetChat();
        String uid = reqData.getUid();
        int unread = 0;
        DistributeLock lock = new DistributeLock(recordData.getMsgIndex());
        lock.lock();
        try {
            MsgListData msgListData = msgListDao.findMsgListData(recordData.getMsgIndex());
            if (msgListData == null) {
                msgListData = new MsgListData();
                msgListData.setMsg_index(recordData.getMsgIndex());
                msgListData.setA_id(recordData.getFromUid());
                msgListData.setB_id(recordData.getToUid());
                msgListData.setA_entry(1);
                msgListData.setB_entry(1);
                msgListData.setNewGreet(greetChat > 0 ? 1 : 0);
            }
            msgListData.setA_last_msg(recordData);
            msgListData.setB_last_msg(recordData);
            msgListData.setMtime(System.currentTimeMillis());
            if (msgListData.getA_id().equals(recordData.getToUid())) {
                msgListData.setA_unread(msgListData.getA_unread() + 1);
            } else {
                msgListData.setB_unread(msgListData.getB_unread() + 1);
            }
            if (msgListData.getA_id().equals(uid)) {
                unread = msgListData.getB_unread();
            } else {
                unread = msgListData.getA_unread();
            }
            // 取消会话删除状态
            msgListData.setA_delete(0);
            msgListData.setB_delete(0);
            msgListDao.save(msgListData);
        } catch (Exception e) {
            logger.error("update msg list error. msgIndex={} uid={} {}", recordData.getMsgIndex(), recordData.getFromUid(), e.getMessage(), e);
        } finally {
            lock.unlock();
        }
        return unread;
    }

    private int getUnReadNum(MysqlMsgRecordData recordData, String uid) {
        int unread = 0;
        MsgListData msgListData = msgListDao.findMsgListData(recordData.getMsgIndex());
        if (msgListData == null) {
            return unread;
        }
        if (msgListData.getA_id().equals(uid)) {
            unread = msgListData.getA_unread();
        } else {
            unread = msgListData.getB_unread();
        }
        return unread;
    }

    private SendMsgVO sendImage(MsgSendDTO reqData) {
        if (!checkAndRepairMsgInfo(reqData)) {
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
//        int userLevel = userLevelDao.getUserLevel(reqData.getUid());
        String uid = reqData.getUid();
        String aid = reqData.getAid();
        String originalUrl = getUploadOssUrl(reqData);
        FriendData friendData = friendDao.findDataFromCache(uid, aid);
        boolean isSafe = true;
        // if (friendData != null && DateHelper.getNowSeconds() - friendData.getCtime() <= THREE_DAY_TIME
        //         && vipInfoDao.getIntVipLevel(uid) < 2 && vipInfoDao.getIntVipLevel(aid) < 2) {
        try {
            isSafe = detectService.detectImage(new ImageDTO(originalUrl, DetectOriginConstant.PRIVATE_PICTURE, uid)).getData().getIsSafe() == 1;
        } catch (Exception e) {
            logger.info("");
        }
        // }
        if (!isSafe) {
            logger.info("unsafe image isSafe:{} uid:{} friendData:{} originalUrl:{} ", isSafe, uid, friendData, originalUrl);
            throw new CommonH5Exception(MsgHttpCode.MSG_CONTAINS_BAD_IMAGE);
        }
        reqData.setMsg_body(originalUrl);
        int size = reqData.getMsg_info().getIntValue("size");
        if (size > 0 && size <= IMG_LIMIT_SIZE) {
            reqData.msg_info.put("thumbnailUrl", originalUrl);
            logger.info("send image. size={} uid={}", size, uid);
            return doSend(reqData);
        }
        try {
            String url = ImageUrlGenerator.generateMsgChatUrl(reqData.getMsg_body());
            logger.info("send chat image. size={} url={} originalUrl={} uid={}", size, url, reqData.getMsg_body(), uid);
            reqData.setMsg_body(reqData.getMsg_body());
            reqData.msg_info.put("thumbnailUrl", url);
            return doSend(reqData);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            reqData.msg_info.put("thumbnailUrl", reqData.msg_body);
            return doSend(reqData);
        }
    }

    private boolean checkAndRepairMsgInfo(MsgSendDTO reqData) {
        try {
            JSONObject msgInfo = reqData.getMsg_info();
            msgInfo.put("width", msgInfo.getIntValue("width"));
            msgInfo.put("height", msgInfo.getIntValue("height"));
            return true;
        } catch (Exception e) {
            logger.error("check and repair msg info error. {}", e.getMessage(), e);
        }
        return false;
    }

    private MysqlMsgRecordData saveToDB(MsgSendDTO reqData) {
        String uid = reqData.getUid();
        MysqlMsgRecordData recordData = new MysqlMsgRecordData();
        recordData.setFromUid(uid);
        recordData.setToUid(reqData.aid);
        recordData.setMsgIndex(MsgUtils.generateMsgIndex(uid, reqData.aid));
        recordData.setMsg(reqData.msg_body);
        recordData.setMsgInfo(reqData.msg_info.toJSONString());
        recordData.setMsgId(new ObjectId().toString());
        recordData.setTimestamp(System.currentTimeMillis());
        recordData.setMsgType(reqData.msg_type);
        recordData.setFromLike("");
        recordData.setToLike("");
        // 指定用户屏蔽接收指定类型消息
        if (UN_RECEIVE_MSG_TYPE_UID_LIST.contains(reqData.aid) && UN_RECEIVE_MSG_TYPE_LIST.contains(reqData.msg_type)) {
            recordData.setToDelete(1);
        }
        recordDao.insert(recordData);
        return recordData;
    }

  /*  private void sendToWebSocket(MysqlMsgRecordData recordData) {
        MsgRecordRsp recordRsp = new MsgRecordRsp();
        HttpEnvData envData = new HttpEnvData();
        envData.setOs(0);
        envData.setVersioncode(1);
        envData.setUid(recordData.getFromUid());
        recordRsp.fillFrom(recordData);
        JSONObject fcmData = new JSONObject();
        fcmData.put("fromUid", recordRsp.getFromUid());
        fcmData.put("toUid", recordRsp.getToUid());
        fcmData.put("msgType", recordRsp.getMsgType());
        fcmData.put("msg", recordRsp.getMsg());
        fcmData.put("info", recordRsp.getInfo());
        JSONObject sendData = new JSONObject();
        sendData.put("topic", "ws_new_messages");
        sendData.put("uid", recordRsp.getToUid());
        sendData.put("data", fcmData);
        redisTaskService.broadcastMessage(PY_SEND_MSG_TO_WS_CHANNEL, sendData);
    }*/

    private String getUploadOssUrl(MsgSendDTO req) {
        return ImageUrlGenerator.createCdnUrl(req.getMsg_body());
    }

    private ApiResult<String> checkBlock(MsgSendDTO req) {
        String uid = req.getUid();
        ActorData actorData = actorDao.getActorData(uid);
        String tn_id = actorData.getTn_id();
        String blockTime = blockRedis.checkBlock(tn_id, BlockTnConstant.BLOCK_MSG);
        if (!StringUtils.isEmpty(blockTime)) {
            HttpCode blockError = HttpCode.BLOCK_ERROR;
            String msgEn = String.format(BlockTnConstant.MSG_BLOCK_MSG_EN, blockTime);
            String msgAr = String.format(BlockTnConstant.MSG_BLOCK_MSG_AR, blockTime);
            HttpCode newError = new HttpCode(blockError.getCode(), msgEn, msgAr);
            return ApiResult.getError(newError);
        }
        return ApiResult.getOk();
    }
}
