package com.quhong.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.web.WebClient;
import com.quhong.data.AppleNotificationData;
import com.quhong.data.dto.AppleNotificationDTO;
import com.quhong.data.dto.ApplePayDTO;
import com.quhong.handler.WebController;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.monitor.MonitorSender;
import com.quhong.service.ApplePayService;
import com.quhong.utils.JwsUtil;
import com.quhong.utils.RequestUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Base64;

@RestController
@RequestMapping("${baseUrl}")
public class AppleController extends WebController {

    private final static Logger logger = LoggerFactory.getLogger(AppleController.class);

    @Autowired
    private ApplePayService applePayService;
    @Autowired
    private WebClient webClient;
    @Resource
    protected MonitorSender monitorSender;

    /**
     * 苹果支付验证
     */
    @PostMapping("/apple/verifyReceipt")
    public String applePayVerify(HttpServletRequest request) {
        ApplePayDTO applePayDTO = RequestUtils.getSendData(request, ApplePayDTO.class);
        logger.info("applePayVerify uid={} data={}", applePayDTO.getUid(), JSON.toJSONString(applePayDTO));
        if (StringUtils.isEmpty(applePayDTO.getReceipt_data()) || StringUtils.isEmpty(applePayDTO.getTransaction_id())) {
            monitorSender.info("ustar_pay", "苹果支付参数错误", JSON.toJSONString(applePayDTO));
            return createResult(PayHttpCode.PARAM_ERROR, null);
        }
        if (applePayDTO.getReceipt_data().length() > 60000) {
            logger.error("apple verifyReceipt receipt_data too long uid={}", applePayDTO.getUid());
        }
        return createResult(applePayDTO, PayHttpCode.APPLE_PAY_SUCCESS, applePayService.applePayVerify(applePayDTO));
    }

    /**
     * 苹果通知回调V2版本
     * <a href="https://developer.apple.com/documentation/appstoreservernotifications/responsebodyv2decodedpayload">responseBodyV2DecodedPayload</a>
     */
    @PostMapping("/apple_pay/notification")
    public String appleNotification(@RequestBody AppleNotificationDTO dto) {
        logger.info("apple_pay notification. signedPayload={}", dto.getSignedPayload());
        String signedPayload = new String(Base64.getDecoder().decode(dto.getSignedPayload().split("\\.")[0]));
        JSONObject jsonObject = JSONObject.parseObject(signedPayload);
        Jws<Claims> result = JwsUtil.verifyJWT(jsonObject.getJSONArray("x5c").get(0).toString(), dto.getSignedPayload());
        if (null == result) {
            logger.error("apple notification invalid={}", dto.getSignedPayload());
            return "failed";
        }
        JSONObject resultObject = new JSONObject(result.getPayload());
        String notificationType = resultObject.getString("notificationType");
        JSONObject data = resultObject.getJSONObject("data");
        String env = data.getString("environment");
        JSONObject signedTransactionInfo = JSONObject.parseObject(new String(Base64.getDecoder().decode(data.getString("signedTransactionInfo").split("\\.")[0])));
        Jws<Claims> signedTransactionResult = JwsUtil.verifyJWT(signedTransactionInfo.getJSONArray("x5c").get(0).toString(), data.getString("signedTransactionInfo"));
        if (null == signedTransactionResult) {
            logger.error("apple notification invalid={}", dto.getSignedPayload());
            return "failed";
        }
        if (ServerConfig.isProduct()) {
            if (!"Production".equals(env)) {
                webClient.sendRestfulPost("https://testv2.qmovies.tv/pay/apple_pay/notification", JSON.toJSONString(dto));
                logger.info("notification is test. send to test server. notificationType={}", notificationType);
                return "success";
            }
        }
        JSONObject transactionResult = new JSONObject(signedTransactionResult.getPayload());
        AppleNotificationData notificationData = new AppleNotificationData();
        notificationData.setEnvironment(env);
        notificationData.setNotification_type(notificationType);
        notificationData.setProduct_id(transactionResult.getString("productId"));
        notificationData.setTransaction_id(transactionResult.getString("transactionId"));
        notificationData.setOriginal_transaction_id(transactionResult.getString("originalTransactionId"));
        applePayService.appleNotify(notificationData, transactionResult);
        return "success";
    }
}
