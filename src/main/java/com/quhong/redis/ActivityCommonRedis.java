package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.vo.ForYouListV2VO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;

/**
 * 活动常用redis处理方式
 */


@Lazy
@Component
public class ActivityCommonRedis {
    private static final Logger logger = LoggerFactory.getLogger(ActivityCommonRedis.class);
    private static final Integer MAX_SIZE = 30;


    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    /**
     * hash key
     */
    private String getCommonHashKey(String hashActivityId) {
        return String.format("hash:activity:%s", hashActivityId);
    }

    /**
     * 获取hash key所有值
     */
    public Map<String, Integer> getCommonHashAll(String hashActivityId) {
        Map<String, Integer> hashMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getCommonHashKey(hashActivityId));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", Integer.parseInt(String.valueOf(entry.getValue())));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getCommonHashAll error hashActivityId={} e={}", hashActivityId, e.getMessage(), e);
        }
        return hashMap;
    }


    /**
     * 获取hash key所有值
     */
    public Map<String, String> getCommonHashAllMapStr(String hashActivityId) {
        Map<String, String> hashMap = new HashMap<>();

        try {
            Map<Object, Object> entries = clusterTemplate.opsForHash().entries(getCommonHashKey(hashActivityId));
            for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                hashMap.put(entry.getKey() + "", String.valueOf(entry.getValue()));
            }
            return hashMap;
        } catch (Exception e) {
            logger.error("getCommonHashAllMapStr error hashActivityId={} e={}", hashActivityId, e.getMessage(), e);
        }
        return hashMap;
    }


    /**
     * 增加hash key 某个属性的值
     */
    public int incCommonHashNum(String hashActivityId, String key, int num) {
        try {
            Long afterNum = clusterTemplate.opsForHash().increment(getCommonHashKey(hashActivityId), key, num);
            clusterTemplate.expire(getCommonHashKey(hashActivityId), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return afterNum.intValue();
        } catch (Exception e) {
            logger.error("incCommonHashNum error hashActivityId={} e={}", hashActivityId, e);
            return num;
        }
    }

    /**
     * 设置hash key 某个属性的值
     */
    public void setCommonHashNum(String hashActivityId, String key, int num) {
        try {
            clusterTemplate.opsForHash().put(getCommonHashKey(hashActivityId), key, String.valueOf(num));
            clusterTemplate.expire(getCommonHashKey(hashActivityId), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setCommonHashNum error hashActivityId={} e={}", hashActivityId, e);
        }
    }

    /**
     * 设置hash key 某个属性的值
     */
    public void setCommonHashData(String hashActivityId, String key, String data) {
        try {
            clusterTemplate.opsForHash().put(getCommonHashKey(hashActivityId), key, data);
            clusterTemplate.expire(getCommonHashKey(hashActivityId), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setCommonHashData error hashActivityId={} e={}", hashActivityId, e);
        }
    }

    /**
     * 删除hash key 某个属性的值
     */
    public void delCommonHashData(String hashActivityId, String key) {
        try {
            clusterTemplate.opsForHash().delete(getCommonHashKey(hashActivityId), key);
        } catch (Exception e) {
            logger.error("delCommonHashData error hashActivityId={} e={}", hashActivityId, e);
        }
    }

    /**
     * 获取hash key 某个属性的值
     */
    public int getCommonHashValue(String hashActivityId, String key) {
        try {
            String valueStr = (String) clusterTemplate.opsForHash().get(getCommonHashKey(hashActivityId), key);
            if (StringUtils.isEmpty(valueStr)) {
                return 0;
            }
            return Integer.parseInt(valueStr);

        } catch (Exception e) {
            logger.info("getCommonHashValue error hashActivityId={} key={}  e={}", hashActivityId, key, e);
            return 0;
        }
    }

    public String getCommonHashStrValue(String hashActivityId, String key) {
        try {
            return (String) clusterTemplate.opsForHash().get(getCommonHashKey(hashActivityId), key);
        } catch (Exception e) {
            logger.info("getCommonHashStrValue error hashActivityId={} key={}  e={}", hashActivityId, key, e);
            return null;
        }
    }

    public Set<String> getCommonHashAllKeyStr(String hashActivityId) {
        Set<String> result = new CopyOnWriteArraySet<>();
        try {
            String key = getCommonHashKey(hashActivityId);
            Set<Object> fields = clusterTemplate.opsForHash().keys(getCommonHashKey(hashActivityId));
            for (Object field : fields) {
                result.add(String.valueOf(field));
            }
            return result;
        } catch (Exception e) {
            logger.error("getCommonHashAllKeyStr error. hashActivityId={}", hashActivityId, e);
            return result;
        }
    }

    public boolean hasUid(String hashActivityId, String uid) {
        try {
            String key = getCommonHashKey(hashActivityId);
            // redis中没有数据时才返回true
            boolean putSuccess = clusterTemplate.opsForHash().putIfAbsent(key, uid, "ok");
            if (putSuccess) {
                clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_FIFTEEN, TimeUnit.DAYS);
            }
            return !putSuccess;
        } catch (Exception e) {
            logger.error("hasUid error. uid={}", uid, e);
            return false;
        }
    }

    public void setCommonHashDataAll(String hashActivityId, Map<String, String> dataMap) {
        try {
            String key = getCommonHashKey(hashActivityId);
            clusterTemplate.opsForHash().putAll(key, dataMap);
        } catch (Exception e) {
            logger.info("setCommonHashDataAll error hashActivityId={} dataMap={}  e={}", hashActivityId, dataMap, e);
        }
    }

    /**
     * List相关
     */
    private String getCommonListKey(String activityId) {
        return String.format("list:activity:%s", activityId);
    }

    /**
     * 保留最近三十条记录
     */
    public void addCommonListRecord(String activityId, String data) {
        String key = getCommonListKey(activityId);
        Long listSize = clusterTemplate.opsForList().leftPush(key, data);
        clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        if (null != listSize && listSize > MAX_SIZE) {
            clusterTemplate.opsForList().trim(key, 0, MAX_SIZE);
        }
    }

    public List<String> getCommonListRecord(String activityId) {
        return getCommonListRecord(activityId, MAX_SIZE);
    }

    /**
     * 保留所有记录
     */
    public void addCommonListData(String activityId, String data) {
        String key = getCommonListKey(activityId);
        Long listSize = clusterTemplate.opsForList().leftPush(key, data);
        clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
    }

    public int addRightCommonListData(String activityId, String data) {
        String key = getCommonListKey(activityId);
        Long listSize = clusterTemplate.opsForList().rightPush(key, data);
        clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        return listSize != null ? listSize.intValue() : 0;
    }

    public List<String> getCommonListRecord(String activityId, int size) {
        try {
            String key = getCommonListKey(activityId);
            List<String> jsonList = clusterTemplate.opsForList().range(key, 0, size - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return new ArrayList<>();
            }
            List<String> resultList = new ArrayList<>(MAX_SIZE);
            resultList.addAll(jsonList);
            return resultList;
        } catch (Exception e) {
            logger.error("getCommonListRecord error", e);
            return new ArrayList<>();
        }
    }

    public List<String> getCommonListPageRecord(String activityId, int start, int end) {
        try {
            String key = getCommonListKey(activityId);
            List<String> jsonList = clusterTemplate.opsForList().range(key, start, end - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            return jsonList;
        } catch (Exception e) {
            logger.error("getCommonListPageRecord error", e);
            return Collections.emptyList();
        }
    }


    public int rightPushAllCommonList(String activityId, List<String> dataList) {
        try {
            String key = getCommonListKey(activityId);
            Long afterItem = clusterTemplate.opsForList().rightPushAll(key, dataList);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return afterItem != null ? afterItem.intValue() : 0;
        } catch (Exception e) {
            logger.error("rightPushAllCommonList error={}", e.getMessage(), e);
        }
        return 0;
    }

    public void leftPushAllCommonList(String activityId, List<String> dataList) {
        leftPushAllCommonList(activityId, dataList, null);
    }

    public void leftPushAllCommonList(String activityId, List<String> dataList, Integer maxSize) {
        try {
            String key = getCommonListKey(activityId);
            Long listSize = clusterTemplate.opsForList().leftPushAll(key, dataList);
            if (maxSize != null && maxSize > 0) {
                if (null != listSize && listSize > maxSize) {
                    clusterTemplate.opsForList().trim(key, 0, maxSize);
                }
            }
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("leftPushAllCommonList error={}", e.getMessage(), e);
        }
    }

    public String leftPopCommonListKey(String activityId) {
        try {
            String key = getCommonListKey(activityId);
            String prizeKey = clusterTemplate.opsForList().leftPop(key);
            return prizeKey != null ? prizeKey : "";
        } catch (Exception e) {
            logger.error("leftPopCommonListKey error e={}", e.getMessage());
            return "";
        }
    }

    public String getDataCommonListByIndex(String activityId, int index) {
        try {
            String key = getCommonListKey(activityId);
            String prizeKey = clusterTemplate.opsForList().index(key, index);
            return prizeKey != null ? prizeKey : "";
        } catch (Exception e) {
            logger.error("getDataCommonListByIndex error e={}", e.getMessage());
            return "";
        }
    }

    public void removeCommonListKey(String activityId, String data) {
        try {
            String key = getCommonListKey(activityId);
            clusterTemplate.opsForList().remove(key, 0, data);
        } catch (Exception e) {
            logger.error("removeCommonListKey error e={}", e.getMessage(), e);
        }
    }

    public int getCommonListSize(String activityId) {
        try {
            String key = getCommonListKey(activityId);
            Long poolSize = clusterTemplate.opsForList().size(key);
            return poolSize != null ? poolSize.intValue() : 0;
        } catch (Exception e) {
            logger.error("getCommonListSize error score={}", e.getMessage());
            return 0;
        }
    }

    public void deleteCommonListData(String activityId) {
        try {
            String key = getCommonListKey(activityId);
            clusterTemplate.delete(key);
        } catch (Exception e) {
            logger.error("deleteCommonListData error e={}", e.getMessage(), e);
        }
    }


    /**
     * 集合set相关
     */

    private String getCommonSetKey(String activityId) {
        return String.format("set:activity:%s", activityId);
    }

    public void addCommonSetData(String activityId, String data) {
        try {
            String key = getCommonSetKey(activityId);
            clusterTemplate.opsForSet().add(key, data);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addCommonSetData error activityId={} e={}", activityId, data, e);
        }
    }

    public void addSetValues(String activityId, Set<String> valueSet) {
        String key = getCommonSetKey(activityId);
        try {
            clusterTemplate.opsForSet().add(key, valueSet.toArray(new String[0]));
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("addSetValue error. key={} valueSet={} {}", key, Arrays.toString(valueSet.toArray()), e.getMessage(), e);
        }
    }

    public void removeCommonSetData(String activityId, String data) {
        try {
            String key = getCommonSetKey(activityId);
            clusterTemplate.opsForSet().remove(key, data);
        } catch (Exception e) {
            logger.error("removeCommonSetData error activityId={} e={}", activityId, data, e);
        }
    }

    public int isCommonSetData(String activityId, String data) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getCommonSetKey(activityId), data)) ? 1 : 0;
        } catch (Exception e) {
            logger.error("isDeviceData error activityId={} data={}, e={}", activityId, data, e);
            return 1;
        }
    }

    public int getCommonSetNum(String activityId) {
        try {

            String key = getCommonSetKey(activityId);
            Long number = clusterTemplate.opsForSet().size(key);
            return ObjectUtils.isEmpty(number) ? 0 : number.intValue();
        } catch (Exception e) {
            logger.info("getCommonSetNum error activityId={}  score={}", activityId, e);
            return 0;
        }
    }

    public Set<String> getCommonSetMember(String activityId) {
        try {
            String key = getCommonSetKey(activityId);
            return clusterTemplate.opsForSet().members(key);
        } catch (Exception e) {
            logger.info("getCommonSetMember error activityId={}  score={}", activityId, e);
            return Collections.emptySet();
        }
    }

    public String getCommonSetOneMember(String activityId) {
        try {
            String key = getCommonSetKey(activityId);
            return clusterTemplate.opsForSet().randomMember(key);
        } catch (Exception e) {
            logger.info("getCommonSetOneMember error activityId={}  score={}", activityId, e);
            return "";
        }
    }


    /**
     * str类型相关
     */
    private String getCommonStrKey(String activityId) {
        return String.format("str:activity:%s", activityId);
    }

    public int getCommonStrScore(String activityId) {
        try {
            String score = clusterTemplate.opsForValue().get(getCommonStrKey(activityId));
            return score != null ? Integer.parseInt(score) : 0;
        } catch (Exception e) {
            logger.info("getCommonStrScore error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public String getCommonStrValue(String activityId) {
        try {
            String value = clusterTemplate.opsForValue().get(getCommonStrKey(activityId));
            return value != null ? value : "";
        } catch (Exception e) {
            logger.info("getCommonStrValue error activityId={} e={}", activityId, e.getMessage(), e);
        }
        return "";
    }

    public void setCommonStrScore(String activityId, int score) {
        try {
            String key = getCommonStrKey(activityId);
            clusterTemplate.opsForValue().set(key, String.valueOf(score), ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setCommonStrScore error activityId={} score={} e={}", activityId, score, e.getMessage(), e);
        }
    }

    public long incCommonStrScore(String activityId, int score) {
        try {
            String key = getCommonStrKey(activityId);
            Long ret = clusterTemplate.opsForValue().increment(key, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return ret == null ? 0 : ret;
        } catch (Exception e) {
            logger.info("setCommonStrScore error activityId={} score={} e={}", activityId, score, e.getMessage(), e);
        }
        return 0;
    }

    public void setCommonStrData(String activityId, String data) {
        try {
            String key = getCommonStrKey(activityId);
            clusterTemplate.opsForValue().set(key, data, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("setCommonStrScore error activityId={} data={} e={}", activityId, data, e.getMessage(), e);
        }
    }

    /**
     * zset相关
     */
    private String getCommonZSetKey(String activityId) {
        return String.format("zset:activity:%s", activityId);
    }

    /**
     * 获取分数
     */
    public int getCommonZSetRankingScore(String activityId, String uid) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getCommonZSetKey(activityId), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRankingScore error activityId={} uid={}, e={}", activityId, uid, e.getMessage(), e);
            return 0;
        }
    }

    public double getCommonZSetRankingDoubleScore(String activityId, String uid) {
        try {
            Double score = clusterTemplate.opsForZSet().score(getCommonZSetKey(activityId), uid);
            return score != null ? score : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRankingDoubleScore error activityId={} uid={}, e={}", activityId, uid, e.getMessage(), e);
            return 0;
        }
    }

    public int incrCommonZSetRankingScore(String activityId, String uid, int score) {
        try {
            String key = getCommonZSetKey(activityId);
            int curScore = getCommonZSetRankingScore(activityId, uid);
            int nowSocre = curScore + score;
            double rankScore = new BigDecimal(nowSocre + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
            return nowSocre;
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScore error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
            return 0;
        }
    }

    public void addCommonZSetRankingScore(String activityId, String uid, int score) {
        try {
            String key = getCommonZSetKey(activityId);
            clusterTemplate.opsForZSet().add(key, uid, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addCommonZSetRankingScore error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
        }
    }

    public void addCommonZSetRankingScoreTime(String activityId, String uid, int score) {
        try {
            String key = getCommonZSetKey(activityId);
            double rankScore = new BigDecimal(score + "." + (Integer.MAX_VALUE - DateHelper.getNowSeconds())).doubleValue();
            clusterTemplate.opsForZSet().add(key, uid, rankScore);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("addCommonZSetRankingScore error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
        }
    }

    public int incrCommonZSetRankingScoreSimple(String activityId, String uid, int score) {
        Double ret = null;
        try {
            String key = getCommonZSetKey(activityId);
            ret = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScoreSimple error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
        }
        return ret == null ? 0 : ret.intValue();
    }

    public double incrCommonZSetRankingScoreDouble(String activityId, String uid, double score) {
        Double ret = null;
        try {
            String key = getCommonZSetKey(activityId);
            ret = clusterTemplate.opsForZSet().incrementScore(key, uid, score);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_180, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScoreSimple error activityId={} aid={} giftId={} score={}", activityId, uid, score, e.getMessage(), e);
        }
        return ret == null ? 0 : ret;
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getCommonZSetRank(String activityId, String uid) {
        try {
            String key = getCommonZSetKey(activityId);
            Long rank = clusterTemplate.opsForZSet().reverseRank(key, uid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRank error activityId={} aid={}", activityId, uid, e);
            return 0;
        }
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     * 正序
     */
    public int getCommonZSetPositiveRank(String activityId, String uid) {
        try {
            String key = getCommonZSetKey(activityId);
            Long rank = clusterTemplate.opsForZSet().rank(key, uid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRank error activityId={} aid={}", activityId, uid, e);
            return 0;
        }
    }

    /**
     * 获取排行榜
     */
    public List<String> getCommonRankingList(String activityId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取排行榜
     * 倒序
     *
     * @param activityId
     * @param start      0 第一个元素
     * @param end        例如取前十，这里传10
     * @return
     */
    public List<String> getCommonRankingList(String activityId, int start, int end) {
        List<String> rankingList = new ArrayList<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, start, end - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     * 倒序
     */
    public Map<String, Integer> getCommonRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取带分数排行榜
     * 倒序
     */
    public Map<String, Integer> getCommonRankingMapByPage(String activityId, int start, int end) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, start, end - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 获取排行榜
     * 正序
     */
    public List<String> getCommonRangeRankingList(String activityId, int length) {
        List<String> rankingList = new ArrayList<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return rankingList;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            rankingList.add(rangeWithScore.getValue());
        }
        return rankingList;
    }

    /**
     * 获取带分数排行榜
     * 正序
     */
    public Map<String, Integer> getCommonRangeRankingMap(String activityId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取带分数排行榜
     * 正序
     */
    public Map<String, Integer> getCommonRangeRankingMapByPage(String activityId, int start, int end) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeWithScores(key, start, end - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 通过分数获取成员
     * 正序
     */
    public Map<String, Integer> getCommonRangeRankingMapByScore(String activityId, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().rangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 通过分数获取成员
     * 倒序
     */
    public Map<String, Integer> getCommonRankingMapByScore(String activityId, int min, int max) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    /**
     * 分页获取指定分数段带分数排行榜
     * 倒序
     * offset: 偏移多少
     * count: 取多少
     */
    public Map<String, Integer> getOtherRankingMapByScoreAPage(String activityId, int min, int max, int offset, int count) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(activityId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeByScoreWithScores(key, min, max, offset, count);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    /**
     * 移除排名
     */
    public void removeCommonZSet(String activityId, String uid) {
        try {
            String key = getCommonZSetKey(activityId);
            clusterTemplate.opsForZSet().remove(key, uid);
        } catch (Exception e) {
            logger.info("getCommonZSetRank error activityId={} aid={}", activityId, uid, e);
        }
    }

    /**
     * 获取大小
     */
    public int getCommonZSetMemberNum(String activityId) {
        try {
            String key = getCommonZSetKey(activityId);
            Long memberNum = clusterTemplate.opsForZSet().zCard(key);
            return memberNum == null ? 0 : memberNum.intValue();
        } catch (Exception e) {
            logger.info("getCommonZSetMemberNum error activityId={} error={}", activityId, e.getMessage(), e);
        }
        return 0;
    }

    public void deleteCommonZSet(String activityId) {
        try {
            String key = getCommonZSetKey(activityId);
            clusterTemplate.delete(key);
        } catch (Exception e) {
            logger.info("deleteCommonZSet error activityId={} e={}", activityId, e.getMessage(), e);
        }
    }


    public void addAwardNotify(String activityId, int slang, String strNotify) {
        String key = getAwardNotifyKey(activityId, slang);
        try {
            Long listSize = clusterTemplate.opsForList().leftPush(key, strNotify);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            if (null != listSize && listSize > 100) {
                clusterTemplate.opsForList().trim(key, 0, 10);
            }
        } catch (Exception e) {
            logger.error("addAwardNotify error. activityId={} strNotify={} {}", activityId, strNotify, e);
        }
    }

    public List<String> getAwardNotifyList(String activityId, int slang) {
        try {
            String key = getAwardNotifyKey(activityId, slang);
            return clusterTemplate.opsForList().range(key, 0, 9);
        } catch (Exception e) {
            logger.error("getAwardNotifyList error. activityId={}", activityId, e);
            return Collections.emptyList();
        }
    }

    private String getAwardNotifyKey(String activityId, int slang) {
        return "list:activityAwardNotify_" + activityId + "_" + slang;
    }

    public void saveDrawRecord(String activityId, String uid, Object object) {
        String key = getDrawRecordKey(activityId, uid);
        try {
            clusterTemplate.opsForZSet().add(key, JSONObject.toJSONString(object), DateHelper.getNowSeconds());
            clusterTemplate.expire(key, 30, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("saveDrawRecord error. activityId={} uid={} strDrawRecord={} {}", activityId, uid, JSONObject.toJSONString(object), e.getMessage(), e);
        }
    }

    public <T> List<T> getDrawRecords(String activityId, String uid, int start, int end, Class<T> clazz) {
        String key = getDrawRecordKey(activityId, uid);
        List<T> list = new ArrayList<>();
        try {
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, start, end);
            if (null != rangeWithScores) {
                for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
                    if (null == rangeWithScore.getValue()) {
                        continue;
                    }
                    list.add(JSONObject.parseObject(rangeWithScore.getValue(), clazz));
                }
            }
        } catch (Exception e) {
            logger.error("getDrawRecords error. activityId={} uid={} start={} end={} {}", activityId, uid, start, end, e.getMessage(), e);
            return Collections.emptyList();
        }
        return list;
    }

    private String getDrawRecordKey(String activityId, String uid) {
        return "zset:activityDrawRecord_" + activityId + "_" + uid;
    }

    private String getCountryListKey(String countryCode) {
        return "str:countryList:" + countryCode;
    }

    public List<ForYouListV2VO> getCountryList(String countryCode) {
        List<ForYouListV2VO> result = new ArrayList<>();
        try {
            String json = clusterTemplate.opsForValue().get(getCountryListKey(countryCode));
            if (StringUtils.isEmpty(json)) {
                return result;
            }
            return JSON.parseArray(json, ForYouListV2VO.class);
        } catch (Exception e) {
            logger.error("get countryList error countryCode={}", countryCode, e);
            return result;
        }
    }


}
