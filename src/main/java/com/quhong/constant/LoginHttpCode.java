package com.quhong.constant;

import com.quhong.enums.HttpCode;

public class LoginHttpCode extends HttpCode {
    public static final HttpCode DIFF_DEV_LOGIN = new HttpCode(3001, "diff_dev_login");
    public static final HttpCode ACCOUNT_BANNED = new HttpCode(3002, "account_banned");
    public static final HttpCode UNPAID_BILLS = new HttpCode(3003, "unpaid_bills");

    public static final HttpCode SPECIAL_GUST_VIP_LIMIT = new HttpCode(6001, "sg_vip_limit");
    public static final HttpCode SPECIAL_GUST_NAME_FORMAT = new HttpCode(6001, "sg_name_format");
    public static final HttpCode SPECIAL_GUST_PWD_FORMAT = new HttpCode(6001, "sg_pwd_format");
    public static final HttpCode SPECIAL_GUST_EXISTED = new HttpCode(6001, "sg_existed");

    public static final HttpCode SPECIAL_GUST_SET_FORMAT = new HttpCode(6001, "sg_set_name_format");
    public static final HttpCode SPECIAL_GUST_SET_SAME = new HttpCode(6001, "sg_set_same");
    public static final HttpCode SPECIAL_GUST_SET_NOT_EXISTED = new HttpCode(6001, "sg_set_no_existed");
    public static final HttpCode SPECIAL_GUST_SET_ERROR = new HttpCode(101, "sg_set_error");

    public static final HttpCode SPECIAL_GUST_LOGIN_NOT_FIND = new HttpCode(6001, "sg_login_not_find");
    public static final HttpCode SPECIAL_GUST_LOGIN_PWD_ERROR = new HttpCode(6001, "sg_login_pwd_error");


    public static final HttpCode SPECIAL_GUST_SUCCESS = new HttpCode(0, "Alias name and password set successfully! Please remember you alias name and password, and do not let other know."
            , "تم تعيين الإسم المستعار و كلمة السر بنجاح ! الرجاء تذكر إسمك المستعار و كلمة السر، و لا تدع الآخرين يعرفونها");
    public static final HttpCode SPECIAL_GUST_SET_SUCCESS = new HttpCode(0, "Reset password successfully!"
            , "تم إعادة تعيين كلمة السر بنجاح!");
    public static final HttpCode LOGIN_FAIL = new HttpCode(100, "login fail, please try again");
    public static final HttpCode LOCK_TIMEOUT = new HttpCode(500, "Distributed Lock Timeout.");

    public static final HttpCode PHONE_LOGIN_ACCOUNT_ERROR = new HttpCode(6001, "ph_login_account_error");
    public static final HttpCode PHONE_LOGIN_NOT_FIND = new HttpCode(6001, "ph_login_not_find");
    public static final HttpCode PHONE_LOGIN_PWD_ERROR = new HttpCode(6001, "sg_login_pwd_error");

    public static final HttpCode EMAIL_LOGIN_REFUSE = new HttpCode(45, "Email registration has been disabled. It is recommended to use facebook, mobile phone number registration.");
    public static final HttpCode LOGIN_FAIL_UNSUPPORTED = new HttpCode(100, "login fail, current area are not supported");
    public static final HttpCode DEVICE_LOGIN_LIMIT_EMULATOR = new HttpCode(45, "device_login_limit_emulator");
    public static final HttpCode DEVICE_LOGIN_LIMIT_AREA = new HttpCode(45, "device_login_limit_area");
    public static final HttpCode DEVICE_LOGIN_LIMIT_BAN = new HttpCode(73, "device_login_limit_ban");
    public static final HttpCode DEVICE_LOGIN_LIMIT_ACCOUNT = new HttpCode(45, "device_login_limit_account");
    public static final HttpCode DEVICE_LOGIN_LIMIT_IP = new HttpCode(6001, "device_login_limit_ip");

    public static final HttpCode ACCOUNT_LOGIN_BLOCK_BAN = new HttpCode(41, "account_login_block_ban");

    public static final HttpCode PHONE_ACCOUNT_CHECK_EXISTED = new HttpCode(6001, "phone existed");
    public static final HttpCode UPDATE_YOUR_APP = new HttpCode(6002, "Please update the app.");

    public static final HttpCode VERIFICATION_ERROR = new HttpCode(6005, "The verification code is invalid","رمز التحقق غير صالح");
    public static final HttpCode VERIFICATION_EXPIRED = new HttpCode(6005, "Verification code has expired","لقد انتهت صلاحية رمز التحقق");
    public static final HttpCode INVALID_ID = new HttpCode(6006, "Invalid User ID","معرف مستخدم غير صحيح");
    public static final HttpCode PLEASE_WAIT = new HttpCode(6007, "Please wait and try again in 1 minute.","يرجى الانتظار والمحاولة مرة أخرى بعد دقيقة واحدة.");

    public static final HttpCode ACCOUNT_RISK_ZERO = new HttpCode(45, "account_risk_zero");

    public static final HttpCode PHONE_LOGIN_NOT_REGISTER = new HttpCode(7001, "ph_login_not_register");

    public static final HttpCode TN_MSG_EMPTY = new HttpCode(1001, "tn msg empty");

    public static final HttpCode LOGIN_SEND_SMS_LIMIT = new HttpCode(1001, "login_send_sms_limit_tips");
}
