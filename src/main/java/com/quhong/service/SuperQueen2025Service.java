package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.config.ActivityCommonConfig;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.MomentConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.SendGiftData;
import com.quhong.data.dto.PainterPictureDTO;
import com.quhong.data.vo.*;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerPublishMomentDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IMomentService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.BadgeListDao;
import com.quhong.mongo.dao.MomentActivityDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.BadgeListData;
import com.quhong.mongo.data.MomentActivityData;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.PageUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 2025女王活动
 */
@Service
public class SuperQueen2025Service extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(SuperQueen2025Service.class);
    private static final String ACTIVITY_TITLE_EN = "2025 Super Queen";
    private static final String ACTIVITY_TITLE_AR = "2025 Super Queen";
    //    public static final String PRE_ACTIVITY_ID = "6777b9f2d4af685fa686705d"; // 报名期间的活动id
//    public static final String ACTIVITY_ID = "6777b9fdd4af685fa686705e"; //
//    public static final String PRE_ACTIVITY_ID = "67a49366cbaf63918ab387d4"; // 报名期间的活动id
    public static String ACTIVITY_ID = "68c2a55fb84ffd72e0a73272"; //  681b0f4b4021e4c5d4251436 202505正式活动id
    public static final int PRE_ACTIVITY_BEFORE_DAY = 6; // 预报名在正式活动的几天前
    public static final int CHARM_TYPE = 1; // 魅力
    public static final int GENEROUS_TYPE = 2; // 慷慨
    public static final int PASSION_TYPE = 3; // 热情
    public static final int SUPER_TYPE = 4; // 超级

    private static final List<Integer> QUEEN_ALL_LIST = Arrays.asList(CHARM_TYPE, GENEROUS_TYPE, PASSION_TYPE);

    private static String ACTIVITY_URL = String.format("https://static.youstar.live/super_queen2024/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";

    private static final List<Integer> SCORE_CHARM_LIST = Arrays.asList(0, 100, 500, 1200, 2600, 4800, 7800);
    private static final List<Integer> SCORE_GENEROUS_LIST = Arrays.asList(0, 100, 500, 1200, 2600, 4800, 7800);
    private static final List<Integer> SCORE_PASSION_LIST = Arrays.asList(0, 600, 1000, 1400, 1800, 2000, 2400);

    // 1-3月活动id集合 "6777b9fdd4af685fa686705e", "67a9f0669ca3a87eec6d74b0", "67c7bda65913f30d6b6d2fe0"
    // 4-6月活动id集合 "67f64fc806db8d00be1c5014", "681b0f4b4021e4c5d4251436", "6846ca39619765a0e117bd67"
    private static List<String> QUEEN_ACTIVITY_ID_LIST = Arrays.asList("67f64fc806db8d00be1c5014", "681b0f4b4021e4c5d4251436", "6846ca39619765a0e117bd67");

    private static List<Map<Integer, List<String>>> honorHistoryList = new ArrayList<>(); // 所有获得女王称号名单集合
    /**
     * 类型等级积分
     */
    public static final Map<Integer, List<Integer>> TYPE_SCORE_MAP = new HashMap<Integer, List<Integer>>() {
        {
            put(CHARM_TYPE, SCORE_CHARM_LIST);
            put(GENEROUS_TYPE, SCORE_GENEROUS_LIST);
            put(PASSION_TYPE, SCORE_PASSION_LIST);
        }
    };

    /**
     * 类型等级奖励
     */
    public static final Map<Integer, List<String>> TYPE_SCORE_KEY_MAP = new HashMap<Integer, List<String>>() {
        {
            put(CHARM_TYPE, Arrays.asList("charmLevel1", "charmLevel2", "charmLevel3", "charmLevel4", "charmLevel5", "charmLevel6"));
            put(GENEROUS_TYPE, Arrays.asList("generousLevel1", "generousLevel2", "generousLevel3", "generousLevel4", "generousLevel5", "generousLevel6"));
            put(PASSION_TYPE, Arrays.asList("passionLevel1", "passionLevel2", "passionLevel3", "passionLevel4", "passionLevel5", "passionLevel6"));
        }
    };


    /**
     * 榜单奖励
     */
    public static final Map<Integer, List<String>> TYPE_RANK_KEY_MAP = new HashMap<Integer, List<String>>() {
        {
            put(CHARM_TYPE, Arrays.asList("charmRankTop1", "charmRankTop2", "charmRankTop3"));
            put(GENEROUS_TYPE, Arrays.asList("generousRankTop1", "generousRankTop2", "generousRankTop3"));
            put(PASSION_TYPE, Arrays.asList("passionRankTop1", "passionRankTop2", "passionRankTop3"));
        }
    };

    /**
     * 专属奖励
     */
    public static final Map<Integer, String> TYPE_REWARD_MAIN_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(CHARM_TYPE, "charmMainKey");
            put(GENEROUS_TYPE, "generousMainKey");
            put(PASSION_TYPE, "passionMainKey");
            put(SUPER_TYPE, "superMainKey");
        }
    };

    /**
     * 惊喜奖励
     */
    public static final Map<Integer, String> TYPE_REWARD_EXTRA_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(CHARM_TYPE, "charmExtraKey");
            put(GENEROUS_TYPE, "generousExtraKey");
            put(PASSION_TYPE, "passionExtraKey");
        }
    };


    /**
     * 勋章id映射  2923,2922,2921
     */
    private static Map<Integer, Integer> TYPE_BADGE_ID_MAP = new HashMap<Integer, Integer>() {
        {
            put(CHARM_TYPE, 2922);
            put(GENEROUS_TYPE, 2921);
            put(PASSION_TYPE, 2923);
            put(SUPER_TYPE, 2924);
        }
    };


    /**
     * 获得的目标积分
     */
    public static final Map<Integer, Integer> TYPE_REWARD_TARGET_MAP = new HashMap<Integer, Integer>() {
        {
            put(CHARM_TYPE, 8500);
            put(GENEROUS_TYPE, 8500);
            put(PASSION_TYPE, 2500);
        }
    };

    /**
     * 惊喜奖励每日减分
     */
    public static final Map<Integer, Integer> TYPE_REWARD_EXTRA_REDUCE_MAP = new HashMap<Integer, Integer>() {
        {
            put(CHARM_TYPE, -100);
            put(GENEROUS_TYPE, -100);
            put(PASSION_TYPE, -100);
        }
    };

    /**
     * 停止下发通知消息title
     */
    private static Map<Integer, List<String>> TYPE_STOP_TITLE_NOTICE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(CHARM_TYPE, Arrays.asList("Charm Queen surprise rewards have stopped being issued",
                    "توقف إصدار مكافآت ملكة السحر المفاجئة "));
            put(GENEROUS_TYPE, Arrays.asList("Generous Queen surprise rewards have stopped being issued",
                    "توقف إصدار مكافآت ملكة الكرم المفاجئة "));
            put(PASSION_TYPE, Arrays.asList("Passion Queen surprise rewards have stopped being issued",
                    "توقف إصدار مكافآت الملكة الشغوفة المفاجئة "));
        }
    };

    /**
     * 停止下发通知消息body
     */
    private static Map<Integer, List<String>> TYPE_STOP_BODY_NOTICE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(CHARM_TYPE, Arrays.asList("Your Charm Queen surprise rewards have stopped being issued. Go to gain charm points to keep geting the surprise rewards.",
                    "تم توقف إصدار مكافآت ملكة السحر المفاجئة. قم بجمع نقاط السحر للاستمرار في الحصول على المكافآت المفاجئة."));
            put(GENEROUS_TYPE, Arrays.asList("Your Generous Queen surprise rewards have stopped being issued. Go to gain generous points to keep geting the surprise rewards.",
                    "تم توقف إصدار مكافآت ملكة الكرم المفاجئة. قم بجمع نقاط الكرم للاستمرار في الحصول على المكافآت المفاجئة."));
            put(PASSION_TYPE, Arrays.asList("Your Passion Queen surprise rewards have stopped being issued. Go to gain passion points to keep geting the surprise rewards.",
                    "تم توقف إصدار مكافآت الملكة الشغوفة المفاجئة. قم بجمع نقاط الشغف للاستمرار في الحصول على المكافآتالمفاجئة."));
        }
    };


    /**
     * 下发通知消息title
     */
    private static Map<Integer, List<String>> TYPE_START_TITLE_NOTICE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(CHARM_TYPE, Arrays.asList("Charm Queen surprise reward has been issued", "تم إصدار مكافأة مفاجئة لملكة السحر"));
            put(GENEROUS_TYPE, Arrays.asList("Generous Queen surprise reward has been issued", "تم إصدار مكافأة مفاجئة للملكة الكرم"));
            put(PASSION_TYPE, Arrays.asList("Passion Queen surprise reward has been issued", "تم إصدار مكافأة مفاجئة لملكة الشغوفة"));
        }
    };

    /**
     * 下发通知消息body
     */
    private static Map<Integer, List<String>> TYPE_START_BODY_NOTICE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(CHARM_TYPE, Arrays.asList("Your Charm Queen surprise reward for today has been sent! Please check!"
                    , "تم إرسال مكافأة ملكة السحر المفاجئة لليوم!\n" +
                            "يرجى التحقق!\n"));
            put(GENEROUS_TYPE, Arrays.asList("Your Generous Queen surprise reward for today has been sent! Please check!", "تم إرسال مكافأة ملكة الكرم المفاجئة لليوم!\n" +
                    "يرجى التحقق!\n"));
            put(PASSION_TYPE, Arrays.asList("Your Passion Queen surprise reward for today has been sent! Please check!", "تم إرسال مكافأة الملكة الشغوفة المفاجئة لليوم\n" +
                    "يرجى التحقق!\n"));
        }
    };


    /**
     * 下发通知消息title
     */
    private static Map<Integer, List<String>> TYPE_LOCK_TITLE_NOTICE_MAP = new HashMap<Integer, List<String>>() {
        {
            put(11, Arrays.asList("Your charm value has reached 100, unlocking the first node reward.", "لقد وصلت قيمة سحرك إلى 100، مما يفتح مكافآت اضافية الأولى."));
            put(16, Arrays.asList("Your charm value has reached 7800, unlocking the last node reward.", "لقد وصلت قيمة سحرك إلى 7800، مما يفتح مكافآت اضافية الأخيرة."));
            put(21, Arrays.asList("Your generous value has reached 100, unlocking the first node reward.", "لقد وصلت قيمة كرمك إلى 100، مما يفتح مكافآت اضافية الأولى."));
            put(26, Arrays.asList("Your generous value has reached 7800, unlocking the last node reward.", "لقد وصلت قيمة كرمك إلى 7800، مما يفتح مكافآت اضافية الأخيرة."));
            put(31, Arrays.asList("Your passion value has reached 600, unlocking the first node reward.", "لقد وصلت قيمة شغفك إلى 600، مما يفتح مكافآت اضافية الأولى."));
            put(36, Arrays.asList("Your passion value has reached 2400, unlocking the last node reward.", "لقد وصلت قيمة شغفك إلى 2400، مما يفتح مكافآت اضافية الأخيرة."));

            put(5, Arrays.asList("\uD83D\uDCE2 You are just one step away from glory! You are just one step away from unlocking your exclusive identity! Come and complete the final challenge to claim your exclusive rewards! \uD83C\uDF81✨",
                    "\uD83D\uDCE2 أنت على بعد خطوة واحدة فقط من المجد! أنت على بعد خطوة واحدة فقط من فتح هويتك الحصرية! تعال واكمل التحدي النهائي لتحقيق مكافآتك الحصرية! \uD83C\uDF81✨"));
        }
    };


    /**
     * 埋点事件
     */
    public static final Map<Integer, String> TYPE_EVENT_TITLE_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(CHARM_TYPE, "charm");
            put(GENEROUS_TYPE, "generous");
            put(PASSION_TYPE, "passion");
            put(SUPER_TYPE, "super");
        }
    };

    /**
     * 埋点事件
     */
    public static final Map<Integer, String> TYPE_EVENT_SENCE_KEY_MAP = new HashMap<Integer, String>() {
        {
            put(1, "task");
            put(2, "daily");
            put(3, "rank");
            put(4, "periodic");
        }
    };

    private static final int GREETING_PRIVATE = 31 ;// 主动打招呼
    private static final int GREETING_BE_REPLIED = 32 ;// 我回复别人的打招呼消息
    private static final int GREETING_REPLY_OTHER = 33 ;// 别人回复我的打招呼消息

    public static final String HONOR_MONTH_1_3 = "queen_honor_month_1_3"; //  荣誉表彰1-3月
    public static String HONOR_ACTIVITY_ID = "689f05e2d1a79b68dc072415";// 荣誉表彰活动id 67ea388412e176ab0fdd7a06
    private static final String SUPER_QUEEN_HONOR_EVENT = "Super Queen Honor reward";
    private static final String SUPER_QUEEN_HONOR_KEY = "superQueenHonorGetKey";
    private static final String SUPER_QUEEN_NO_HONOR_KEY = "superQueenNoHonorGetKey";
    private static String HONOR_ACTIVITY_URL = String.format("https://static.youstar.live/super_queen_hall2025/?activityId=%s", HONOR_ACTIVITY_ID);

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final int HISTORY_PAGE_SIZE = 30;
    private static final int HISTORY_MAX_SIZE = 3000;

    private static int charmGift39DId = 25;
    private static int charmGift99DId = 1018;
    private static int charmGift199DId = 458;

    private static int generousGift27DId = 253; // 给女用户送礼
    private static int generousGift39DId = 25; // 给男用户送礼
    private static int generousGift49DId = 670; // 给新用户送礼

    private static int PASSION_DAY_MAX = 800; //  每日800
    private static int PASSION_DAY_MAX_2 = 700; //

    public static final String FROM_YOUR_CIRCLE_EVENT = "from_your_circle_event";
    public static final String FROM_YOUR_CIRCLE_REPORT_EVENT = "Your Circle of Glory-get";

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL,
            CommonMqTaskConstant.ADD_FRIEND, CommonMqTaskConstant.COMMENT_MOMENT,
            CommonMqTaskConstant.LIKE_MOMENT, CommonMqTaskConstant.ON_MIC_TIME,
            CommonMqTaskConstant.GREETING_PRIVATE_MSG);

    public static final String MOMENT_QUEEN_HALL_ORIGIN = String.format("%s-%s"
            , MomentConstant.NOT_CHECK_IMAGE_PREFIX, "queenHallPicture");
    private static int TOPIC_RID = 17343;
    private static final String ACTIVITY_ICON = "https://cdn3.qmovies.tv/youstar/op_1751279038_pyq.jpg";
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private ActivityCommonConfig activityCommonConfig;
    @Resource
    private SuperQueen2025Service superQueen2025Service;
    @Resource
    private BadgeListDao badgeListDao;
    @Resource
    private IMomentService iMomentService;
    @Resource
    private MomentActivityDao momentActivityDao;
//    @Resource
//    private RepeatMsgRedis repeatMsgRedis;

    @PostConstruct
    public void init() {

        if (ServerConfig.isNotProduct()) {
            charmGift99DId = 899; // bag
            charmGift199DId = 632; // Camel
            charmGift39DId = 110; // red rose

            generousGift27DId = 102; // coffee
            generousGift39DId = 7;  // rose
            generousGift49DId = 636; // Perfume

            TYPE_BADGE_ID_MAP = new HashMap<Integer, Integer>() {
                {
                    put(CHARM_TYPE, 1717);
                    put(GENEROUS_TYPE, 1716);
                    put(PASSION_TYPE, 1715);
                    put(SUPER_TYPE, 1718);
                }
            };
            QUEEN_ACTIVITY_ID_LIST = Arrays.asList("676cf15f819709ee60d83839", "67a49371cbaf63918ab387d5", "67a9f0669ca3a87eec6d74b0");

            ACTIVITY_ID = "68affa5c6055291450230733";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/super_queen2024/?activityId=%s", ACTIVITY_ID);
            TOPIC_RID = 10132;

            HONOR_ACTIVITY_ID = "67ea388412e176ab0fdd7a06";
            HONOR_ACTIVITY_URL = String.format("https://test2.qmovies.tv/super_queen_hall2025/?activityId=%s", HONOR_ACTIVITY_ID);
        }
        initHonorRecord();
    }

    public SuperQueen2025VO joinSuperQueenActivity(String activityId, String uid) {
        // 预报名或者活动开始阶段都可以报名
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        boolean alive = inActivityTime(activityId);
        int curTime = DateHelper.getNowSeconds();
        boolean preAlive = curTime >= activityData.getStartTime() - (int) TimeUnit.DAYS.toSeconds(PRE_ACTIVITY_BEFORE_DAY)
                && curTime < activityData.getStartTime();
        if (preAlive || alive) {
            if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
                boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
                if (!isWhiteTestUser) {
                    // 灰度测试,只有测试用户可以报名
                    throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
                }
            }

            ActorData actorData = actorDao.getActorData(uid);
            if (actorData.getFb_gender() != 2) {
                logger.info("only women join uid:{}", uid);
                throw new CommonH5Exception(ActivityHttpCode.SUPER_QUEEN_ONLY_WOMEN);
            }
            if (activityCommonRedis.isCommonSetData(getSetJoinKey(activityId), uid) == 1) {
                logger.info("join already uid:{}", uid);
                throw new CommonH5Exception(ActivityHttpCode.SUPER_QUEEN_JOIN_ALREADY);
            }
            activityCommonRedis.addCommonSetData(getSetJoinKey(activityId), uid);
            cacheDataService.delSetCache(getSetJoinKey(activityId), 2);
            SuperQueen2025VO vo = new SuperQueen2025VO();
            vo.setJoinState(1);
            doJoinReportEvent(uid, preAlive ? 1 : 2);
            return vo;
        } else {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }

    public SuperQueen2025VO changeNoticeSw(String activityId, int type, String uid, int state) {
        if (!QUEEN_ALL_LIST.contains(type) || state < 0 || state > 1) {
            logger.info("not support type:{} state:{}", type, state);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (inActivityTime(activityId)) {
            if (activityCommonRedis.isCommonSetData(getSetJoinKey(activityId), uid) != 1) {
                logger.info("not join uid:{}", uid);
                throw new CommonH5Exception(ActivityHttpCode.SUPER_QUEEN_NOT_JOIN);
            }
            String totalInfoKey = getHashTotalKey(activityId);
            SuperQueen2025VO.MyQueenInfo myQueenInfo = cacheDataService.getSuperQueen2025VOMyQueenInfo(totalInfoKey, uid);
            boolean isChange = false;
            synchronized (stringPool.intern(getLocalQueenKey(uid))) {
                if (CHARM_TYPE == type && myQueenInfo.getCharmAgainSw() != state) {
                    myQueenInfo.setCharmAgainSw(state);
                    isChange = true;
                } else if (GENEROUS_TYPE == type && myQueenInfo.getGenerousAgainSw() != state) {
                    myQueenInfo.setGenerousAgainSw(state);
                    isChange = true;
                } else if (PASSION_TYPE == type && myQueenInfo.getPassionAgainSw() != state) {
                    myQueenInfo.setPassionAgainSw(state);
                    isChange = true;
                }
                if (isChange) {
                    activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(myQueenInfo));
                }
            }
            SuperQueen2025VO.MyQueenInfoVO myQueenInfoVO = new SuperQueen2025VO.MyQueenInfoVO();
            myQueenInfoVO.setCharmAgainSw(myQueenInfo.getCharmAgainSw());
            myQueenInfoVO.setGenerousAgainSw(myQueenInfo.getGenerousAgainSw());
            myQueenInfoVO.setPassionAgainSw(myQueenInfo.getPassionAgainSw());

            SuperQueen2025VO vo = new SuperQueen2025VO();
            vo.setMyQueenInfoVO(myQueenInfoVO);
            vo.setJoinState(1);
            return vo;
        } else {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }
    }

    public SuperQueen2025VO historyRecordList(String activityId, int type, String uid, int page) {
        if (!QUEEN_ALL_LIST.contains(type) || page <= 0) {
            logger.info("not support type:{} page:{}", type, page);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        List<SuperQueen2025VO.HistoryRedisData> historyRedisDataList = getHistoryListPageRecord(activityId, uid, type, page);
        SuperQueen2025VO vo = new SuperQueen2025VO();
        vo.setHistoryRedisDataList(historyRedisDataList);
        return vo;
    }

    public SuperQueen2025VO queen2025List(String activityId, String uid) {
        SuperQueen2025VO vo = new SuperQueen2025VO();
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        SuperQueen2025VO.MyQueenInfoVO myQueenInfoVO = new SuperQueen2025VO.MyQueenInfoVO();
        String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId), actorData.getTn_id());
        myQueenInfoVO.setIsValidDevice(StringUtils.isEmpty(bindAid) || uid.equals(bindAid) ? 1 : 0);
        SuperQueen2025VO.MyQueenInfo myQueenInfo = null;
        if (activityCommonRedis.isCommonSetData(getSetJoinKey(activityId), uid) == 1) {
            String totalInfoKey = getHashTotalKey(activityId);
            myQueenInfo = cacheDataService.getSuperQueen2025VOMyQueenInfo(totalInfoKey, uid);
            myQueenInfoVO.setCharmQueenState(!StringUtils.isEmpty(myQueenInfo.getCharmFirstDay()) ? 1 : 0);
            myQueenInfoVO.setGenerousQueenState(!StringUtils.isEmpty(myQueenInfo.getGenerousFirstDay()) ? 1 : 0);
            myQueenInfoVO.setPassionQueenState(!StringUtils.isEmpty(myQueenInfo.getPassionFirstDay()) ? 1 : 0);
            myQueenInfoVO.setSuperQueenState(!StringUtils.isEmpty(myQueenInfo.getSuperFirstDay()) ? 1 : 0);
            vo.setMyQueenInfoVO(myQueenInfoVO);
            vo.setJoinState(1);
            if (myQueenInfo.getIsShowSuperFirst() != 1 && !StringUtils.isEmpty(myQueenInfo.getSuperFirstDay())) {
                synchronized (stringPool.intern(getLocalQueenKey(uid))) {
                    if (myQueenInfo.getIsShowSuperFirst() != 1 && !StringUtils.isEmpty(myQueenInfo.getSuperFirstDay())) {
                        myQueenInfo.setIsShowSuperFirst(1);
                        myQueenInfoVO.setIsShowSuperFirst(1);
                        activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(myQueenInfo));
                    }
                }
            }

        } else {
            vo.setJoinState(actorData.getFb_gender() == 2 ? 0 : -1);
        }
        OtherRankingVO charmRankingVO = new OtherRankingVO();
        OtherRankingVO generousRankingVO = new OtherRankingVO();
        OtherRankingVO passionRankingVO = new OtherRankingVO();

        fillOtherRankingVO(activityId, uid, CHARM_TYPE, charmRankingVO, myQueenInfo);
        fillOtherRankingVO(activityId, uid, GENEROUS_TYPE, generousRankingVO, myQueenInfo);
        fillOtherRankingVO(activityId, uid, PASSION_TYPE, passionRankingVO, myQueenInfo);

        vo.setMyQueenInfoVO(myQueenInfoVO);
        vo.setCharmRankingVO(charmRankingVO);
        vo.setGenerousRankingVO(generousRankingVO);
        vo.setPassionRankingVO(passionRankingVO);
        return vo;
    }

    private void fillOtherRankingVO(String activityId, String uid, int type, OtherRankingVO otherRankingVO, SuperQueen2025VO.MyQueenInfo myQueenInfo) {
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        String typeKey = getZetTypeKey(activityId, type);
        Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(typeKey, 10);
        int rank = 1;
        OtherMyRankVO myRankVO = null;
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            OtherRankingListVO rankingListVO = new OtherRankingListVO();
            rankingListVO.setScore(entry.getValue());
            ActorData rankActor = actorDao.getActorDataFromCache(entry.getKey());
            if (null == rankActor) {
                logger.error("can not find actor. uid={}", entry.getKey());
                continue;
            }
            rankingListVO.setUid(entry.getKey());
            rankingListVO.setName(rankActor.getName());
            rankingListVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingListVO.setRank(rank);
            rankingList.add(rankingListVO);
            rank += 1;
            if (uid.equals(entry.getKey())) {
                myRankVO = new OtherMyRankVO();
                BeanUtils.copyProperties(rankingListVO, myRankVO);
            }
        }
        if (myRankVO == null) {
            ActorData myActor = actorDao.getActorDataFromCache(uid);
            myRankVO = new OtherMyRankVO();
            myRankVO.setName(myActor.getName());
            myRankVO.setHead(ImageUrlGenerator.generateRoomUserUrl(myActor.getHead()));
            myRankVO.setUid(uid);
            myRankVO.setScore(activityCommonRedis.getCommonZSetRankingScore(typeKey, uid));
            myRankVO.setRank(activityCommonRedis.getCommonZSetRank(typeKey, uid));
        }
        myRankVO.setRoomScore(getMaxLevel(myQueenInfo, type));
        otherRankingVO.setRankingList(rankingList);
        otherRankingVO.setMyRank(myRankVO);
    }


    public SuperQueen2025VO queen2025Info(String activityId, String uid) {
        String now = getDay(uid);
        String detailKey = getHashDetailKey(activityId, now);
        String totalInfoKey = getHashTotalKey(activityId);
        SuperQueen2025VO vo = new SuperQueen2025VO();
        OtherRankingActivityData activityData = getOtherRankingActivityNull(activityId);

        vo.setPreStartTime(activityData.getStartTime() - (int) TimeUnit.DAYS.toSeconds(PRE_ACTIVITY_BEFORE_DAY));
        vo.setPreEndTime(activityData.getStartTime());
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());

        SuperQueen2025VO.MyQueenInfoVO myQueenInfoVO = new SuperQueen2025VO.MyQueenInfoVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        myQueenInfoVO.setCharmRequiredPoint(TYPE_REWARD_TARGET_MAP.get(CHARM_TYPE));
        myQueenInfoVO.setGenerousRequiredPoint(TYPE_REWARD_TARGET_MAP.get(GENEROUS_TYPE));
        myQueenInfoVO.setPassionRequiredPoint(TYPE_REWARD_TARGET_MAP.get(PASSION_TYPE));
        myQueenInfoVO.setCharmRequiredDPoint(TYPE_REWARD_TARGET_MAP.get(CHARM_TYPE) * 3);
        myQueenInfoVO.setGenerousRequiredDPoint(TYPE_REWARD_TARGET_MAP.get(GENEROUS_TYPE) * 3);
        myQueenInfoVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        myQueenInfoVO.setName(actorData.getName());
        String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(activityId), actorData.getTn_id());
        myQueenInfoVO.setIsValidDevice(StringUtils.isEmpty(bindAid) || uid.equals(bindAid) ? 1 : 0);

        List<SuperQueen2025VO.DetailInfoVO> charmDetailInfoVOList = new ArrayList<>(); //  魅力任务明细信息
        List<SuperQueen2025VO.DetailInfoVO> generousDetailInfoVOList = new ArrayList<>(); //  慷慨任务明细信息
        List<SuperQueen2025VO.DetailInfoVO> passionDetailInfoVOList = new ArrayList<>(); //  热情任务明细信息
        SuperQueen2025VO.DetailInfo detailInfo = null;
        if (activityCommonRedis.isCommonSetData(getSetJoinKey(activityId), uid) == 1) {

            detailInfo = cacheDataService.getSuperQueen2025VODetailInfo(detailKey, uid);
            SuperQueen2025VO.MyQueenInfo myQueenInfo = cacheDataService.getSuperQueen2025VOMyQueenInfo(totalInfoKey, uid);

            myQueenInfoVO.setCharmPoint(myQueenInfo.getCharmPoint());
            myQueenInfoVO.setGenerousPoint(myQueenInfo.getGenerousPoint());
            myQueenInfoVO.setPassionPoint(myQueenInfo.getPassionPoint());
            myQueenInfoVO.setCharmDPoint(myQueenInfo.getCharmDPoint());
            myQueenInfoVO.setGenerousDPoint(myQueenInfo.getGenerousDPoint());


            myQueenInfoVO.setCharmQueenState(!StringUtils.isEmpty(myQueenInfo.getCharmFirstDay()) ? 1 : 0);
            myQueenInfoVO.setGenerousQueenState(!StringUtils.isEmpty(myQueenInfo.getGenerousFirstDay()) ? 1 : 0);
            myQueenInfoVO.setPassionQueenState(!StringUtils.isEmpty(myQueenInfo.getPassionFirstDay()) ? 1 : 0);
            myQueenInfoVO.setSuperQueenState(!StringUtils.isEmpty(myQueenInfo.getSuperFirstDay()) ? 1 : 0);

            myQueenInfoVO.setCharmAgainSw(myQueenInfo.getCharmAgainSw());
            myQueenInfoVO.setGenerousAgainSw(myQueenInfo.getGenerousAgainSw());
            myQueenInfoVO.setPassionAgainSw(myQueenInfo.getPassionAgainSw());

            int needCharm = TYPE_REWARD_TARGET_MAP.get(CHARM_TYPE) - myQueenInfo.getCharmPoint();
            int needGenerous = TYPE_REWARD_TARGET_MAP.get(GENEROUS_TYPE) - myQueenInfo.getGenerousPoint();
            int needPassion = TYPE_REWARD_TARGET_MAP.get(PASSION_TYPE) - myQueenInfo.getPassionPoint();

            myQueenInfoVO.setCharmAgainGet(detailInfo.getCharmAgainGetTime() > 0 ? 1 : 0);
            myQueenInfoVO.setGenerousAgainGet(detailInfo.getGenerousAgainGetTime() > 0 ? 1 : 0);
            myQueenInfoVO.setPassionAgainGet(detailInfo.getPassionAgainGetTime() > 0 ? 1 : 0);

            myQueenInfoVO.setCharmAgainGetLeftPoint(myQueenInfoVO.getCharmQueenState() == 1 && needCharm > 0 ? needCharm : 0);
            myQueenInfoVO.setGenerousAgainGetLeftPoint(myQueenInfoVO.getGenerousQueenState() == 1 && needGenerous > 0 ? needGenerous : 0);
            myQueenInfoVO.setPassionAgainGetLeftPoint(myQueenInfoVO.getPassionQueenState() == 1 && needPassion > 0 ? needPassion : 0);

            vo.setJoinState(1);
        } else {
            myQueenInfoVO.setCharmPoint(0);
            myQueenInfoVO.setGenerousPoint(0);
            myQueenInfoVO.setPassionPoint(0);
            myQueenInfoVO.setCharmDPoint(0);
            myQueenInfoVO.setGenerousDPoint(0);

            myQueenInfoVO.setCharmAgainSw(1);
            myQueenInfoVO.setGenerousAgainSw(1);
            myQueenInfoVO.setPassionAgainSw(1);

            vo.setJoinState(actorData.getFb_gender() == 2 ? 0 : -1);
        }
        List<ActivityCommonConfig.QueenConfig> srcCharmList = activityCommonConfig.getQueenCharmConfigList();
        List<ActivityCommonConfig.QueenConfig> srcGenerousList = activityCommonConfig.getQueenGenerousConfigList();
        List<ActivityCommonConfig.QueenConfig> srcPassionList = activityCommonConfig.getQueenPassionConfigList();

        fillQueenList(CHARM_TYPE, srcCharmList, charmDetailInfoVOList, detailInfo);
        fillQueenList(GENEROUS_TYPE, srcGenerousList, generousDetailInfoVOList, detailInfo);
        fillQueenList(PASSION_TYPE, srcPassionList, passionDetailInfoVOList, detailInfo);

        vo.setMyQueenInfoVO(myQueenInfoVO);
        vo.setCharmDetailInfoVOList(charmDetailInfoVOList);
        vo.setGenerousDetailInfoVOList(generousDetailInfoVOList);
        vo.setPassionDetailInfoVOList(passionDetailInfoVOList);
        return vo;
    }

    private void fillQueenList(int type, List<ActivityCommonConfig.QueenConfig> srcList,
                               List<SuperQueen2025VO.DetailInfoVO> toList, SuperQueen2025VO.DetailInfo detailInfo) {
        for (int i = 0; i < srcList.size(); i++) {
            ActivityCommonConfig.QueenConfig config = srcList.get(i);
            SuperQueen2025VO.DetailInfoVO itemVO = new SuperQueen2025VO.DetailInfoVO();
            BeanUtils.copyProperties(config, itemVO);
            if (detailInfo != null) {
                if (type == CHARM_TYPE) {
                    switch (i) {
                        case 0:
                            itemVO.setNowPoint(detailInfo.getCharmGift99DNum() * config.getNumPerPoint());
                            itemVO.setState(detailInfo.getCharmGift99DNum() >= config.getMaxNum() ? 2
                                    : detailInfo.getCharmGift99DNum() > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", detailInfo.getCharmGift99DNum(), config.getMaxNum()));
                            break;
                        case 1:
                            itemVO.setNowPoint(detailInfo.getCharmGift199DNum() * config.getNumPerPoint());
                            itemVO.setState(detailInfo.getCharmGift199DNum() >= config.getMaxNum() ? 2
                                    : detailInfo.getCharmGift199DNum() > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", detailInfo.getCharmGift199DNum(), config.getMaxNum()));
                            break;
                        case 2:
                            itemVO.setNowPoint(detailInfo.getCharmGift39DNum() * config.getNumPerPoint());
                            itemVO.setState(detailInfo.getCharmGift39DNum() >= config.getMaxNum() ? 2
                                    : detailInfo.getCharmGift39DNum() > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", detailInfo.getCharmGift39DNum(), config.getMaxNum()));
                            break;
                        default:
                            break;

                    }
                } else if (type == GENEROUS_TYPE) {
                    int size = 0;
                    switch (i) {
                        case 0:
                            size = detailInfo.getGenerousGift27DUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 1:
                            size = detailInfo.getGenerousGift39DUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 2:
                            size = detailInfo.getGenerousGift49DUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        default:
                            break;

                    }

                } else if (type == PASSION_TYPE) {
                    int size = 0;
                    switch (i) {
                        case 0:
                            size = detailInfo.getPassionMicUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 1:
                            size = detailInfo.getPassionGreetingReplyUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 2:
                            int size1 = detailInfo.getPassionMomentCommentUsers().size();
                            int size2 = detailInfo.getPassionMomentLikeUsers().size();
                            size = size1 + size2;
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            itemVO.setCompleteProgress1(String.format("%s/%s", size1, config.getMaxNum() / 2));
                            itemVO.setCompleteProgress2(String.format("%s/%s", size2, config.getMaxNum() / 2));
                            break;
                        case 3:
                            size = detailInfo.getPassionGreetingUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 4:
                            size = detailInfo.getPassionPrivateGiftUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 5:
                            size = detailInfo.getPassionGreetingBeRepliedUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        case 6:
                            size = detailInfo.getOnMicTime();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));

                            break;
                        case 7:
                            size = detailInfo.getPassionFriendsUsers().size();
                            itemVO.setNowPoint(size * config.getNumPerPoint());
                            itemVO.setState(size >= config.getMaxNum() ? 2
                                    : size > 0 ? 1 : 0);
                            itemVO.setCompleteProgress(String.format("%s/%s", size, config.getMaxNum()));
                            break;
                        default:
                            break;

                    }

                }
            }
            toList.add(itemVO);
        }
    }

    public OtherRankConfigVO testUidDay(int cmd, String uid, int addDays, int addValue) {
        if (ServerConfig.isProduct()) {
            throw new CommonH5Exception(ActivityHttpCode.AUTH_ERROR);
        }
        OtherRankConfigVO otherRankConfigVO = new OtherRankConfigVO();
        if (cmd == 1) {
            // 设置偏移天数
            activityCommonRedis.setCommonHashData(getHashTestDayKey(null), uid, String.valueOf(addDays));
            otherRankConfigVO.setScore(addDays);
        } else if (cmd == 2) {
            // 查询偏移天数
            int add = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
            otherRankConfigVO.setScore(add);
        } else if (cmd == 3) {
            //  执行当天的定时任务
            if (ServerConfig.isNotProduct()) {
                everyDayCheck();
            }
        } else if (cmd == 4) {
            if (ServerConfig.isNotProduct()) {
                CommonMqTopicData mqData = new CommonMqTopicData();
                mqData.setUid(uid);
                mqData.setValue(addValue);
                mqData.setItem("super_queen_test");
                syncAddHandle(uid, PASSION_TYPE, null, mqData);
            }

        }

        return otherRankConfigVO;
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        handleUserScore(null, data);
    }

    //  LlluminateYouStarSendGiftHandler
    public void handleUserScore(SendGiftData data, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        if (data != null) {
            // 发礼物数据
            String uid = data.getFrom_uid();
            String roomId = data.getRoomId();
            if (StringUtils.hasLength(roomId)) {
                syncAddHandle(uid, GENEROUS_TYPE, data, null);
                for (String aid : data.getAid_list()) {
                    syncAddHandle(aid, CHARM_TYPE, data, null);
                }
            } else {
                if (data.getSend_type() == 2 && data.getGiving_type() == 7) {
                    syncAddHandle(uid, PASSION_TYPE, data, null);
                }
            }
        } else {
            String uid = mqData.getUid();
            if (FROM_YOUR_CIRCLE_EVENT.equals(mqData.getItem())) {
                // 从你的荣耀圈活动过来的
                int type = Integer.parseInt(mqData.getHandleId());
                if (type == CHARM_TYPE || type == GENEROUS_TYPE) {
                    syncAddHandle(uid, type, null, mqData);
                }
            } else {
                if (CommonMqTaskConstant.GREETING_PRIVATE_MSG.equals(mqData.getItem())) {
                    if (mqData.getValue() == 1) {
                        // 主动打招呼
                        mqData.setRemainValue(GREETING_PRIVATE);
                        syncAddHandle(uid, PASSION_TYPE, null, mqData);
                    } else if (mqData.getValue() == 3) {
                        // 我回复别人的打招呼消息
                        mqData.setRemainValue(GREETING_BE_REPLIED);
                        syncAddHandle(uid, PASSION_TYPE, null, mqData);

                        // 别人回复我的打招呼消息
                        mqData.setRemainValue(GREETING_REPLY_OTHER);
                        syncAddHandle(mqData.getAid(), PASSION_TYPE, null, mqData);
                    }
                } else {
                    if (CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL.equals(mqData.getItem())) {
                        uid = RoomUtils.getRoomHostId(mqData.getRoomId());
                    }
                    syncAddHandle(uid, PASSION_TYPE, null, mqData);
                }
            }
        }
    }

    private boolean checkAc(String uid, CommonMqTopicData mqData) {
//        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
//        if (ServerConfig.isProduct() && activityData.getAcNameEn().contains("test")) {
//            boolean isWhiteTestUser = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
//            if (!isWhiteTestUser) {
//                // 灰度测试,只统计测试用户的
//                return false;
//            }
//        }
        Set<String> allJoinUsers = cacheDataService.getAllSetCache(getSetJoinKey(null), 2);
        if (!allJoinUsers.contains(uid)) {
            // 没报名参加活动
            return false;
        }
//        if (mqData != null && CommonMqTaskConstant.ON_MIC_TIME.equals(mqData.getItem())) {
//            String micTimeKey = getZetMicTimeKey();
//            int afterValue = activityCommonRedis.incrCommonZSetRankingScoreSimple(micTimeKey, uid, 1);
//
//        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 解决临界点，时间过期的问题
            return false;
        }
        return true;
    }

    private void syncAddHandle(String uid, int type, SendGiftData data, CommonMqTopicData mqData) {
        if (!checkAc(uid, mqData)) {
            return;
        }

        String now = getDay(uid);
        String detailKey = getHashDetailKey(null, now);
        String totalInfoKey = getHashTotalKey(null);
        String typeKey = getZetTypeKey(null, type);
        synchronized (stringPool.intern(getLocalQueenKey(uid))) {
            SuperQueen2025VO.DetailInfo detailInfo = cacheDataService.getSuperQueen2025VODetailInfo(detailKey, uid);
            SuperQueen2025VO.MyQueenInfo myQueenInfo = cacheDataService.getSuperQueen2025VOMyQueenInfo(totalInfoKey, uid);
            int addScore = 0;
            int maxLimit = 0;
            int nowNum = 0;
            int nowAddDNum = 0; // CHARM_TYPE,GENEROUS_TYPE 有值
            int taskAddScore = 0;// 指定任务增加积分
            ActivityCommonConfig.QueenConfig queenConfig;
            if (type == CHARM_TYPE) {
                if (mqData != null) {
                    addScore = mqData.getValue();
                    if (addScore <= 0) {
                        return;
                    }
                    doReportDetailEvent(uid, type, addScore, FROM_YOUR_CIRCLE_REPORT_EVENT);
                } else {
                    // 收礼
                    Map<Integer, ActivityCommonConfig.QueenConfig> queenCharmConfigMap = activityCommonConfig.getQueenCharmConfigList().stream()
                            .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                    nowAddDNum = data.getPrice() * data.getNumber();
                    addScore = nowAddDNum / 3;
                    if (addScore > 0) {
                        doReportDetailEvent(uid, type, addScore, "receive_gift");
                    }
                    if (data.getGid() == charmGift39DId) {
                        queenConfig = queenCharmConfigMap.get(2);
                        maxLimit = queenConfig.getMaxNum();
                        nowNum = detailInfo.getCharmGift39DNum();
                        if (nowNum < maxLimit) {
                            int add = Math.min((maxLimit - nowNum), data.getNumber());
                            detailInfo.setCharmGift39DNum(nowNum + add);
                            taskAddScore = add * queenConfig.getNumPerPoint();
                            addScore += taskAddScore;
                            doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                        }

                    } else if (data.getGid() == charmGift99DId) {
                        queenConfig = queenCharmConfigMap.get(0);
                        maxLimit = queenConfig.getMaxNum();
                        nowNum = detailInfo.getCharmGift99DNum();
                        if (nowNum < maxLimit) {
                            int add = Math.min((maxLimit - nowNum), data.getNumber());
                            detailInfo.setCharmGift99DNum(nowNum + add);
                            taskAddScore = add * queenConfig.getNumPerPoint();
                            addScore += taskAddScore;
                            doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                        }
                    } else if (data.getGid() == charmGift199DId) {
                        queenConfig = queenCharmConfigMap.get(1);
                        maxLimit = queenConfig.getMaxNum();
                        nowNum = detailInfo.getCharmGift199DNum();
                        if (nowNum < maxLimit) {
                            int add = Math.min((maxLimit - nowNum), data.getNumber());
                            detailInfo.setCharmGift199DNum(nowNum + add);
                            taskAddScore = add * queenConfig.getNumPerPoint();
                            addScore += taskAddScore;
                            doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                        }
                    }
                }
                if (addScore > 0) {
                    myQueenInfo.setCharmPoint(myQueenInfo.getCharmPoint() + addScore);
                    myQueenInfo.setCharmDPoint(myQueenInfo.getCharmDPoint() + nowAddDNum);
                    detailInfo.setCharmDayPoint(detailInfo.getCharmDayPoint() + addScore);
                    if (StringUtils.isEmpty(myQueenInfo.getCharmFirstDay()) &&
                            myQueenInfo.getCharmPoint() >= TYPE_REWARD_TARGET_MAP.get(type)) {
                        myQueenInfo.setCharmFirstDay(now);
                        asyncHandleQueenRes(uid, type, true, 0);
                        detailInfo.setCharmAgainGetTime(DateHelper.getNowSeconds());
                        myQueenInfo.getCharmAgainSet().add(now);
                        asyncHandleQueenRes(uid, type, false, myQueenInfo.getCharmAgainSw());
                    } else if (myQueenInfo.getCharmPoint() >= TYPE_REWARD_TARGET_MAP.get(type)
                            && detailInfo.getCharmAgainGetTime() <= 0 && !myQueenInfo.getCharmAgainSet().contains(now)) {
                        detailInfo.setCharmAgainGetTime(DateHelper.getNowSeconds());
                        myQueenInfo.getCharmAgainSet().add(now);
                        asyncHandleQueenRes(uid, type, false, myQueenInfo.getCharmAgainSw());
                    }
                }
            } else if (type == GENEROUS_TYPE) {
                if (mqData != null) {
                    addScore = mqData.getValue();
                    if (addScore <= 0) {
                        return;
                    }
                    doReportDetailEvent(uid, type, addScore, FROM_YOUR_CIRCLE_REPORT_EVENT);
                } else {
                    // 发礼物
                    Map<Integer, ActivityCommonConfig.QueenConfig> queenGenerousConfigMap = activityCommonConfig.getQueenGenerousConfigList().stream()
                            .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                    nowAddDNum = data.getPrice() * data.getNumber() * data.getAid_list().size();
                    addScore = nowAddDNum / 3;
                    if (addScore > 0) {
                        doReportDetailEvent(uid, type, addScore, "send_gift");
                    }
                    Set<String> disUsers;
                    int add = 0;
                    if (data.getGid() == generousGift27DId) {
                        queenConfig = queenGenerousConfigMap.get(0);
                        Set<String> sendFemaleUsers = data.getAid_list().stream().filter(item -> actorDao.getActorDataFromCache(item).getFb_gender() == 2).collect(Collectors.toSet());
                        if (sendFemaleUsers.size() > 0) {
                            maxLimit = queenConfig.getMaxNum();
//                    nowNum = detailInfo.getGenerousGift27DNum();
//                    if (nowNum < maxLimit) {
//                        int add = Math.min((maxLimit - nowNum), sendFemaleCount * data.getNumber());
//                        detailInfo.setGenerousGift27DNum(nowNum + add);
//                        addScore += (add * 10);
//                    }
                            disUsers = detailInfo.getGenerousGift27DUsers();
                            nowNum = disUsers.size();
                            for (String aid : sendFemaleUsers) {
                                if (disUsers.contains(aid)) {
                                    continue;
                                }
                                if (nowNum >= maxLimit) {
                                    break;
                                }
                                add++;
                                nowNum++;
                                disUsers.add(aid);
                            }
                            if (add > 0) {
                                taskAddScore = add * queenConfig.getNumPerPoint();
                                addScore += taskAddScore;
                                doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                            }
                        }
                    } else if (data.getGid() == generousGift39DId) {
                        queenConfig = queenGenerousConfigMap.get(1);
                        Set<String> sendMaleUsers = data.getAid_list().stream().filter(item -> actorDao.getActorDataFromCache(item).getFb_gender() != 2).collect(Collectors.toSet());
                        if (sendMaleUsers.size() > 0) {
                            maxLimit = queenConfig.getMaxNum();
                            disUsers = detailInfo.getGenerousGift39DUsers();
                            nowNum = disUsers.size();
                            for (String aid : sendMaleUsers) {
                                if (disUsers.contains(aid)) {
                                    continue;
                                }
                                if (nowNum >= maxLimit) {
                                    break;
                                }
                                add++;
                                nowNum++;
                                disUsers.add(aid);
                            }
                            if (add > 0) {
                                taskAddScore = add * queenConfig.getNumPerPoint();
                                addScore += taskAddScore;
                                doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                            }
                        }
                    } else if (data.getGid() == generousGift49DId) {
                        queenConfig = queenGenerousConfigMap.get(2);
                        Set<String> newUsers = data.getAid_list().stream().filter(item -> ActorUtils.isNewRegisterActor(item, 7)).collect(Collectors.toSet());
                        if (newUsers.size() > 0) {
                            maxLimit = queenConfig.getMaxNum();
                            disUsers = detailInfo.getGenerousGift49DUsers();
                            nowNum = disUsers.size();
                            for (String aid : newUsers) {
                                if (disUsers.contains(aid)) {
                                    continue;
                                }
                                if (nowNum >= maxLimit) {
                                    break;
                                }
                                add++;
                                nowNum++;
                                disUsers.add(aid);
                            }
                            if (add > 0) {
                                taskAddScore = add * queenConfig.getNumPerPoint();
                                addScore += taskAddScore;
                                doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                            }
                        }
                    }
                }
                if (addScore > 0) {
                    myQueenInfo.setGenerousPoint(myQueenInfo.getGenerousPoint() + addScore);
                    detailInfo.setGenerousDayPoint(detailInfo.getGenerousDayPoint() + addScore);
                    myQueenInfo.setGenerousDPoint(myQueenInfo.getGenerousDPoint() + nowAddDNum);
                    if (StringUtils.isEmpty(myQueenInfo.getGenerousFirstDay()) &&
                            myQueenInfo.getGenerousPoint() >= TYPE_REWARD_TARGET_MAP.get(type)) {
                        myQueenInfo.setGenerousFirstDay(now);
                        asyncHandleQueenRes(uid, type, true, 0);
                        detailInfo.setGenerousAgainGetTime(DateHelper.getNowSeconds());
                        myQueenInfo.getGenerousAgainSet().add(now);
                        asyncHandleQueenRes(uid, type, false, myQueenInfo.getGenerousAgainSw());
                    } else if (myQueenInfo.getGenerousPoint() >= TYPE_REWARD_TARGET_MAP.get(type)
                            && detailInfo.getGenerousAgainGetTime() <= 0 && !myQueenInfo.getGenerousAgainSet().contains(now)) {
                        detailInfo.setGenerousAgainGetTime(DateHelper.getNowSeconds());
                        myQueenInfo.getGenerousAgainSet().add(now);
                        asyncHandleQueenRes(uid, type, false, myQueenInfo.getGenerousAgainSw());
                    }
                }
            } else if (type == PASSION_TYPE) {
                Map<Integer, ActivityCommonConfig.QueenConfig> queenPassionConfigMap = activityCommonConfig.getQueenPassionConfigList().stream()
                        .collect(Collectors.toMap(ActivityCommonConfig.QueenConfig::getOrder, Function.identity()));
                Set<String> disUsers;
                if (data != null) {
                    // 私信礼物
                    queenConfig = queenPassionConfigMap.get(4);
                    maxLimit = queenConfig.getMaxNum();
                    String aid = null;
                    if (!CollectionUtils.isEmpty(data.getAid_list())) {
                        Iterator<String> iterator = data.getAid_list().iterator();
                        if (iterator.hasNext()) {
                            aid = iterator.next();
                        }
                    }
                    if (StringUtils.isEmpty(aid)) {
                        logger.info("private gift aid is null SendGiftData:{}", JSON.toJSONString(data));
                        return;
                    }
                    disUsers = detailInfo.getPassionPrivateGiftUsers();
                    nowNum = disUsers.size();
                    if (disUsers.contains(aid) || nowNum >= maxLimit) {
                        return;
                    }
                    disUsers.add(aid);
                    addScore = queenConfig.getNumPerPoint();
                    taskAddScore = addScore;
                    doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());

                } else if (CommonMqTaskConstant.INVITE_USER_ON_MIC_ALL.equals(mqData.getItem())) {
                    // 邀请上麦
                    queenConfig = queenPassionConfigMap.get(0);
                    maxLimit = queenConfig.getMaxNum();
                    String aid = mqData.getAid();
                    disUsers = detailInfo.getPassionMicUsers();
                    nowNum = disUsers.size();
                    if (disUsers.contains(aid) || nowNum >= maxLimit) {
                        return;
                    }
                    disUsers.add(aid);
                    addScore = queenConfig.getNumPerPoint();
                    taskAddScore = addScore;
                    doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());

                } else if (CommonMqTaskConstant.GREETING_PRIVATE_MSG.equals(mqData.getItem())) {
                    String aid = mqData.getAid();
                    if (mqData.getRemainValue() == GREETING_PRIVATE) {
                        // 对陌生人打招呼
                        queenConfig = queenPassionConfigMap.get(3);
                        disUsers = detailInfo.getPassionGreetingUsers();
                    } else if (mqData.getRemainValue() == GREETING_BE_REPLIED) {
                        // 回复打招呼信息
                        queenConfig = queenPassionConfigMap.get(1);
                        disUsers = detailInfo.getPassionGreetingReplyUsers();
                    }else if (mqData.getRemainValue() == GREETING_REPLY_OTHER) {
                        // 打招呼信息被回复
                        queenConfig = queenPassionConfigMap.get(5);
                        disUsers = detailInfo.getPassionGreetingBeRepliedUsers();
                        aid = mqData.getUid();
                    }else {
                        return;
                    }
                    maxLimit = queenConfig.getMaxNum();
                    nowNum = disUsers.size();
                    if (disUsers.contains(aid) || nowNum >= maxLimit) {
                        return;
                    }
                    disUsers.add(aid);
                    addScore = queenConfig.getNumPerPoint();
                    taskAddScore = addScore;
                    doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                } else if (CommonMqTaskConstant.COMMENT_MOMENT.equals(mqData.getItem())
                        || CommonMqTaskConstant.LIKE_MOMENT.equals(mqData.getItem())) {
                    // 朋友圈评论或者点赞
                    queenConfig = queenPassionConfigMap.get(2);
                    maxLimit = queenConfig.getMaxNum() / 2;
                    String aid = mqData.getAid();
                    disUsers = detailInfo.getPassionMomentCommentUsers();
                    nowNum = disUsers.size();

                    Set<String> disUsers2 = detailInfo.getPassionMomentLikeUsers();
                    int nowNum2 = disUsers2.size();

                    if (disUsers.contains(aid) || disUsers2.contains(aid)) {
                        return;
                    }
                    if (CommonMqTaskConstant.COMMENT_MOMENT.equals(mqData.getItem())) {
                        if (nowNum >= maxLimit) {
                            return;
                        }
                        disUsers.add(aid);
                    } else {
                        if (nowNum2 >= maxLimit) {
                            return;
                        }
                        disUsers2.add(aid);
                    }

                    addScore = queenConfig.getNumPerPoint();
                    taskAddScore = addScore;
                    doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());

                }  else if (CommonMqTaskConstant.ON_MIC_TIME.equals(mqData.getItem())) {
                    // 在麦30分钟
                    queenConfig = queenPassionConfigMap.get(6);
                    maxLimit = queenConfig.getMaxNum();
                    nowNum = detailInfo.getOnMicTime();
                    if (nowNum < maxLimit) {
                        detailInfo.setOnMicTime(nowNum + 1);
                        addScore = queenConfig.getNumPerPoint();
                        taskAddScore = addScore;
                        doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                    }
                }  else if (CommonMqTaskConstant.ADD_FRIEND.equals(mqData.getItem())) {
                    // 加好友
                    queenConfig = queenPassionConfigMap.get(7);
                    maxLimit = queenConfig.getMaxNum();
                    String aid = mqData.getAid();
                    ActorData actorData = actorDao.getActorDataFromCache(aid);
                    if (actorData == null||StringUtils.isEmpty(actorData.getTn_id())) {
                        return;
                    }
                    String tnId = actorData.getTn_id();
                    disUsers = detailInfo.getPassionFriendsUsers();
                    nowNum = disUsers.size();
                    if (disUsers.contains(tnId) || nowNum >= maxLimit) {
                        return;
                    }
                    disUsers.add(tnId);
                    addScore = queenConfig.getNumPerPoint();
                    taskAddScore = addScore;
                    doReportDetailEvent(uid, type, taskAddScore, queenConfig.getEventDesc());
                } else if ("super_queen_test".equals(mqData.getItem())) {
                    addScore = mqData.getValue();
                }

                if (addScore > 0) {
                    OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
                    int maxPassion = getPassionTodayMax(activityData.getStartTime(),
                            Math.min(DateHelper.getNowSeconds(), activityData.getEndTime() - 10));
                    // 解决重启时两个模块并存时，产生的脏数据
                    myQueenInfo.setPassionPoint(Math.min(myQueenInfo.getPassionPoint() + addScore, maxPassion));
                    detailInfo.setPassionDayPoint(detailInfo.getPassionDayPoint() + addScore);
                    if (StringUtils.isEmpty(myQueenInfo.getPassionFirstDay()) &&
                            myQueenInfo.getPassionPoint() >= TYPE_REWARD_TARGET_MAP.get(type)) {
                        myQueenInfo.setPassionFirstDay(now);
                        asyncHandleQueenRes(uid, type, true, 0);
                        detailInfo.setPassionAgainGetTime(DateHelper.getNowSeconds());
                        myQueenInfo.getPassionAgainSet().add(now);
                        asyncHandleQueenRes(uid, type, false, myQueenInfo.getPassionAgainSw());
                    } else if (myQueenInfo.getPassionPoint() >= TYPE_REWARD_TARGET_MAP.get(type)
                            && detailInfo.getPassionAgainGetTime() <= 0 && !myQueenInfo.getPassionAgainSet().contains(now)) {
                        detailInfo.setPassionAgainGetTime(DateHelper.getNowSeconds());
                        myQueenInfo.getPassionAgainSet().add(now);
                        asyncHandleQueenRes(uid, type, false, myQueenInfo.getPassionAgainSw());
                    }
                }

            }

            if (addScore > 0) {
                if (!StringUtils.isEmpty(myQueenInfo.getPassionFirstDay())
                        && !StringUtils.isEmpty(myQueenInfo.getCharmFirstDay())
                        && !StringUtils.isEmpty(myQueenInfo.getGenerousFirstDay())
                        && StringUtils.isEmpty(myQueenInfo.getSuperFirstDay())) {
                    myQueenInfo.setSuperFirstDay(now);
                    asyncHandleQueenRes(uid, SUPER_TYPE, true, 0);
                }
                handleLevel(uid, myQueenInfo, type, addScore);
                activityCommonRedis.setCommonHashData(detailKey, uid, JSONObject.toJSONString(detailInfo));
                activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(myQueenInfo));
                if (type == PASSION_TYPE) {
                    activityCommonRedis.addCommonZSetRankingScoreTime(typeKey, uid, myQueenInfo.getPassionPoint());
//                    int afScore = activityCommonRedis.incrCommonZSetRankingScore(typeKey, uid, addScore);
//                    if (afScore > myQueenInfo.getPassionPoint()) {
//                        activityCommonRedis.addCommonZSetRankingScore(typeKey, uid, myQueenInfo.getPassionPoint());
//                    }
                } else {
                    activityCommonRedis.incrCommonZSetRankingScoreSimple(typeKey, uid, addScore);
                }
                leftPushAllHistoryList(uid, type, addScore);
                logger.info("success add uid:{} type:{} addScore:{} nowAddDNum:{}",
                        uid, type, addScore, nowAddDNum);

            }
        }
    }

    private void handleLevel(String aid, SuperQueen2025VO.MyQueenInfo myQueenInfo, int type, int addScore) {
        int nowScore = 0;
        Map<String, Integer> levelMap = null;
        boolean isNoticeGetQueen = false;
        if (CHARM_TYPE == type) {
            nowScore = myQueenInfo.getCharmPoint();
            levelMap = myQueenInfo.getCharmLevelMapCollect();
            isNoticeGetQueen = StringUtils.isEmpty(myQueenInfo.getCharmFirstDay());
        } else if (GENEROUS_TYPE == type) {
            nowScore = myQueenInfo.getGenerousPoint();
            levelMap = myQueenInfo.getGenerousLevelMapCollect();
            isNoticeGetQueen = StringUtils.isEmpty(myQueenInfo.getGenerousFirstDay());
        } else if (PASSION_TYPE == type) {
            nowScore = myQueenInfo.getPassionPoint();
            levelMap = myQueenInfo.getPassionLevelMapCollect();
            isNoticeGetQueen = StringUtils.isEmpty(myQueenInfo.getPassionFirstDay());
        }

        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        String before = DateHelper.ARABIAN.formatDateInDay
                (new Date(activityData.getStartTime() * 1000L));
        String after = getDay(aid);
        int d = (int) (DateSupport.calculateDaysBetween(before, after) + 1);
        isNoticeGetQueen = isNoticeGetQueen && d >= 9;
        logger.info("handleLevel aid:{} isNoticeGetQueen:{} d:{} before:{}", aid, isNoticeGetQueen, d, before);
        int oldScore = Math.max(nowScore - addScore, 0);
        List<Integer> scoreLevelList = TYPE_SCORE_MAP.get(type);
        int maxSize = scoreLevelList.size();
        int oldIndex = getIndexLevel(oldScore, scoreLevelList);
        int afterIndex = getIndexLevel(nowScore, scoreLevelList);
        if (afterIndex > oldIndex && levelMap != null) {
//            logger.info("handleLevel--> aid:{} type:{} oldIndex:{} oldScore:{} afterIndex:{} nowScore:{} levelMap:{}",
//                    aid, type, oldIndex, oldScore, afterIndex, nowScore, levelMap);
            for (int i = oldIndex + 1; i <= afterIndex; i++) {
                String level = String.valueOf(i);
                int state = levelMap.getOrDefault(level, 0);
                if (state == 0) {
                    levelMap.put(level, 1);
                    List<String> resKeyList = TYPE_SCORE_KEY_MAP.get(type);
                    int noticeLevel = 1 == i || 5 == i || 6 == i ? i : 0;// 1或者5,6通知
                    if (resKeyList != null && i <= 6) {
                        asyncHandleLevelRes(aid, resKeyList.get(i - 1), type, noticeLevel, level, isNoticeGetQueen);
                    }
                }
            }
        }

    }

    /**
     * @param myQueenInfo
     * @param type
     * @return 取值0-6
     */
    private int getMaxLevel(SuperQueen2025VO.MyQueenInfo myQueenInfo, int type) {
        if (myQueenInfo == null) {
            return 0;
        }
        int maxLevel = 0;
        Map<String, Integer> levelMap = null;
        if (CHARM_TYPE == type) {
            levelMap = myQueenInfo.getCharmLevelMapCollect();
        } else if (GENEROUS_TYPE == type) {
            levelMap = myQueenInfo.getGenerousLevelMapCollect();
        } else if (PASSION_TYPE == type) {
            levelMap = myQueenInfo.getPassionLevelMapCollect();
        }
        if (levelMap == null || levelMap.isEmpty()) {
            return 0;
        }
        for (Map.Entry<String, Integer> entry : levelMap.entrySet()) {
            try {
                if (entry.getValue() == 1) {
                    maxLevel = Math.max(Integer.parseInt(entry.getKey()), maxLevel);
                }
            } catch (NumberFormatException e) {
                continue;
            }
        }
        return maxLevel;
    }

    /**
     * 沙特时间0点10分执行
     * 1 减积分
     * 2 昨天未下发惊喜奖励的通知停止
     * 3 今天保级成功后，已下发惊喜奖励的通知
     * 4 活动第五天，零点通知热情女王积分大于2000，但未获得热情女王称号的用户
     */
    public void everyDayCheck() {
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        // 活动第一天的0点10分不用执行
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (DateHelper.getNowSeconds() <= activityData.getStartTime() + TimeUnit.MINUTES.toSeconds(30)) {
            return;
        }
        Set<String> allJoinUsers = cacheDataService.getAllSetCache(getSetJoinKey(null), 2);
        Map<Integer, Set<String>> noticeMap = new HashMap<>();
        Set<String> charmSet = new HashSet<>();
        Set<String> generousSet = new HashSet<>();
        Set<String> passionSet = new HashSet<>();
        noticeMap.put(CHARM_TYPE, charmSet);
        noticeMap.put(GENEROUS_TYPE, generousSet);
        noticeMap.put(PASSION_TYPE, passionSet);
        String yesterday = DateHelper.ARABIAN.getYesterdayStr(new Date());
        String totalInfoKey = getHashTotalKey(null);
        for (String uid : allJoinUsers) {
            if (ServerConfig.isNotProduct()) {
                yesterday = getDay(uid, false);
            }
            SuperQueen2025VO.MyQueenInfo myQueenInfo = cacheDataService.getSuperQueen2025VOMyQueenInfo(totalInfoKey, uid);
//                SuperQueen2025VO.DetailInfo yesterdayDetailInfo = cacheDataService.getSuperQueen2025VODetailInfo(yesterdayKey, uid);
            for (Integer type : QUEEN_ALL_LIST) {
                if (CHARM_TYPE == type && StringUtils.hasLength(myQueenInfo.getCharmFirstDay())) {
                    syncReduceHandle(uid, type);
                    if (!myQueenInfo.getCharmAgainSet().contains(yesterday) && myQueenInfo.getCharmAgainSw() == 1) {
                        charmSet.add(uid);
                    }
                } else if (GENEROUS_TYPE == type && StringUtils.hasLength(myQueenInfo.getGenerousFirstDay())) {
                    syncReduceHandle(uid, type);
                    if (!myQueenInfo.getGenerousAgainSet().contains(yesterday) && myQueenInfo.getGenerousAgainSw() == 1) {
                        generousSet.add(uid);
                    }
                } else if (PASSION_TYPE == type && StringUtils.hasLength(myQueenInfo.getPassionFirstDay())) {
                    syncReduceHandle(uid, type);
                    if (!myQueenInfo.getPassionAgainSet().contains(yesterday) && myQueenInfo.getPassionAgainSw() == 1) {
                        passionSet.add(uid);
                    }
                }
            }
        }

        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                noticeMap.forEach((k, v) -> {
                    List<String> titleList = TYPE_STOP_TITLE_NOTICE_MAP.get(k);
                    List<String> bodyList = TYPE_STOP_BODY_NOTICE_MAP.get(k);
                    if (CHARM_TYPE == k) {
                        logger.info("notice type:{} size:{}", k, v.size());
                        v.forEach(item -> {
                            sendOfficialMsg(titleList, bodyList, item);
                        });
                    } else if (GENEROUS_TYPE == k) {
                        logger.info("notice type:{} size:{}", k, v.size());
                        v.forEach(item -> {
                            sendOfficialMsg(titleList, bodyList, item);
                        });

                    } else if (PASSION_TYPE == k) {
                        logger.info("notice type:{} size:{}", k, v.size());
                        v.forEach(item -> {
                            sendOfficialMsg(titleList, bodyList, item);
                        });
                    }
                });
            }
        });
    }

    private void sendOfficialMsg(List<String> titleList, List<String> bodyList, String item) {
        ActorData actorData = actorDao.getActorDataFromCache(item);
        int slang = actorData.getSlang();
        String title = slang == SLangType.ARABIC ? titleList.get(1) : titleList.get(0);
        String body = slang == SLangType.ARABIC ? bodyList.get(1) : bodyList.get(0);
        commonOfficialMsg(item, "", 0, 0, "",
                title, body, ACTIVITY_URL);
    }


    // 0 点减当天积分
    private void syncReduceHandle(String uid, int type) {
        String now = getDay(uid);
        String detailKey = getHashDetailKey(null, now);
        String totalInfoKey = getHashTotalKey(null);
        String typeKey = getZetTypeKey(null, type);
        int reduce = TYPE_REWARD_EXTRA_REDUCE_MAP.get(type);
        synchronized (stringPool.intern(getLocalQueenKey(uid))) {
            SuperQueen2025VO.DetailInfo detailInfo = cacheDataService.getSuperQueen2025VODetailInfo(detailKey, uid);
            SuperQueen2025VO.MyQueenInfo myQueenInfo = cacheDataService.getSuperQueen2025VOMyQueenInfo(totalInfoKey, uid);
            boolean isChange = false;
            if (CHARM_TYPE == type && StringUtils.hasLength(myQueenInfo.getCharmFirstDay()) && detailInfo.getIsCharmAgainReduce() <= 0) {
                detailInfo.setIsCharmAgainReduce(1);
                myQueenInfo.setCharmPoint(myQueenInfo.getCharmPoint() + reduce);
                detailInfo.setCharmDayPoint(detailInfo.getCharmDayPoint() + reduce);
                // 保级成功了
                if (myQueenInfo.getCharmPoint() >= TYPE_REWARD_TARGET_MAP.get(type)
                        && detailInfo.getCharmAgainGetTime() <= 0 && !myQueenInfo.getCharmAgainSet().contains(now)) {
                    detailInfo.setCharmAgainGetTime(DateHelper.getNowSeconds());
                    myQueenInfo.getCharmAgainSet().add(now);
                    asyncHandleQueenRes(uid, type, false, myQueenInfo.getCharmAgainSw());
                }
                isChange = true;
            } else if (GENEROUS_TYPE == type && StringUtils.hasLength(myQueenInfo.getGenerousFirstDay()) && detailInfo.getIsGenerousAgainReduce() <= 0) {
                detailInfo.setIsGenerousAgainReduce(1);
                myQueenInfo.setGenerousPoint(myQueenInfo.getGenerousPoint() + reduce);
                detailInfo.setGenerousDayPoint(detailInfo.getGenerousDayPoint() + reduce);
                // 保级成功了
                if (myQueenInfo.getGenerousPoint() >= TYPE_REWARD_TARGET_MAP.get(type)
                        && detailInfo.getGenerousAgainGetTime() <= 0 && !myQueenInfo.getGenerousAgainSet().contains(now)) {
                    detailInfo.setGenerousAgainGetTime(DateHelper.getNowSeconds());
                    myQueenInfo.getGenerousAgainSet().add(now);
                    asyncHandleQueenRes(uid, type, false, myQueenInfo.getGenerousAgainSw());
                }
                isChange = true;
            } else if (PASSION_TYPE == type && StringUtils.hasLength(myQueenInfo.getPassionFirstDay()) && detailInfo.getIsPassionAgainReduce() <= 0) {
                detailInfo.setIsPassionAgainReduce(1);
                myQueenInfo.setPassionPoint(myQueenInfo.getPassionPoint() + reduce);
                detailInfo.setPassionDayPoint(detailInfo.getPassionDayPoint() + reduce);
                // 保级成功了
                if (myQueenInfo.getPassionPoint() >= TYPE_REWARD_TARGET_MAP.get(type)
                        && detailInfo.getPassionAgainGetTime() <= 0 && !myQueenInfo.getPassionAgainSet().contains(now)) {
                    detailInfo.setPassionAgainGetTime(DateHelper.getNowSeconds());
                    myQueenInfo.getPassionAgainSet().add(now);
                    asyncHandleQueenRes(uid, type, false, myQueenInfo.getPassionAgainSw());
                }
                isChange = true;
            }
            if (isChange) {
                activityCommonRedis.setCommonHashData(detailKey, uid, JSONObject.toJSONString(detailInfo));
                activityCommonRedis.setCommonHashData(totalInfoKey, uid, JSONObject.toJSONString(myQueenInfo));
                activityCommonRedis.incrCommonZSetRankingScoreSimple(typeKey, uid, reduce);
                leftPushAllHistoryList(uid, type, reduce);
                doReportDetailEvent(uid, type, reduce, "");
                logger.info("success reduce uid:{} type:{} reduceScore:{}", uid, type, reduce);
            } else {
                logger.info("fail reduce because already reduce  uid:{} type:{} reduceScore:{}", uid, type, reduce);
            }
        }
    }

    private void asyncHandleQueenRes(String aid, int type, boolean isMain, int state) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String resKey = isMain ? TYPE_REWARD_MAIN_KEY_MAP.get(type) : TYPE_REWARD_EXTRA_KEY_MAP.get(type);
                if (StringUtils.isEmpty(resKey)) {
                    logger.info("resKey is empty handleQueenRes fail aid:{} type:{}", aid, type);
                    return;
                }
                ActorData actorData = actorDao.getActorData(aid);
                String tnId = actorData.getTn_id();
                if (StringUtils.hasLength(tnId)) {
                    String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(null), tnId);
                    if (StringUtils.isEmpty(bindAid) || aid.equals(bindAid)) {
                        String eventTitle = getEventTitle(type, isMain ? 1 : 2);
                        resourceKeyHandlerService.sendResourceData(aid, resKey,
                                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                        activityCommonRedis.setCommonHashData(getHashBindKey(null), tnId, aid);
                        if (!isMain && state == 1) {
                            List<String> titleList = TYPE_START_TITLE_NOTICE_MAP.get(type);
                            List<String> bodyList = TYPE_START_BODY_NOTICE_MAP.get(type);
                            sendOfficialMsg(titleList, bodyList, aid);
                        }
                        if (isMain) {
                            doDoneQueenEvent(aid, type);
                        }
                        logger.info("handleQueenRes success aid:{} resKey:{} type:{} tnId:{}", aid, resKey, type, tnId);
                    } else {
                        logger.info("handleQueenRes fail aid:{} resKey:{} type:{} tnId:{} bindAid:{}", aid, resKey, type, tnId, bindAid);
                    }
                } else {
                    String eventTitle = getEventTitle(type, isMain ? 1 : 2);
                    resourceKeyHandlerService.sendResourceData(aid, resKey,
                            eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                    if (!isMain && state == 1) {
                        List<String> titleList = TYPE_START_TITLE_NOTICE_MAP.get(type);
                        List<String> bodyList = TYPE_START_BODY_NOTICE_MAP.get(type);
                        sendOfficialMsg(titleList, bodyList, aid);
                    }
                    if (isMain) {
                        doDoneQueenEvent(aid, type);
                    }
                    logger.info("handleQueenRes success tnId is empty aid:{} resKey:{} type:{} tnId:{}", aid, resKey, type, tnId);
                }
            }
        });
    }

    public void distributionRanking() {
        distributionRanking(CHARM_TYPE);
        distributionRanking(GENEROUS_TYPE);
        distributionRanking(PASSION_TYPE);
    }

    public String testTotal() {
        distributionRanking(1);
        return "";
    }

    // 下发榜单奖励
    private void distributionRanking(int rankType) {
        try {
            int length = 3;
            List<String> resKeyList = TYPE_RANK_KEY_MAP.get(rankType);
            Map<String, Integer> totalRankingMap = activityCommonRedis.getCommonRankingMap(getZetTypeKey(null, rankType), length);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : totalRankingMap.entrySet()) {
                if (rank > length) {
                    continue;
                }
                String aid = entry.getKey();
                String resKey = resKeyList.get(rank - 1);
                handleRes(aid, resKey, rankType);
                rank += 1;
            }

        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }


    public SuperQueen2025VO honorHallMyInfo(String activityId, String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivity(activityId);
        SuperQueen2025VO vo = new SuperQueen2025VO();
        vo.setStartTime(activityData.getStartTime());
        vo.setEndTime(activityData.getEndTime());
        SuperQueen2025VO.HonorHallVO honorHallVO = superQueen2025Service.fillStatesVO(uid);
        OtherRankingListVO myInfo = new OtherRankingListVO();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        myInfo.setUid(uid);
        myInfo.setName(actorData.getName());
        myInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        myInfo.setRidData(actorData.getRidData());
        honorHallVO.setMyInfo(myInfo);
        honorHallVO.setGetHonorReward(activityCommonRedis.isCommonSetData(getSetHonorGetKey(HONOR_ACTIVITY_ID), uid));
        vo.setHonorHallVO(honorHallVO);
        return vo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_30M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public SuperQueen2025VO.HonorHallVO fillStatesVO(String uid) {
        SuperQueen2025VO.HonorHallVO honorHallVO = new SuperQueen2025VO.HonorHallVO();
        List<Integer> charmStatesList = new ArrayList<>();
        List<Integer> generousStatesList = new ArrayList<>();
        List<Integer> passionStatesList = new ArrayList<>();
        List<Integer> superStatesList = new ArrayList<>();

        Map<Integer, Integer> rankTypeCountMap = new HashMap<>(); // 各种女王获得的统计

        fillStatesList(uid, charmStatesList, CHARM_TYPE, rankTypeCountMap);
        fillStatesList(uid, generousStatesList, GENEROUS_TYPE, rankTypeCountMap);
        fillStatesList(uid, passionStatesList, PASSION_TYPE, rankTypeCountMap);
        fillStatesList(uid, superStatesList, SUPER_TYPE, rankTypeCountMap);

        if (!CollectionUtils.isEmpty(rankTypeCountMap)) {
            Integer badgeId = null;
            if (rankTypeCountMap.getOrDefault(SUPER_TYPE, 0) > 0) {
                badgeId = TYPE_BADGE_ID_MAP.get(SUPER_TYPE);
            } else {
                Integer maxKey = rankTypeCountMap.entrySet()
                        .stream()
                        .max(Map.Entry.comparingByValue())
                        .get()
                        .getKey();
                badgeId = TYPE_BADGE_ID_MAP.get(maxKey);
            }
            logger.info("rankTypeCountMap:{} badgeId:{}", rankTypeCountMap, badgeId);
            if (badgeId != null) {
                BadgeListData badgeListData = badgeListDao.findData(badgeId);
                honorHallVO.setQueenBadgeUrl(badgeListData.getIcon());
            }

        }
        honorHallVO.setCharmStatesList(charmStatesList);
        honorHallVO.setGenerousStatesList(generousStatesList);
        honorHallVO.setPassionStatesList(passionStatesList);
        honorHallVO.setSuperStatesList(superStatesList);

        return honorHallVO;
    }

    private void fillStatesList(String aid, List<Integer> statesList, int rankType, Map<Integer, Integer> rankTypeCountMap) {
        statesList.addAll(Arrays.asList(0, 0, 0)); // Q2依次为4,5,6月对应的女王获取
        int i = 0;  // 月份递增
        for (Map<Integer, List<String>> item : honorHistoryList) {
            // 遍历月份
            for (Map.Entry<Integer, List<String>> getEntry : item.entrySet()) {
                // 遍历女王类型
                if (rankType == getEntry.getKey()) {
                    if (getEntry.getValue().contains(aid)) {
                        statesList.set(i, 1);
                        rankTypeCountMap.merge(rankType, 1, Integer::sum);
                    }
                    break;
                }
            }
            i++;
        }
    }

    public SuperQueen2025VO honorHallList(String activityId, String uid, int rankType, int month, int page) {
        if (rankType < 1 || rankType > 4 || month < 1 || month > 3) {
            logger.info("not support type:{} month:{}", rankType, month);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        SuperQueen2025VO vo = new SuperQueen2025VO();
        SuperQueen2025VO.HonorHallVO honorHallVO = superQueen2025Service.cacheHonorHallList(rankType, month, page);
        if (page == 1) {
            OtherRankingListVO myInfo = new OtherRankingListVO();
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            myInfo.setName(actorData.getName());
            myInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myInfo.setRidData(actorData.getRidData());
            myInfo.setUid(uid);
            honorHallVO.setMyInfo(myInfo);
        }
        vo.setHonorHallVO(honorHallVO);
        return vo;
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public SuperQueen2025VO.HonorHallVO cacheHonorHallList(int rankType, int month, int page) {
        SuperQueen2025VO.HonorHallVO honorHallVO = new SuperQueen2025VO.HonorHallVO();
        List<OtherRankingListVO> rankingList = new ArrayList<>();
        if (honorHistoryList.size() < month) {
            logger.info("not support month:{}", month);
            return honorHallVO;
        }
        Map<Integer, List<String>> itemMaps = honorHistoryList.get(month - 1);
        List<String> aidList = itemMaps.get(rankType);

        PageUtils.PageData<String> pageData = PageUtils.getPageData(aidList, page, 20);
        for (String aid : pageData.list) {
            OtherRankingListVO itemVO = new OtherRankingListVO();
            ActorData actorData = actorDao.getActorDataFromCache(aid);
            itemVO.setName(actorData.getName());
            itemVO.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            itemVO.setRidData(actorData.getRidData());
            itemVO.setUid(aid);
            rankingList.add(itemVO);
        }
        honorHallVO.setRankingList(rankingList);
        honorHallVO.setNextUrl(pageData.nextPage > 0 ? String.valueOf(pageData.nextPage) : "");
        honorHallVO.setTotalCount(pageData.totalSize);
        return honorHallVO;
    }

    public void honorHallCollect(String activityId, String uid) {
        checkActivityTime(HONOR_ACTIVITY_ID);
        if (activityCommonRedis.isCommonSetData(getSetHonorGetKey(HONOR_ACTIVITY_ID), uid) == 1) {
            logger.info("get already uid:{}", uid);
            throw new CommonH5Exception(ActivityHttpCode.CARROM_COLLECT_FAILED);
        }
        boolean isGetQueen = false;
        for (Map<Integer, List<String>> item : honorHistoryList) {
            for (Map.Entry<Integer, List<String>> getEntry : item.entrySet()) {
                if (getEntry.getValue().contains(uid)) {
                    isGetQueen = true;
                    break;
                }

            }
            if (isGetQueen) {
                break;
            }
        }
        logger.info("isGetQueen {}", isGetQueen);
        activityCommonRedis.addCommonSetData(getSetHonorGetKey(HONOR_ACTIVITY_ID), uid);
        resourceKeyHandlerService.sendResourceData(uid, isGetQueen ? SUPER_QUEEN_HONOR_KEY : SUPER_QUEEN_NO_HONOR_KEY,
                SUPER_QUEEN_HONOR_EVENT, SUPER_QUEEN_HONOR_EVENT, SUPER_QUEEN_HONOR_EVENT,
                "", "");

    }

    // 初始化1-3月获得称号用户
    private void initHonorRecord() {
        int pos = 1;
        for (String activityId : QUEEN_ACTIVITY_ID_LIST) {
            Map<Integer, List<String>> typeMapList = new HashMap<>();
            for (Map.Entry<Integer, Integer> targetEntry : TYPE_REWARD_TARGET_MAP.entrySet()) {
                int targetKey = targetEntry.getKey();
                int target = targetEntry.getValue();
                // if (pos == 1 && target == TYPE_REWARD_TARGET_MAP.get(CHARM_TYPE)) {
                //     // 第一次举办的魅力女王目标积分为当前配置*10
                //     target = target * 10;
                // }
                String key = getZetTypeKey(activityId, targetKey);
                Map<String, Integer> rankingMap = activityCommonRedis.getOtherRankingMapByScoreAPage(key,
                        target, target * 100, 0, 5000);
                List<String> aidList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(rankingMap)) {
                    for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                        String aid = entry.getKey();
                        aidList.add(aid);
                    }
                }
                typeMapList.put(targetKey, aidList);
                PageUtils.PageData<String> pageData = PageUtils.getPageData(aidList, 1, 10);
                logger.info("initHonorRecord-->pos:{} activityId:{} targetKey:{} target:{} aidSize:{} subAidList:{}",
                        pos, activityId, targetKey, target, aidList.size(), pageData.list);
            }

            // 当月获得超级女王的用户
            List<String> aidSuperList = new ArrayList<>();
            boolean firstIteration = true;
            for (Map.Entry<Integer, List<String>> getEntry : typeMapList.entrySet()) {
                List<String> currentList = getEntry.getValue();
                if (firstIteration) {
                    aidSuperList.addAll(currentList);
                    firstIteration = false;
                } else {
                    aidSuperList.retainAll(currentList);
                }
            }
            typeMapList.put(SUPER_TYPE, aidSuperList);
            PageUtils.PageData<String> superPageData = PageUtils.getPageData(aidSuperList, 1, 10);
            logger.info("initHonorRecord-->pos:{} activityId:{} targetKey:{} aidSuperSize:{} subSuperAidList:{}",
                    pos, activityId, SUPER_TYPE, aidSuperList.size(), superPageData.list);
            honorHistoryList.add(typeMapList);
            pos++;
        }

    }

    private void handleRes(String aid, String resKey, int type) {
        String eventTitle = getEventTitle(type, 3);
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
    }

    private void asyncHandleLevelRes(String aid, String resKey, int type, int noticeLevel, String level, boolean isNoticeGetQueen) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                String tnId = actorData.getTn_id();
                if (StringUtils.hasLength(tnId)) {
                    String bindAid = activityCommonRedis.getCommonHashStrValue(getHashBindKey(null), tnId);
                    if (StringUtils.isEmpty(bindAid) || aid.equals(bindAid)) {
                        String eventTitle = getEventTitle(type, 4) + level;
                        resourceKeyHandlerService.sendResourceData(aid, resKey,
                                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                        activityCommonRedis.setCommonHashData(getHashBindKey(null), tnId, aid);
                        if (noticeLevel > 0) {
                            int noticeType = noticeLevel == 5 ? noticeLevel : type * 10 + noticeLevel;
                            if (noticeType == 5 && !isNoticeGetQueen) {
                                return;
                            }
                            List<String> titleList = TYPE_LOCK_TITLE_NOTICE_MAP.get(noticeType);
                            List<String> bodyList = TYPE_LOCK_TITLE_NOTICE_MAP.get(noticeType);
                            if (titleList != null) {
                                sendOfficialMsg(titleList, bodyList, aid);
                            }
                        }
                        logger.info("handleLevelRes success aid:{} resKey:{} type:{} tnId:{}", aid, resKey, type, tnId);
                    } else {
                        logger.info("handleLevelRes fail aid:{} resKey:{} type:{} tnId:{} bindAid:{}", aid, resKey, type, tnId, bindAid);
                    }
                } else {
                    String eventTitle = getEventTitle(type, 4) + level;
                    resourceKeyHandlerService.sendResourceData(aid, resKey,
                            eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                    if (noticeLevel > 0) {
                        int noticeType = noticeLevel == 5 ? noticeLevel : type * 10 + noticeLevel;
                        if (noticeType == 5 && !isNoticeGetQueen) {
                            return;
                        }
                        List<String> titleList = TYPE_LOCK_TITLE_NOTICE_MAP.get(noticeType);
                        List<String> bodyList = TYPE_LOCK_TITLE_NOTICE_MAP.get(noticeType);
                        if (titleList != null) {
                            sendOfficialMsg(titleList, bodyList, aid);
                        }
                    }
                    logger.info("handleLevelRes success tnId is empty aid:{} resKey:{} type:{} tnId:{}", aid, resKey, type, tnId);
                }
            }
        });
    }

    public void leftPushAllHistoryList(String uid, int type, int change) {
        String key = getHistoryListKey(null, uid, type);
        SuperQueen2025VO.HistoryRedisData historyRedisData = new SuperQueen2025VO.HistoryRedisData();
        historyRedisData.setChange(change);
        historyRedisData.setCtime(DateHelper.getNowSeconds());
        List<String> strList = new ArrayList<>();
        strList.add(JSONObject.toJSONString(historyRedisData));
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(key, strList, HISTORY_MAX_SIZE);
    }


    public List<SuperQueen2025VO.HistoryRedisData> getHistoryListPageRecord(String activityId, String uid, int type, int page) {
        try {
            int start = (page - 1) * HISTORY_PAGE_SIZE;
            int end = page * HISTORY_PAGE_SIZE;
            String key = getHistoryListKey(activityId, uid, type);
            List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
            List<SuperQueen2025VO.HistoryRedisData> resultList = new ArrayList<>();
            for (String json : jsonList) {
                SuperQueen2025VO.HistoryRedisData rewardData = JSON.parseObject(json, SuperQueen2025VO.HistoryRedisData.class);
                resultList.add(rewardData);
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getHistoryListPageRecord error", e);
            return Collections.emptyList();
        }
    }

    public void pictureQueenHallPush(String activityId, String uid, PainterPictureDTO dto) {
        checkActivityTime(activityId);
        String picture = dto.getPicture();
//        int slang = dto.getSlang();
        if (StringUtils.isEmpty(picture)) {
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }

        String nowDay = getDayByBase(activityId, uid);
        int startTime = DateHelper.ARABIAN.stringDateToStampSecond(nowDay);
        int endTime = (int) (startTime + TimeUnit.DAYS.toSeconds(1));
        MomentActivityData momentActivityData = momentActivityDao.findMomentOneByTime(uid, MOMENT_QUEEN_HALL_ORIGIN, startTime, endTime);
        if (momentActivityData != null) {
            throw new CommonH5Exception(ActivityHttpCode.ALREADY_SHARE);
        }
        synchronized (stringPool.intern(getLocalPublishUserKey(uid))) {
            InnerPublishMomentDTO publishMomentDTO = new InnerPublishMomentDTO();
            publishMomentDTO.setUid(uid);
            publishMomentDTO.setText("تقدير الملكة الخارقة");
            publishMomentDTO.setShow(MomentConstant.MOMENT_PUBLIC);
            publishMomentDTO.setActiveId(activityId);
            publishMomentDTO.setLocation(MOMENT_QUEEN_HALL_ORIGIN);
            InnerPublishMomentDTO.MomentImageDTO imageDTO = new InnerPublishMomentDTO.MomentImageDTO();
            imageDTO.setUrl(picture);
            imageDTO.setWidth("3000");
            imageDTO.setHeight("2000");
            publishMomentDTO.setImgs(Collections.singletonList(imageDTO));

            InnerPublishMomentDTO.Quote quote = new InnerPublishMomentDTO.Quote();
            quote.setType(6);
            quote.setIcon("");
            quote.setContent("تقدير الملكة الخارقة");
            quote.setAction(HONOR_ACTIVITY_URL);

            publishMomentDTO.setQuote(quote);

            publishMomentDTO.setTopicRid(TOPIC_RID);

            HttpResult<String> result = iMomentService.publish(publishMomentDTO);
            if (result.getCode() == 20) {
                logger.info("motherMomentPush level limit. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.LEVEL_LIMIT);
            }

            if (result.isError()) {
                logger.error("motherMomentPush error. uid={} code={}", uid, result.getCode());
                throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
            }
            String mid = result.getData();
            logger.info("queenHallPicture publish moment success. uid={} mid={}", uid, mid);
        }

    }


    private void doJoinReportEvent(String uid, int applyType) {
        ActivityApplicationEvent event = new ActivityApplicationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScene(ACTIVITY_TITLE_EN);
        event.setApply_type(applyType);
        eventReport.track(new EventDTO(event));
    }

    private void doDoneQueenEvent(String uid, int type) {
        ActivityParticipationEvent event = new ActivityParticipationEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_stage(type);
        eventReport.track(new EventDTO(event));
    }

    private void doReportDetailEvent(String uid, int type, int changed, String changedDesc) {
        ScoreRecordEvent event = new ScoreRecordEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setScore_changed(changed);
        event.setScore_changed_sence(21);
        event.setScore_changed_detail(type);
        event.setScore_changed_desc(changed > 0 ? changedDesc : "daily_reward");
        eventReport.track(new EventDTO(event));
    }

    private String getDay(String uid) {
        return getDay(uid, true);
    }

    private String getDay(String uid, boolean baseIsToday) {
        if (ServerConfig.isNotProduct()) {
            return getTestDays(uid, baseIsToday);
        }
        return baseIsToday ? DateHelper.ARABIAN.formatDateInDay()
                : DateHelper.ARABIAN.getYesterdayStr(new Date());
    }

    private int getIndexLevel(int score, List<Integer> scoreLevelList) {
        List<Integer> tempLevelNumList = new ArrayList<>(scoreLevelList);
        int currentLevelIndex = 0;
        if (tempLevelNumList.contains(score)) {
            currentLevelIndex = tempLevelNumList.indexOf(score);
        } else {
            tempLevelNumList.add(score);
            tempLevelNumList.sort(Integer::compare);
            currentLevelIndex = tempLevelNumList.indexOf(score) - 1;
        }
        return currentLevelIndex;
    }

    private String getTestDays(String uid, boolean baseIsToday) {
        int addDays = activityCommonRedis.getCommonHashValue(getHashTestDayKey(null), uid);
//        logger.info("test add days:{}", addDays);
        addDays = baseIsToday ? addDays : addDays - 1;
        return todayMinusDays(addDays);
    }

    /**
     * @param days
     * @return
     */
    private String todayMinusDays(int days) {
        days = -days; // -1为明天  1为昨天
        LocalDate nowDate = DateSupport.ARABIAN.getToday();
        LocalDate oldDate = nowDate.minusDays(days);
        // 获取字符格式 yyyy-MM-dd
        return DateSupport.format(oldDate);
    }

    private int getPassionTodayMax(int startTime, int endTime) {
        if (ServerConfig.isNotProduct()) {
            return PASSION_DAY_MAX * 30;
        }
        String before = DateHelper.ARABIAN.formatDateInDay
                (new Date(startTime * 1000L));
        String after = DateHelper.ARABIAN.formatDateInDay
                (new Date(endTime * 1000L));
        int d = (int) (DateSupport.calculateDaysBetween(before, after) + 1);
        if (d <= 0) {
            return PASSION_DAY_MAX * 10;
        }
        int div = (TYPE_REWARD_TARGET_MAP.get(PASSION_TYPE) + PASSION_DAY_MAX - 1) / PASSION_DAY_MAX; // 向上取整
        if (d <= div) { // 小于等于4天没有减分
            return PASSION_DAY_MAX * d;
        } else {
            // 大于4天之后的有减分
            return PASSION_DAY_MAX * div + PASSION_DAY_MAX_2 * (d - div);
        }
    }

    private String getLocalQueenKey(String uid) {
        return "lock:superQueen:" + uid;
    }

    public String getSetJoinKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":queen:join";
    }

    private String getHashDetailKey(String activityId, String dayStr) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":queen:detail:" + dayStr;
    }

    private String getHashTotalKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":queen:total";
    }

    private String getZetTypeKey(String activityId, int type) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":queen:type:" + type;
    }


    private String getHistoryListKey(String activityId, String uid, int type) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return String.format("queen:history:%s:%s:%s", aId, uid, type);
    }

    /**
     * 设备绑定账号
     *
     * @return
     */
    private String getHashBindKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":device:bind";
    }

    private String getHashTestDayKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? ACTIVITY_ID : activityId;
        return aId + ":test:uid:day";
    }

    private String getSetHonorGetKey(String activityId) {
        String aId = StringUtils.isEmpty(activityId) ? HONOR_ACTIVITY_ID : activityId;
        return aId + ":queen:honor:get";
    }

    private String getEventTitle(int type, int sence) {
        return String.format("2025 Super Queen-%s queen %s reward",
                TYPE_EVENT_TITLE_KEY_MAP.getOrDefault(type, ""),
                TYPE_EVENT_SENCE_KEY_MAP.getOrDefault(sence, ""));
    }

    private String getLocalPublishUserKey(String uid) {
        return "lock:queen:publish:uid:" + uid;
    }
}
