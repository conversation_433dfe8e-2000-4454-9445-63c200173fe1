package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.api.MsgService;
import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.vo.SendMsgVO;
import com.quhong.enums.HttpCode;
import com.quhong.enums.MsgHttpCode;
import com.quhong.exception.CommonException;
import com.quhong.exception.CommonH5Exception;
import com.quhong.managers.SendChatMsgProcessor;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

@DubboService(timeout = 3000)
public class MsgServiceImpl implements MsgService {

    private static final Logger logger = LoggerFactory.getLogger(MsgServiceImpl.class);

    @Resource
    private SendChatMsgProcessor sendChatMsgProcessor;

    public SendMsgVO sendMsg(MsgSendDTO reqData) {
        logger.info("dubbo send msg... data={}", JSON.toJSONString(reqData));
        reqData.setInner(true);
        return sendChatMsgProcessor.doSendMsg(reqData);
    }

    @Override
    public String test(String param) throws CommonException {
        logger.info("test param={} remoteHost={}", param, RpcContext.getServiceContext().getRemoteHost());
        if ("error".equals(param)) {
            throw new CommonException(HttpCode.AUTH_ERROR);
        } else if ("h5error".equals(param)) {
            throw new CommonH5Exception(HttpCode.UPDATE_APP);
        } else if ("oerror".equals(param)) {
            throw new CommonException(MsgHttpCode.VIP_LEVEL_IS_NOT_HIGH_ENOUGH_TO_SEND);
        } else if (param.startsWith("timeout")) {
            try {
                Thread.sleep(Long.parseLong(param.replace("timeout", "")));
                return param + "-" + System.currentTimeMillis();
            } catch (InterruptedException e) {
                throw new CommonException(HttpCode.SERVER_ERROR);
            }
        }
        return param + "-" + System.currentTimeMillis();
    }

}
