package com.quhong.service;

import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.EventReport;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IFriendService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendApplyDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.mysql.dao.RoomBlacklistDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.ActivityCommonRedis;
import com.quhong.redis.FriendsListRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.redis.UserOnlineRedis;
import com.quhong.room.redis.RoomKickRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关系戒指
 */
@Service
public class RingOfRelationShipService extends OtherActivityService implements TaskMsgHandler {


    private static final Logger logger = LoggerFactory.getLogger(RingOfRelationShipService.class);
    private static final String ACTIVITY_TITLE_EN = "Ring of Relationship";
    private static final String ACTIVITY_TITLE_AR = "Ring of Relationship";
    public static String ACTIVITY_ID = "kkkk";

    private static String ACTIVITY_URL = String.format("https://static.youstar.live/ring_of_relationship/?activityId=%s", ACTIVITY_ID);
    private static final String ACTIVITY_BROADCAST_ICON = "";


    private static final int CP_REALATION = 1; // cp类型
    private static final int BROTHER_REALATION = 2; // 兄弟类型
    private static final int SISTER_REALATION = 3; // 姐妹类型
    private static final int BESTIE_REALATION = 4; // 死党类型

    private static final int HANDLE_SEND_TYPE = 1; // 发送类型
    private static final int HANDLE_RECEIVE_TYPE = 2; // 接收类型
    private static final int HANDLE_UNBIND_TYPE = 3; // 解绑类型

    // 用户个人相关业务数据
    private static final String SEND_GIFT_FILED = "send_gift_filed";
    private static final String CP_REALATION_FILED = "cp_realation_filed"; // 值为list
    private static final String BROTHER_REALATION_FILED = "brother_realation_filed";
    private static final String SISTER_REALATION_FILED = "sister_realation_filed";
    private static final String BESTIE_REALATION_FILED = "bestie_realation_filed";

    // 亲密度升级等级值
    private static final List<Integer> RELATION_LEVEL_LIST = Arrays.asList(0, 100, 200, 300, 400);
    private static final List<String> RELATION_KEY_LEVEL_LIST = Arrays.asList("", "FirstKingdomReward1", "FirstKingdomReward2", "FirstKingdomReward3", "FirstKingdomReward4", "FirstKingdomReward5");
    private static final List<String> RELATION_EVENT_LEVEL_LIST = Arrays.asList("", "FirstKingdomReward1", "FirstKingdomReward2", "FirstKingdomReward3", "FirstKingdomReward4", "FirstKingdomReward5");

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(CommonMqTaskConstant.SEND_PRIVATE_MSG);



    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private CacheDataService cacheDataService;
    @Resource
    private EventReport eventReport;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private RoomBlacklistDao roomBlacklistDao;
    @Resource
    private RoomKickRedis roomKickRedis;
    @Resource
    private IFriendService ifriendService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private ActivityCommonRedis activityCommonRedis;
    @Resource
    private DAUDao dauDao;
    @Resource
    private FriendApplyDao friendApplyDao;
    @Resource
    private IDetectService idetectService;
    //    @Resource(name = AsyncConfig.ASYNC_TASK)
//    private Executor executor;
    @Resource
    private BasePlayerRedis basePlayerRedis;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "aaaa";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/ring_of_relationship/?activityId=%s", ACTIVITY_ID);
        }
    }

    private String getLocalEventUserKey(String uid) {
        return "lock:fate_plaza:user:" + uid;
    }

    /**
     * 获取已发送亲密礼物的游湖数据(用于ZSet存储) member为aid, 值为0未申请1好友发私信（已申请）2非好友发好友请求（已申请）
     *
     * @param activityId
     * @return
     */
    private String getRelationshipKey(String activityId, String uid, int relationType) {
        return String.format("ring_of_relationship_relation:%s:%s:%s", activityId, uid, relationType);
    }


    /**
     * 获取申请数据(用于Hash存储) field为noteId 值为RingOfRelationShipVO.NoteInfoVO
     */
    private String getNoteKey(String activityId) {
        return String.format("ring_of_relationship_notes:%s", activityId);
    }

    /**
     * 获取申请或者接收的message数据(用于ZSet存储) member为noteId
     *
     * @param activityId
     * @param uid
     * @param handleType 1: 我发送的消息 2: 我接收的消息 3解绑功能区消息
     */
    private String getSquareNoteActivityId(String activityId, String uid, int handleType) {
        return String.format("ring_of_relationship_message:%s:%s:%s", activityId, uid, handleType);
    }


    /**
     * 用户个人的活动相关数据(用于hash存储) field为
     */
    private String getRelationDetailKey(String activityId, String uid) {
        return String.format("ring_of_relationship_user:%s:%s", activityId, uid);
    }


    /**
     * 已绑定亲密关系(用于hash存储) field为已绑定关系的index，值为值为RingOfRelationShipVO.RelationShipInfo
     */
    private String getRelationDetailKey(String activityId) {
        return String.format("ring_of_relationship_detail:%s", activityId);
    }


    /**
     * 已绑定亲密关系的每日发消息数据(用于hash存储) field为已绑定关系的index，值为DailyTaskInfo
     */
    private String getRelationDetailDayKey(String activityId, String day) {
        return String.format("ring_of_relationship_detail_day:%s:%s", activityId, day);
    }

    /**
     * 获取亲密度rank(用于ZSet存储) member为已绑定关系的index，值为亲密度
     */
    private String getRelationRankKey(String activityId) {
        return String.format("ring_of_relationship_rank:%s", activityId);
    }


    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();
        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            return;
        }
        syncAddHandle(uid, data);
    }


    private boolean checkAc(String uid, CommonMqTopicData mqData) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            boolean isWhiteTestAid = whiteTestDao.isMemberByType(mqData.getAid(), WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest && !isWhiteTestAid) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return false;
        }
        return true;
    }

    private void syncAddHandle(String uid, CommonMqTopicData mqData) {
        if (!checkAc(uid, mqData)) {
            return;
        }
        String dateStr = getDayByBase(ACTIVITY_ID, uid);
        String aid = mqData.getAid();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(aid)) {
            return;
        }
        if (CommonMqTaskConstant.ADD_FRIEND.equals(mqData.getItem())) {
            // 处理添加好友事件,mq发送端会发送两次，所以这里只处理一次
        } else if (CommonMqTaskConstant.SEND_PRIVATE_MSG.equals(mqData.getItem())) {
            synchronized (stringPool.intern(getLocalEventUserKey(uid))) {
                // 处理发送私信事件
            }
        }
    }


    /**
     * 处理资源发放（带埋点标题）
     */
    private void handleRes(String uid, String resKey, String eventTitle) {
        try {
            resourceKeyHandlerService.sendResourceData(uid, resKey, eventTitle, eventTitle, eventTitle, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
        } catch (Exception e) {
        }
    }

}
