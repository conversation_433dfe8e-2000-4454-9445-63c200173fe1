package com.quhong.service;

import com.quhong.constant.AccountConstant;
import com.quhong.data.ActorData;
import com.quhong.data.MsgPageData;
import com.quhong.data.MsgRecordRsp;
import com.quhong.data.dto.TempToAidDTO;
import com.quhong.data.vo.MsgListVO;
import com.quhong.data.vo.NewGreetMsgVO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.SLangType;
import com.quhong.enums.UserMsgStatus;
import com.quhong.handler.HttpEnvData;
import com.quhong.managers.ChatMsgListProcessor;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MsgListDao;
import com.quhong.mysql.data.MysqlMsgRecordData;
import com.quhong.redis.PlayerStatusRedis;
import com.quhong.redis.RoomMicRedis;
import com.quhong.redis.UserInterceptionRedis;
import com.quhong.redis.UserOnlineRedis;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class GreetMeetService {
    private static final Logger logger = LoggerFactory.getLogger(GreetMeetService.class);
    private static final int PAGE_SIZE = 10;

    @Resource
    private MsgListDao msgListDao;
    @Resource
    private RoomActorCache actorCache;
    @Resource
    private UserInterceptionRedis userInterceptionRedis;
    @Resource
    private PlayerStatusRedis playerStatusRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private RoomMicRedis roomMicRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private UserOnlineRedis userOnlineRedis;


    public NewGreetMsgVO getNewGreetList(ChatMsgListProcessor.ChatMsgListReq reqData) {
        String uid = reqData.getUid();
        NewGreetMsgVO vo = new  NewGreetMsgVO();
        int originPage = reqData.getPage();
        int page = Math.max(originPage - 1, 0);
        List<ChatMsgListProcessor.MsgItemRsp> retList = new ArrayList<>();
        MsgPageData msgPageData = new MsgPageData(page, PAGE_SIZE);
        List<MsgListVO> msgList = msgListDao.findMsgList(uid, msgPageData.getStart(), PAGE_SIZE, 1);
        for (MsgListVO sortData : msgList) {
            ChatMsgListProcessor.MsgItemRsp item = new ChatMsgListProcessor.MsgItemRsp();
            item.setUnread(sortData.getUnread());
            item.setSticky(sortData.getSticky());
            String aid;
            if (sortData.getA_id().equals(uid)) {
                aid = sortData.getB_id();
            } else {
                aid = sortData.getA_id();
            }
            item.setLast_time(sortData.getMtime());
            //设置最近一条消息
            MysqlMsgRecordData myLastMsg = sortData.getMy_last_msg();
            if (myLastMsg == null) {
                myLastMsg = sortData.getLast_msg();
            }
            if (myLastMsg != null) {
                MsgRecordRsp msgRecordRsp = new MsgRecordRsp();
                msgRecordRsp.fillFrom(myLastMsg);
                if (msgRecordRsp.getMsgType() == 22 && !AppVersionUtils.versionCheck(845, reqData)) {
                    // 针对旧版本收到进场通知礼物消息展示错误的问题
                    int resType = msgRecordRsp.getInfo() != null ? msgRecordRsp.getInfo().getIntValue("resType") : 0;
                    if (resType == 11) {
                        msgRecordRsp.setMsgType(1);
                        if (reqData.getSlang() == SLangType.ARABIC) {
                            msgRecordRsp.setMsg("أرسلت لك تأثير دخول");
                        }
                    }
                }
                item.setMsg(msgRecordRsp);
            } else {
                logger.info("can not find last msg. msgIndex={} uid={}", sortData.getMsg_index(), uid);
            }
            RoomActorDetailData detailData = actorCache.getData("", aid, false);
            // 过滤已删除的账号
            if (detailData.getAccountStatus() == AccountConstant.DELETED) {
                msgListDao.delMsgListData(uid, aid);
                continue;
            }
            item.fillFrom(detailData);
            // 设置是否拦截状态
            item.setInterceptionStatus(userInterceptionRedis.getUserInterceptionStatus(uid, aid));
            ActorData toActor = actorDao.getActorData(aid);
            // 设置用户状态
            item.setUserStatus(getUserStatus(toActor, item));
            item.setNewFlag(ActorUtils.isNewRegisterActor(aid, 30) ? 1 : 0);
            retList.add(item);
        }
        vo.setList(retList);
        vo.setNextUrl(msgList.size() < PAGE_SIZE ? "" : String.valueOf(originPage + 1));
        if (page == 0) {
            vo.setUnreadNum(msgListDao.getUnreadCount(uid, 1));
        }
        return vo;
    }

    private int getUserStatus(ActorData actorData, ChatMsgListProcessor.MsgItemRsp item) {
        String uid = actorData.getUid();
        int userStatus = UserMsgStatus.USER_STATUS_OFFLINE;
        if (actorData.getAccept_talk() <= 0){
            return userStatus;
        }
        int status = userOnlineRedis.isOnline(uid);
        if (status <= 0) {
            return userStatus;
        }
        // 设置在线状态
        userStatus = UserMsgStatus.USER_STATUS_ONLINE;
        String toActorRoomId = roomPlayerRedis.getActorRoomStatus(uid);
        if (!StringUtils.isEmpty(toActorRoomId)) {
            // 设置在麦状态
            Set<String> allMics = roomMicRedis.getRoomMicSetRedis(toActorRoomId);
            if (allMics.contains(uid)) {
                item.setActionValue(toActorRoomId);
                userStatus = UserMsgStatus.USER_STATUS_IN_MIC;
            }
        }
        return userStatus;
    }

    /**
     * 清理打招呼未读
     */
    public void cleanNewGreetUnread(HttpEnvData envData) {
        String uid = envData.getUid();
        logger.info("clean unread. uid={}", uid);
        msgListDao.cleanUnread(uid, 1);
    }

    /**
     * 切换正常聊天状态
     */
    public void updateNormalChat(TempToAidDTO dto) {
        String uid = dto.getUid();
        msgListDao.updateNormalChat(uid, dto.getAid());
    }

}
