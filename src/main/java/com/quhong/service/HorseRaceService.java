package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.*;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.HorseRaceVO;
import com.quhong.data.vo.OtherRankingListVO;
import com.quhong.data.vo.PrizeConfigVO;
import com.quhong.datas.DayTimeData;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.msg.obj.LuckyGiftRewardObject;
import com.quhong.msg.room.RoomLuckGiftRewardMsg;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class HorseRaceService extends OtherActivityService implements DailyTaskHandler {


    private static final Logger logger = LoggerFactory.getLogger(HorseRaceService.class);
    private static  String ACTIVITY_TITLE_EN = "Horse Racing Challenge";
    private static  String ACTIVITY_TITLE_EN_PAY = "Horse Racing Challenge-reward1";
    private static  String ACTIVITY_TITLE_EN_NO_PAY = "Horse Racing Challenge-reward2";
    private static  String ACTIVITY_TITLE_EN_SPECIAL = "Horse Racing Challenge-reward3";
    private static  String ACTIVITY_TITLE_EN_DAILY_REWARD = "Horse Racing Challenge-dailyreward";
    private static final String ACTIVITY_TITLE_AR = "تحدي سباق الخيل";
    private static final String ACTIVITY_DESC = "Horse Racing Challenge Reward";
    public static  String ACTIVITY_ID = "333";
    private static  String ACTIVITY_URL = String.format("https://static.youstar.live/horse_jump_challenge2025/?activityId=%s", ACTIVITY_ID) ;
    private static final String ACTIVITY_BROADCAST_ICON = "https://cdn3.qmovies.tv/youstar/op_1727086329_icon.png";
    private static final Integer RECORD_PAGE_SIZE = 10;
    private static final List<Integer> DRAW_NUM_LIST = Arrays.asList(1, 10, 50);
    private static final List<String> DRAW_USER_LIST = Arrays.asList("627faa534f497060a683d088", "620bd31e464de81263b9c0ef", "616f2433541b4e39f7dabca4",
            "62a45b81c8dad4488d482810", "5c88a0f166dc630038467c4e", "5ab6ac5c1bad4814cbd56daa", "5e3521cfb271b6040a95e13a", "62a3fb110460bd3470eb8c76", "6606588cbe90384a14cd5279",
            "5c0ffd0c66dc6300781eaccc", "63e21b325ed641547f75961f", "5cc819f966dc630025bf64fa", "5da1afc32cd28589838f48ca", "65b71f30ea494915d29c40b9");
    private static final String CARD_GOLD = "gold";
    private static final String CARD_SILVER = "silver";
    private static final String CARD_COPPER = "copper";
    private static final String CARD_COMMON = "common";
    private static final String CARD_FAILED = "failed";
    private static final List<String> CARD_TYPE_LIST = Arrays.asList(CARD_GOLD, CARD_SILVER, CARD_COPPER, CARD_COMMON);
    private static  List<String> DAILY_DATE_LIST = Arrays.asList("2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-09-28", "2024-09-29", "2024-09-30");

    private static final int LIMIT_INIT_POOL = 30;

    private static final String CARD_PAGE_CONFIG_KEY = "horseRaceOpenPageDraw";   // 打开页面抽奖
    private static final String CARD_GIFT_CONFIG_KEY = "horseRaceSendGiftDraw";   // 发送礼物抽奖
    private static final String POOL_SIZE_PAY_KEY = "horseRaceDrawPay";   // 付费用户key
    private static final String POOL_SIZE_NO_PAY_KEY = "horseRaceDrawNoPay";   // 非付费用户key
    private static final String POOL_SIZE_SPECIAL_KEY = "horseRaceDrawSpecial";   // 特殊用户key
    private static final Interner<String> stringPool = Interners.newWeakInterner();
    private static final Map<String, Integer> CARD_TYPE_MAP = new HashMap<>();
    static {
        CARD_TYPE_MAP.put(CARD_GOLD, 1);
        CARD_TYPE_MAP.put(CARD_SILVER, 2);
        CARD_TYPE_MAP.put(CARD_COPPER, 3);
        CARD_TYPE_MAP.put(CARD_COMMON, 4);
    }

    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private ResourceKeyConfigDao resourceKeyConfigDao;
    @Resource
    private MarsMsgActivityService marsMsgActivityService;
    @Resource
    private EventReport eventReport;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;


    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "68d4e427fcc18c5b5b92b28b";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/horse_jump_challenge2025/?activityId=%s", ACTIVITY_ID);
        }

        OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
        if (activityData == null) {
            logger.error("init activityData is null ACTIVITY_ID:{}", ACTIVITY_ID);
            return;
        }

        if (activityData.getAcNameEn().startsWith("test")) {
            // 灰度测试 改为test开头不触发奖励广播
            ACTIVITY_TITLE_EN = "test Horse Racing Challenge";
            ACTIVITY_TITLE_EN_PAY = "test Horse Racing Challenge-reward1";
            ACTIVITY_TITLE_EN_NO_PAY = "test Horse Racing Challenge-reward2";
            ACTIVITY_TITLE_EN_SPECIAL = "test Horse Racing Challenge-reward3";
            ACTIVITY_TITLE_EN_DAILY_REWARD = "test Horse Racing Challenge-dailyreward";
        }

    }

    // 抽奖相关的每日key
    private String getDailyDate(String activityId){
        return String.format("dailyDate:%s", activityId);
    }

    private String getHashActivityId(String activityId, String uid){
        return String.format("horseRace:%s:%s", activityId, uid);
    }

    // 滚动记录
    private String getScrollKey(String activityId){
        return String.format("scroll:%s", activityId);
    }

    // 抽卡片Key
    private String getListDrawCardKey(String activityId, String origin){
        return String.format("drawCard:%s:%s", activityId, origin);
    }

    // 抽奖品Key
    private String getListDrawPrizeKey(String activityId, String resKey){
        return String.format("drawPrize:%s:%s", activityId, resKey);
    }

    // 每日抽奖排行榜
    // 历史记录key
    private String getDailyDrawRankKey(String activityId, String dateStr){
        return String.format("dailyDrawRank:%s:%s", activityId, dateStr);
    }

    // 历史记录key
    private String getHistoryRecordListKey(String activityId, String uid){
        return String.format("historyRecord:%s:%s", activityId, uid);
    }

    // 设备限制
    private String getDeviceLimitKey(String activityId, String dateStr){
        return String.format("deviceLimit:%s:%s", activityId, dateStr);
    }


    public HorseRaceVO horseRaceConfig(String activityId, String uid) {

        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivity(activityId);
        HorseRaceVO vo = new HorseRaceVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());
        String  hashActivityId = getHashActivityId(activityId, uid);
        Map<String, Integer> userDataMap = activityCommonRedis.getCommonHashAll(hashActivityId);

        // 每日登录抽卡片
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));

        int currentDayDraw = userDataMap.getOrDefault(currentDay, 0);
        ActorData actorData = actorDao.getActorDataFromCache(uid);

        if(currentDayDraw <= 0 && inActivityTime(activityId)){
            String tnId = actorData.getTn_id();
            boolean flag = !StringUtils.isEmpty(tnId);
            String deviceLimitKey = getDeviceLimitKey(activityId, currentDay);
            if (activityCommonRedis.getCommonZSetRankingScore(deviceLimitKey, tnId) >= 1){
                flag = false;
            }
            if (flag){
                String cardKey = this.drawCard(activityId, uid, CARD_PAGE_CONFIG_KEY);
                this.doCardReportEvent(uid, cardKey, "1", 1);
                if(!CARD_FAILED.equals(cardKey)){
                    int cardNum = userDataMap.getOrDefault(cardKey, 0);
                    userDataMap.put(cardKey, cardNum+1);
                    this.getOpenDrawCard(vo, hashActivityId, cardKey, currentDay);
                }
                activityCommonRedis.incrCommonZSetRankingScoreSimple(deviceLimitKey, tnId, 1);
            }
        }

        vo.setGold(userDataMap.getOrDefault(CARD_GOLD, 0));
        vo.setSilver(userDataMap.getOrDefault(CARD_SILVER, 0));
        vo.setCopper(userDataMap.getOrDefault(CARD_COPPER, 0));
        vo.setCommon(userDataMap.getOrDefault(CARD_COMMON, 0));

        // 每日榜单
        List<HorseRaceVO.DailyRankConfig> dailyRankConfigList = new ArrayList<>();

        // int endTime = Math.min(activity.getEndTime() - 10, DateHelper.getNowSeconds()); // 偏移10秒为活动的最后一天
        int endTime =activity.getEndTime() - 10;
        String acStart = DateHelper.ARABIAN.formatDateInDay(new Date(activity.getStartTime() * 1000L));
        String acEnd = DateHelper.ARABIAN.formatDateInDay(new Date(endTime * 1000L));
        List<DayTimeData> dayTimeDataList = DateHelper.ARABIAN.getContinuesDays(acStart, acEnd);
        for (DayTimeData item : dayTimeDataList) {
            String dateStr = item.getDate();
            HorseRaceVO.DailyRankConfig dailyRankConfig = new HorseRaceVO.DailyRankConfig();
            String dateRankKey = getDailyDrawRankKey(activityId, dateStr);
            List<OtherRankingListVO> rankingList = new ArrayList<>();
            OtherRankingListVO myRank = new OtherRankingListVO();
            makeOtherRankingData(rankingList, myRank, dateRankKey, uid, 10,true);

            dailyRankConfig.setDateStr(dateStr);
            dailyRankConfig.setRankingListVOList(rankingList);
            dailyRankConfig.setMyRankVO(myRank);
            dailyRankConfigList.add(dailyRankConfig);
        }
        vo.setDailyRankConfigList(dailyRankConfigList);

        // 抽奖滚屏记录
        List<PrizeConfigVO> drawRecordList = new ArrayList<>();
        List<String> scrollList = activityCommonRedis.getCommonListRecord(getScrollKey(activityId));
        for (String item : scrollList) {
            PrizeConfigVO drawRecord = JSON.parseObject(item, PrizeConfigVO.class);
            ActorData actor = actorDao.getActorDataFromCache(drawRecord.getUid());
            drawRecord.setUserName(actor.getName());
            drawRecordList.add(drawRecord);
        }
        vo.setDrawRecordList(drawRecordList);
        return vo;
    }

    private void getOpenDrawCard(HorseRaceVO vo, String hashActivityId, String cardKey, String currentDay){
        ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(CARD_PAGE_CONFIG_KEY);
        if(resourceKeyConfigData != null) {
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));;
            ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(cardKey);
            PrizeConfigVO drawCardVO = new PrizeConfigVO();
            drawCardVO.setIconEn(resourceMeta.getResourceIcon());
            drawCardVO.setNameEn(resourceMeta.getResourceNameEn());
            drawCardVO.setNameAr(resourceMeta.getResourceNameAr());
            vo.setDrawCardVO(drawCardVO);
            activityCommonRedis.setCommonHashNum(hashActivityId, currentDay, 1);
        }
    }


    // 初始化奖池
    public void initDrawPool(String origin, String poolDrawKey){
        int poolSize = activityCommonRedis.getCommonListSize(poolDrawKey);
        if(poolSize <= LIMIT_INIT_POOL){
            List<String> poolList = new ArrayList<>();
            String resourceKey = CARD_PAGE_CONFIG_KEY.equals(origin) ? CARD_PAGE_CONFIG_KEY : CARD_GIFT_CONFIG_KEY;
            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(resourceKey);
            if(resourceKeyConfigData != null) {
                List<ResourceKeyConfigData.ResourceMeta> resourceMetaList = resourceKeyConfigData.getResourceMetaList();
                for (ResourceKeyConfigData.ResourceMeta resourceMeta: resourceMetaList) {
                    int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                    poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
                }
                Collections.shuffle(poolList);
                activityCommonRedis.rightPushAllCommonList(poolDrawKey, poolList);
            }
        }
    }


    public String drawCard(String activityId, String uid, String origin){
        synchronized (stringPool.intern(uid)) {
            String poolDrawKey = getListDrawCardKey(activityId, origin);
            this.initDrawPool(origin, poolDrawKey);
            String cardKey = activityCommonRedis.leftPopCommonListKey(poolDrawKey);
            if (StringUtils.isEmpty(cardKey)) {
                throw new CommonH5Exception(HttpCode.SERVER_ERROR);
            }
            if (!CARD_FAILED.equals(cardKey)){
                activityCommonRedis.incCommonHashNum(getHashActivityId(activityId, uid), cardKey, 1);
            }
            return cardKey;
        }
    }


    // 发送礼物抽奖
    public void sendGiftHandle(SendGiftData giftData, String activityId){
        int totalNum = giftData.getNumber() * giftData.getAid_list().size();
        String fromUid = giftData.getFrom_uid();
        String roomId = giftData.getRoomId();
        Map<String, Integer> rewardMap = new HashMap<>();
        for (int i=0; i < totalNum; i++){
            String cardKey = this.drawCard(activityId, fromUid, CARD_GIFT_CONFIG_KEY);
            this.doCardReportEvent(fromUid, cardKey, "2", 1);

            rewardMap.compute(cardKey, (k, v) -> {
                if (null == v) {
                    v = 1;
                }else {
                    v += 1;
                }
                return v;
            });
        }

        if (!rewardMap.isEmpty()){
            RoomLuckGiftRewardMsg rewardMsg = new RoomLuckGiftRewardMsg();
            List<LuckyGiftRewardObject> luckyGiftRewardList = new ArrayList<>();

            ResourceKeyConfigData resourceKeyConfigData = resourceKeyConfigDao.findByKey(CARD_GIFT_CONFIG_KEY);
            if(resourceKeyConfigData == null) {
                return;
            }
            Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));;
            for (String key : rewardMap.keySet()) {
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(key);
                LuckyGiftRewardObject rewardObject = new LuckyGiftRewardObject();
                rewardObject.setIcon(resourceMeta.getResourceIcon());
                rewardObject.setName("");
                rewardObject.setType(2);
                rewardObject.setValue(rewardMap.get(key));
                luckyGiftRewardList.add(rewardObject);
            }

            rewardMsg.setAid(fromUid);
            rewardMsg.setA_type(0);
            rewardMsg.setType(2);
            rewardMsg.setLucky_gift_reward(luckyGiftRewardList);
            marsMsgActivityService.asyncSendPlayerMsg(roomId, fromUid, fromUid, rewardMsg, false);
        }
    }


    /**
     * 使用卡片抽奖
     */
    private void initDrawPrizePool(Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String drawPrizeKey){
        int poolSize = activityCommonRedis.getCommonListSize(drawPrizeKey);
        if(poolSize <= LIMIT_INIT_POOL){
            List<String> poolList = new ArrayList<>();
            for(String prizeKey: resourceMetaMap.keySet()){
                ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(prizeKey);
                int rateNumber = Integer.parseInt(resourceMeta.getRateNumber());
                poolList.addAll(Stream.generate(resourceMeta::getMetaId).limit(rateNumber).collect(Collectors.toList()));
            }
            Collections.shuffle(poolList);
            activityCommonRedis.rightPushAllCommonList(drawPrizeKey, poolList);
        }
    }

    // 抽奖
    private String shootDraw(String activityId, Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap, String resKey){
        String drawPrizeKey = getListDrawPrizeKey(activityId, resKey);
        this.initDrawPrizePool(resourceMetaMap, drawPrizeKey);
        String awardKey = activityCommonRedis.leftPopCommonListKey(drawPrizeKey);
        if (StringUtils.isEmpty(awardKey)) {
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        return awardKey;
    }

    // 获取抽奖配置
    private ResourceKeyConfigData getResourceKeyConfig(String uid){
        if(DRAW_USER_LIST.contains(uid)){
            return  resourceKeyConfigDao.findByKey(POOL_SIZE_SPECIAL_KEY);
        }
        int rechargeMoney = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
        if(rechargeMoney >= 5){
            return  resourceKeyConfigDao.findByKey(POOL_SIZE_PAY_KEY);
        }else {
            return  resourceKeyConfigDao.findByKey(POOL_SIZE_NO_PAY_KEY);
        }
    }

    private String getResTitleByKey(String resKey){
        if (POOL_SIZE_SPECIAL_KEY.equals(resKey)){
            return ACTIVITY_TITLE_EN_SPECIAL;
        }

        if (POOL_SIZE_PAY_KEY.equals(resKey)){
            return ACTIVITY_TITLE_EN_PAY;
        }
        return ACTIVITY_TITLE_EN_NO_PAY;
    }




    public HorseRaceVO horseRaceDraw(String activityId, String uid, int zone, String cardType, int amount) {
        checkActivityTime(activityId);
        if(!CARD_TYPE_LIST.contains(cardType)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if(!DRAW_NUM_LIST.contains(amount)){
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String currentDay = DateHelper.ARABIAN.formatDateInDay();
        // String currentDay = activityCommonRedis.getCommonStrValue(this.getDailyDate(activityId));

        synchronized (stringPool.intern(uid)) {
            String hashActivityId = getHashActivityId(activityId, uid);
            Map<String, Integer> userDataMap =  activityCommonRedis.getCommonHashAll(hashActivityId);
            int cardNum = userDataMap.getOrDefault(cardType, 0);
            if(cardNum <= 0 || cardNum < amount){
                throw new CommonH5Exception(ActivityHttpCode.INSUFFICIENT_NUMBER);
            }

            int afterNum = activityCommonRedis.incCommonHashNum(hashActivityId, cardType, -amount);
            userDataMap.put(cardType, afterNum);

            int currentTime = DateHelper.getNowSeconds();
            List<PrizeConfigVO> drawRecordList = new ArrayList<>();
            String rewardTitle = "";
            HorseRaceVO vo = new HorseRaceVO();
            vo.setGold(userDataMap.getOrDefault(CARD_GOLD, 0));
            vo.setSilver(userDataMap.getOrDefault(CARD_SILVER, 0));
            vo.setCopper(userDataMap.getOrDefault(CARD_COPPER, 0));
            vo.setCommon(userDataMap.getOrDefault(CARD_COMMON, 0));

            if (zone == 0) {   // 不在区域内没得抽奖
                logger.info("shootCardDraw not in zone activityId:{}, uid:{}, cardType:{}, amount:{}", activityId, uid, cardType, amount);
            }else{
                ResourceKeyConfigData resourceKeyConfigData = this.getResourceKeyConfig(uid);
                if(resourceKeyConfigData == null){
                    throw new CommonH5Exception(ActivityHttpCode.SERVER_ERROR);
                }
                String resKey = resourceKeyConfigData.getKey();
                rewardTitle = this.getResTitleByKey(resKey);
                Map<String, ResourceKeyConfigData.ResourceMeta> resourceMetaMap = resourceKeyConfigData.getResourceMetaList().stream().collect(Collectors.toMap(ResourceKeyConfigData.ResourceMeta::getMetaId, Function.identity()));
                for (int i = 0; i < amount; i++) {
                    String awardKey = this.shootDraw(activityId, resourceMetaMap, resKey);
                    ResourceKeyConfigData.ResourceMeta resourceMeta = resourceMetaMap.get(awardKey);
                    if(resourceMeta == null){
                        continue;
                    }
                    resourceKeyHandlerService.sendOneResourceData(actorData, resourceMeta, 905, rewardTitle, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON, 0);
                    PrizeConfigVO drawRecord = new PrizeConfigVO();
                    drawRecord.setUid(uid);
                    drawRecord.setDrawType(awardKey);
                    drawRecord.setRewardType(String.valueOf(resourceMeta.getResourceType()));
                    drawRecord.setNameEn(resourceMeta.getResourceNameEn());
                    drawRecord.setNameAr(resourceMeta.getResourceNameAr());
                    drawRecord.setIconEn(resourceMeta.getResourceIcon());
                    drawRecord.setRewardTime(resourceMeta.getResourceTime());
                    drawRecord.setRewardNum(resourceMeta.getResourceNumber());
                    drawRecord.setRewardPrice(resourceMeta.getResourcePrice());
                    drawRecord.setCtime(currentTime);
                    drawRecordList.add(drawRecord);

                    String jsonRecord = JSON.toJSONString(drawRecord);
                    activityCommonRedis.addCommonListData(getScrollKey(activityId), jsonRecord);
                    activityCommonRedis.addCommonListRecord(getHistoryRecordListKey(activityId, uid), jsonRecord);
                }
            }
            this.doDrawReportEvent(uid, cardType, amount, rewardTitle, drawRecordList);
            activityCommonRedis.incrCommonZSetRankingScore(getDailyDrawRankKey(activityId, currentDay), uid, amount);
            vo.setDrawRecordList(drawRecordList);
            return vo;
        }
    }

    public HorseRaceVO horseRaceDrawRecord(String activityId, String uid, int page) {
        int start = (page - 1) * RECORD_PAGE_SIZE;
        int end = page * RECORD_PAGE_SIZE;
        String recordListKey = getHistoryRecordListKey(activityId, uid);
        List<String> prizeKeyTimeList = activityCommonRedis.getCommonListPageRecord(recordListKey, start, end);
        HorseRaceVO drawRecordVO = new  HorseRaceVO();
        List<PrizeConfigVO> drawRecordList = new ArrayList<>();
        for (String item : prizeKeyTimeList) {
            PrizeConfigVO drawRecord = JSON.parseObject(item, PrizeConfigVO.class);
            drawRecordList.add(drawRecord);
        }
        drawRecordVO.setDrawRecordList(drawRecordList);
        if(drawRecordList.size() < RECORD_PAGE_SIZE){
            drawRecordVO.setNextUrl(-1);
        }else {
            drawRecordVO.setNextUrl(page + 1);
        }
        return drawRecordVO;
    }

    private void doDrawReportEvent(String uid, String cardType, int amount, String rewardTitle, List<PrizeConfigVO> drawRecordList) {
        Map<String, Integer> drawRecordMap = drawRecordList.stream().collect(Collectors.groupingBy(PrizeConfigVO::getNameEn, Collectors.summingInt(PrizeConfigVO::getRewardNum)));
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ACTIVITY_TITLE_EN);
        event.setTicket_type(CARD_TYPE_MAP.getOrDefault(cardType, 0));
        event.setCost_ticket(amount);
        event.setDraw_nums(amount);
        event.setDraw_success_nums(drawRecordList.size());
        event.setDraw_detail(rewardTitle);
        event.setDraw_result(JSON.toJSONString(drawRecordMap));
        eventReport.track(new EventDTO(event));
    }


    private void doCardReportEvent(String uid, String cardType, String cardOrigin, int num) {
        GetActivityTicketsEvent event = new GetActivityTicketsEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(ACTIVITY_ID);
        event.setActivity_tickets_resource(cardOrigin);
        event.setActivity_tickets_type(CARD_TYPE_MAP.getOrDefault(cardType, 0));
        event.setGet_activity_tickets(num);
        eventReport.track(new EventDTO(event));
    }


    @Override
    public void dailyTaskRun(String dateStr) {
        try{
            OtherRankingActivityData activityData = otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID);
            if(activityData == null){
                return;
            }
            int endTime = activityData.getEndTime();
            int currentTime = DateHelper.getNowSeconds();
            if (currentTime - 3600 > endTime) {
                return;
            }
            if(dateStr == null){
                dateStr = DateHelper.ARABIAN.getYesterdayStr(new Date());
            }
            logger.info("dailyTaskRun Horse Racing Challenge");
            Map<String, Integer> rankingMap = activityCommonRedis.getCommonRankingMap(getDailyDrawRankKey(ACTIVITY_ID, dateStr), 50);
            int rank = 1;
            for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
                String hostUid = entry.getKey();
                logger.info("distribute Horse Racing Challenge rank:{} hostUid: {}", rank, hostUid);
                if(rank <= 10){
                    String resourceKey = String.format("horseRaceDailyTop%s", rank);
                    resourceKeyHandlerService.sendResourceData(hostUid, resourceKey, ACTIVITY_TITLE_EN_DAILY_REWARD, ACTIVITY_TITLE_AR, ACTIVITY_DESC, ACTIVITY_URL, ACTIVITY_BROADCAST_ICON);
                }
                ActivityDailyRankingEvent event = new ActivityDailyRankingEvent();
                event.setActive_id(ACTIVITY_ID);
                event.setDate(dateStr);
                event.setUid(hostUid);
                event.setActivity_name(ACTIVITY_TITLE_EN);
                event.setRank(rank);
                event.setRank_value(entry.getValue());
                event.setCtime(DateHelper.getNowSeconds());
                eventReport.track(new EventDTO(event));
                rank += 1;
            }
        }catch (Exception e){
            logger.error("distribution Horse Racing Challenge error: {}", e.getMessage(), e);
        }
    }
}
