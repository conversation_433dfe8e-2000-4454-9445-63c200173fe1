package com.quhong.utils;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Security;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class AppleClientSecretGenerator {
    private static final Logger logger = LoggerFactory.getLogger(AppleClientSecretGenerator.class);

    /**
     * 六个月有效期
     */
    public static final String APPLE_CLIENT_SECRET = genAppleClientSecret();

    public static String genAppleClientSecret() {
        Security.addProvider(new BouncyCastleProvider());
        String teamId = "N8SQASNPTJ";
        String clientId = "com.stonemobile.youstar";
        String keyId = "369A67T8Z6";
        int validityPeriod = 180; // In days
        Map<String, Object> claims = new HashMap<>();
        claims.put("iss", teamId);
        claims.put("iat", new Date().getTime() / 1000L);
        claims.put("exp", new Date().getTime() / 1000L + 86400 * validityPeriod);
        claims.put("aud", "https://appleid.apple.com");
        claims.put("sub", clientId);
        String token;
        try {
            token = Jwts.builder()
                    .setClaims(claims)
                    .signWith(SignatureAlgorithm.ES256, getPrivateKeyFromPem())
                    .setHeaderParam("kid", keyId)
                    .compact();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        logger.info("gen apple client secret token={}", token);
        return token;
    }

    /**
     * 获取私匙
     * doc/AuthKey_369A67T8Z6.p8
     */
    public static PrivateKey getPrivateKeyFromPem() throws Exception {
        // 每行结尾需要\r
        String p8 = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgtRS6StKy3kBswIUz\r" +
                "VoJsOR3kSQpOsd/x45OVJcWB1oWgCgYIKoZIzj0DAQehRANCAAScjI1ECKd+HZfv\r" +
                "mxkP2HPDZHgtfPMNMII4zcJUU5GzNlgcMrpJu3Npn3EPLBTOdunnouKU8OQmb5Oe\r" +
                "URrEUbx8\r";
        byte[] b = Base64.getMimeDecoder().decode(p8);
        KeyFactory kf = KeyFactory.getInstance("ECDH", "BC");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(b);
        return kf.generatePrivate(keySpec);
    }

    public static void main(String[] args) {
        System.out.println(genAppleClientSecret());
    }
}
