package com.quhong.utils;


import org.bouncycastle.util.encoders.Base64;


import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;

/*
 AES加密
 */
public class AesUtilWithBase64 {

    private static final String EncryptAlg = "AES";

    private static final String Cipher_Mode="AES/ECB/PKCS7Padding";

    private static final String Encode="UTF-8";

    public static void main(String[] args) {
        generateKey("1259338372");
    }

    public static byte[] generateKey() {
        byte[] base64Bytes = Base64.encode("1259338372".getBytes());
        byte[] key = new byte[24];
        return base64Bytes;
    }

    public static byte[] generateKey(String appId) {
        byte[] base64Bytes = Base64.encode(appId.getBytes());
        int length = base64Bytes.length;
        byte[] key = null;
        int actualSize = 0;
        if (length >= 32) {
            actualSize = 32;
        } else if (length >= 24) {
            actualSize = 24;
        } else if (length >= 16) {
            actualSize = 16;
        } else {
            actualSize = 16;
        }

        key = new byte[actualSize];
        System.arraycopy(base64Bytes, 0, key, 0, actualSize);
        return key;
    }

    public static String encodeBody(String content, String appId) throws Exception {
        String result = "";
        try {
//            byte[] raw = PASSWORD.getBytes(Encode);
            SecretKeySpec skeySpec = new SecretKeySpec(generateKey(appId), EncryptAlg);
            Cipher cipher = Cipher.getInstance(EncryptAlg);//"算法/模式/补码方式"
            cipher.init(Cipher.ENCRYPT_MODE, skeySpec);

            byte[] data = cipher.doFinal(content.getBytes(Encode));
            result= Base64.toBase64String(data);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("AES加密失败："+e.getMessage(),e);
        }
        return result;
    }

    public static String byteToHex(byte[] bytes){
        String strHex = "";
        StringBuilder sb = new StringBuilder("");
        for (int n = 0; n < bytes.length; n++) {
            strHex = Integer.toHexString(bytes[n] & 0xFF);
            sb.append((strHex.length() <2 ) ? "0" + strHex : strHex); // 每个字节由两个字符表示，位数不够，高位补0
        }
        return sb.toString();
    }

    //加密
    public static String encode(String content) throws Exception {
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            SecretKeySpec sKeySpec = new SecretKeySpec(generateKey(),EncryptAlg);
            Cipher cipher = Cipher.getInstance(EncryptAlg);//"算法/模式/补码方式"
            cipher.init(Cipher.ENCRYPT_MODE,sKeySpec);

            byte[] data = cipher.doFinal(content.getBytes(Encode));
            String result = Base64.toBase64String(data);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("AES加密失败" + e.getMessage(),e);
        }
    }

    /**
     *
     * 兼容java后台Base64加密,适配低版本安卓系统
     *
     *
     */
    static class AndroidBase64 {
        private static final char last2byte = (char) Integer
                .parseInt("00000011", 2);
        private static final char last4byte = (char) Integer
                .parseInt("00001111", 2);
        private static final char last6byte = (char) Integer
                .parseInt("00111111", 2);
        private static final char lead6byte = (char) Integer
                .parseInt("11111100", 2);
        private static final char lead4byte = (char) Integer
                .parseInt("11110000", 2);
        private static final char lead2byte = (char) Integer
                .parseInt("11000000", 2);
        private static final char[] encodeTable = new char[]{'A', 'B', 'C', 'D',
                'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q',
                'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd',
                'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q',
                'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3',
                '4', '5', '6', '7', '8', '9', '+', '/'};

        /**
         * base64加密
         *
         * @param from
         * @return
         */
        public static String getAndroidEncode(byte[] from) {
            StringBuffer to = new StringBuffer((int) (from.length * 1.34) + 3);
            int num = 0;
            char currentByte = 0;
            for (int i = 0; i < from.length; i++) {
                num = num % 8;
                while (num < 8) {
                    switch (num) {
                        case 0:
                            currentByte = (char) (from[i] & lead6byte);
                            currentByte = (char) (currentByte >>> 2);
                            break;
                        case 2:
                            currentByte = (char) (from[i] & last6byte);
                            break;
                        case 4:
                            currentByte = (char) (from[i] & last4byte);
                            currentByte = (char) (currentByte << 2);
                            if ((i + 1) < from.length) {
                                currentByte |= (from[i + 1] & lead2byte) >>> 6;
                            }
                            break;
                        case 6:
                            currentByte = (char) (from[i] & last2byte);
                            currentByte = (char) (currentByte << 4);
                            if ((i + 1) < from.length) {
                                currentByte |= (from[i + 1] & lead4byte) >>> 4;
                            }
                            break;
                    }
                    to.append(encodeTable[currentByte]);
                    num += 6;
                }
            }
            if (to.length() % 4 != 0) {
                for (int i = 4 - to.length() % 4; i > 0; i--) {
                    to.append("=");
                }
            }
            return to.toString();
        }
    }

    private static String sha1Encode(byte[] bstr){
        String s = System.getProperty("line.separator");
        return AndroidBase64.getAndroidEncode(bstr).replaceAll(s,"");
    }

    public static String hmacSHA1(String key, String text) throws InvalidKeyException, NoSuchAlgorithmException
    {
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(new SecretKeySpec(key.getBytes(), "HmacSHA1"));
        return sha1Encode(mac.doFinal(text.getBytes()));
    }
}
