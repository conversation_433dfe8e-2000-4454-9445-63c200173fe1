package com.quhong.utils;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Map;

public class HttpUtils {

    private static final Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    private final static String CHARSET = "UTF-8";

    private static String URL = "https://rce.tencentcloudapi.com/";

    public static String sign(String s, String key, String method) throws Exception {
        Mac mac = Mac.getInstance(method);
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(CHARSET), mac.getAlgorithm());
        mac.init(secretKeySpec);
        byte[] hash = mac.doFinal(s.getBytes(CHARSET));
        return DatatypeConverter.printBase64Binary(hash);
    }

    public static String getStringToSign(Map<String, String> params) {
        StringBuilder s2s = new StringBuilder("POSTrce.tencentcloudapi.com/?");
        // 签名时要求对参数进行字典排序，此处用TreeMap保证顺序
        for (String k : params.keySet()) {
            s2s.append(k).append("=").append(params.get(k)).append("&");
        }
        return s2s.substring(0, s2s.length() - 1);
    }

    public static String getUrl(Map<String, String> params) throws UnsupportedEncodingException {
        StringBuilder url = new StringBuilder(URL);
        // 实际请求的url中对参数顺序没有要求
        for (String k : params.keySet()) {
            // 需要对请求串进行urlencode，由于key都是英文字母，故此处仅对其value进行urlencode
            url.append(k).append("=").append(URLEncoder.encode(params.get(k), CHARSET)).append("&");
        }
        return url.substring(0, url.length() - 1);
    }

    public static String makeQueryString(Map<String,String> params,String encode) throws UnsupportedEncodingException {
        String url = "";
        for (Map.Entry<String, String> entry : params.entrySet()) {
            url += entry.getKey() + "=" + (encode == null ? entry.getValue() : URLEncoder.encode(entry.getValue(), encode)) + "&";
        }
        return url.substring(0, url.length()-1);
    }

    /**
     * 通过给定的请求参数和编码格式，以获取服务器返回的数据
     * params 请求参数
     * encode 编码格式
     * @return
     */
    public static String sendPost(Map<String,String> params, String encode) throws UnsupportedEncodingException {
        String buffer = makeQueryString(params,encode);
        logger.info("buffer={}", buffer);
        try {
            URL url = new URL(URL);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(3000);
            //设置允许输入输出
            urlConnection.setDoInput(true);
            urlConnection.setDoOutput(true);
            byte[] mydata = buffer.getBytes();
            //设置请求报文头，设定请求数据类型
            urlConnection.setRequestProperty("accept", "*/*");
            urlConnection.setRequestProperty("connection", "Keep-Alive");
            urlConnection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            urlConnection.setRequestProperty("Content-Type","application/x-www-form-urlencoded");
            //设置请求数据长度
            urlConnection.setRequestProperty("Content-Length",
                    String.valueOf(mydata.length));
            //设置POST方式请求数据
            urlConnection.setRequestMethod("POST");
            OutputStream outputStream = urlConnection.getOutputStream();
            outputStream.write(mydata);
            int responseCode = urlConnection.getResponseCode();
            if(responseCode == 200){
                return changeInputStream(urlConnection.getInputStream(),
                        encode);
            }
        } catch (IOException e) {
           e.printStackTrace();
        }
        return null;
    }


    /**
     * 把服务端返回的输入流转换成字符串格式
     * @param inputStream 服务器返回的输入流
     * @param encode 编码格式
     * @return 解析后的字符串
     */
    private static String changeInputStream(InputStream inputStream, String encode) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int len = 0;
        String result="";
        if (inputStream != null) {
            try {
                while ((len = inputStream.read(data)) != -1) {
                    outputStream.write(data,0,len);
                }
                result=new String(outputStream.toByteArray(),encode);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }




}
