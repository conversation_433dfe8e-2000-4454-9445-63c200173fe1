package com.quhong.data.vo;


public class RingOfRelationShipVO extends OtherRankConfigVO {


    //
    public static class NoteInfoVO {
        // 已发送亲密礼物的用户
        private String noteId; // 小纸条id
        private String name;
        private String head;
        private String aid;
        private Integer gender; // 1男2女
        private String countryFlag; // 国旗
        private Integer isApply; // 0: 未申请 1: 已申请

        // message 消息
        private String fromUid; // 邀请方
        private String fromName; // 邀请方
        private String fromHead; // 邀请方
        private String toUid; // 被邀请方
        private String toName; // 被邀请方
        private String toHead; // 被邀请方
        private Integer applyType; // 申请状态 0: 等待审批 1: 已过期 2: 已同意 3: 已拒绝
        private Integer relationShipType; // 关系类型 1: cp 2: 兄弟 3: 姐妹 4: 死党
        private Integer publishTime; // 发布时间


        // unbind相关
        // relationShipType
        private String relationShipName; // 关系名对象的用户名
        private String relationShipHead; // 关系名对象的头像
        private String relationShipAid; // 关系名对象的uid
        private String buildRelationTime; // 建立关系时间
        private Integer intimacy; // 当前的亲密度
        private Integer unbindTime; // 解除关系时间

        public String getNoteId() {
            return noteId;
        }

        public void setNoteId(String noteId) {
            this.noteId = noteId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public Integer getGender() {
            return gender;
        }

        public void setGender(Integer gender) {
            this.gender = gender;
        }

        public String getCountryFlag() {
            return countryFlag;
        }

        public void setCountryFlag(String countryFlag) {
            this.countryFlag = countryFlag;
        }

        public Integer getIsApply() {
            return isApply;
        }

        public void setIsApply(Integer isApply) {
            this.isApply = isApply;
        }

        public String getFromUid() {
            return fromUid;
        }

        public void setFromUid(String fromUid) {
            this.fromUid = fromUid;
        }

        public String getFromName() {
            return fromName;
        }

        public void setFromName(String fromName) {
            this.fromName = fromName;
        }

        public String getFromHead() {
            return fromHead;
        }

        public void setFromHead(String fromHead) {
            this.fromHead = fromHead;
        }

        public String getToUid() {
            return toUid;
        }

        public void setToUid(String toUid) {
            this.toUid = toUid;
        }

        public String getToName() {
            return toName;
        }

        public void setToName(String toName) {
            this.toName = toName;
        }

        public String getToHead() {
            return toHead;
        }

        public void setToHead(String toHead) {
            this.toHead = toHead;
        }

        public Integer getApplyType() {
            return applyType;
        }

        public void setApplyType(Integer applyType) {
            this.applyType = applyType;
        }

        public Integer getRelationShipType() {
            return relationShipType;
        }

        public void setRelationShipType(Integer relationShipType) {
            this.relationShipType = relationShipType;
        }

        public Integer getPublishTime() {
            return publishTime;
        }

        public void setPublishTime(Integer publishTime) {
            this.publishTime = publishTime;
        }

        public String getRelationShipName() {
            return relationShipName;
        }

        public void setRelationShipName(String relationShipName) {
            this.relationShipName = relationShipName;
        }

        public String getRelationShipHead() {
            return relationShipHead;
        }

        public void setRelationShipHead(String relationShipHead) {
            this.relationShipHead = relationShipHead;
        }

        public String getRelationShipAid() {
            return relationShipAid;
        }

        public void setRelationShipAid(String relationShipAid) {
            this.relationShipAid = relationShipAid;
        }

        public String getBuildRelationTime() {
            return buildRelationTime;
        }

        public void setBuildRelationTime(String buildRelationTime) {
            this.buildRelationTime = buildRelationTime;
        }

        public Integer getIntimacy() {
            return intimacy;
        }

        public void setIntimacy(Integer intimacy) {
            this.intimacy = intimacy;
        }

        public Integer getUnbindTime() {
            return unbindTime;
        }

        public void setUnbindTime(Integer unbindTime) {
            this.unbindTime = unbindTime;
        }
    }

    // 每日任务
    public static class DailyTaskInfo {
        private String dayStr;
        private String endUid; // 本伦结束发言用户id
        private String nextUid; // 下一个发言用户id
        private int round; // 当前聊天的轮次

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public String getEndUid() {
            return endUid;
        }

        public void setEndUid(String endUid) {
            this.endUid = endUid;
        }

        public String getNextUid() {
            return nextUid;
        }

        public void setNextUid(String nextUid) {
            this.nextUid = nextUid;
        }

        public int getRound() {
            return round;
        }

        public void setRound(int round) {
            this.round = round;
        }
    }

    // 亲密关系数据
    public static class RelationShipInfo {
        private String fromUid; // 申请方
        private String toUid; // 被邀请方
        private String relationShipType; // 关系类型 1: cp 2: 兄弟 3: 姐妹 4: 死党
        private int fromHide; // 申请方是否隐藏 0否 1是
        private int toHide; // 被邀请方是否隐藏 0否 1是
        private Integer ctime; // 申请时间

        public String getFromUid() {
            return fromUid;
        }

        public void setFromUid(String fromUid) {
            this.fromUid = fromUid;
        }

        public String getToUid() {
            return toUid;
        }

        public void setToUid(String toUid) {
            this.toUid = toUid;
        }

        public String getRelationShipType() {
            return relationShipType;
        }

        public void setRelationShipType(String relationShipType) {
            this.relationShipType = relationShipType;
        }

        public int getFromHide() {
            return fromHide;
        }

        public void setFromHide(int fromHide) {
            this.fromHide = fromHide;
        }

        public int getToHide() {
            return toHide;
        }

        public void setToHide(int toHide) {
            this.toHide = toHide;
        }

        public Integer getCtime() {
            return ctime;
        }

        public void setCtime(Integer ctime) {
            this.ctime = ctime;
        }
    }




}
