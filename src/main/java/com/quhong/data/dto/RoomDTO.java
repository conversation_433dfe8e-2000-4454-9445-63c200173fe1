package com.quhong.data.dto;

import com.quhong.handler.HttpEnvData;

public class RoomDTO extends HttpEnvData {
    private String roomName; // 房间名字
    private String roomHead; // 房间头像
    private String roomAnnounce; // 房间公告
    @Deprecated
    private int roomType; // 房间类型，1直播、2普通语音、3音乐房
    private int roomMode = 1; // 房间模式，1Voice、2Live
    private String token;
    private String ip;
    private String lang;
    private long requestTime;
    private int tagId = 2; // 标签id
    private boolean isChangeRoomName;
    private int mos;
    // 内部字段
    private boolean roomChange; // 房间类型切换

    private int fromScene; // 进入房间的来源场景  0 未知 1 搜索场景进入
    private int emojiType; // 表情分类  0: 麦位表情 1: 公屏表情  2: 私信表情  3新版游戏房麦位表情 5私信互动表情
    private String aid;
    private String enterRoomSource;

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    @Deprecated
    public int getRoomType() {
        return roomType;
    }

    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }

    public int getRoomMode() {
        return roomMode;
    }

    public void setRoomMode(int roomMode) {
        this.roomMode = roomMode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }

    public int getTagId() {
        return tagId;
    }

    public void setTagId(int tagId) {
        this.tagId = tagId;
    }

    public boolean isChangeRoomName() {
        return isChangeRoomName;
    }

    public void setChangeRoomName(boolean changeRoomName) {
        isChangeRoomName = changeRoomName;
    }

    public int getMos() {
        return mos;
    }

    public void setMos(int mos) {
        this.mos = mos;
    }

    public boolean isRoomChange() {
        return roomChange;
    }

    public void setRoomChange(boolean roomChange) {
        this.roomChange = roomChange;
    }

    public int getFromScene() {
        return fromScene;
    }

    public void setFromScene(int fromScene) {
        this.fromScene = fromScene;
    }

    public int getEmojiType() {
        return emojiType;
    }

    public void setEmojiType(int emojiType) {
        this.emojiType = emojiType;
    }

    public String getRoomHead() {
        return roomHead;
    }

    public void setRoomHead(String roomHead) {
        this.roomHead = roomHead;
    }

    public String getRoomAnnounce() {
        return roomAnnounce;
    }

    public void setRoomAnnounce(String roomAnnounce) {
        this.roomAnnounce = roomAnnounce;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getEnterRoomSource() {
        return enterRoomSource;
    }

    public void setEnterRoomSource(String enterRoomSource) {
        this.enterRoomSource = enterRoomSource;
    }
}
