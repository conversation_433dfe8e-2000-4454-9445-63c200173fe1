package com.quhong.controllers;

import com.quhong.constant.LoginConstant;
import com.quhong.constant.LoginHttpCode;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.*;
import com.quhong.data.vo.BeforeLoginVO;
import com.quhong.data.vo.LoginRespVO;
import com.quhong.data.vo.RegisterRespVO;
import com.quhong.enums.ClientOS;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.exception.CommonException;
import com.quhong.handler.WebController;
import com.quhong.intercepters.UserInterceptor;
import com.quhong.service.AbstractLoginService;
import com.quhong.service.AccountService;
import com.quhong.service.impl.*;
import com.quhong.task.MigrationTask;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("${baseUrl}")
public class RegisterOrLoginController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(RegisterOrLoginController.class);

    @Resource
    private AppleJwtLoginService appleJwtLoginService;
    @Resource
    private FaceBookLoginService faceBookLoginService;
    @Resource
    private FireBaseMailLoginService fireBaseMailLoginService;
    @Resource
    private FireBasePhoneLoginService fireBasePhoneLoginService;
    @Resource
    private GoogleLoginService googleLoginService;
    @Resource
    private HuaWeiLoginService huaWeiLoginService;
    @Resource
    private SpecialGustLoginService specialGustLoginService;
    @Resource
    private TestAccountLoginService testAccountLoginService;
    @Resource
    private HuaWeiPhoneLoginService huaWeiPhoneLoginService;
    @Resource
    private MigrationTask migrationTask;
    @Resource
    private AccountService accountService;

    /**
     * 登入或注册账号
     */
    @RequestMapping("register_or_login")
    private String registerOrLogin(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        RegisterOrLoginDTO req = RequestUtils.getSendData(request, RegisterOrLoginDTO.class);
        if (ServerConfig.isNotProduct() && req.getOs() == ClientOS.IOS && req.getVersioncode() == 794) {
            throw new CommonException(HttpCode.UPDATE_APP);
        }
//        RegisterOrLoginDTO req = RequestUtils.getSendDataFromNoCache(request, RegisterOrLoginDTO.class);

        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("registerOrLogin get req={}", req);
        AbstractLoginService abstractLoginService = getService(req.getType());
        LoginRespVO vo = abstractLoginService.registerOrLogin(req);
        logger.info("reqId={} timeMillis={}", req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 检查手机号是否存在
     */
    @RequestMapping("check_phone")
    private String checkPhone(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        CheckOutPhoneDTO req = RequestUtils.getSendDataFromNoCache(request, CheckOutPhoneDTO.class);
        if (req == null) {
            String remoteIp = RequestUtils.getIpAddress(request);
            logger.info("check_phone req is null. path={} remoteAddress={}", request.getRequestURI(), remoteIp);
            req = new CheckOutPhoneDTO();
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("checkPhone get req={}", req);
        request.setAttribute(UserInterceptor.CLIENT_LANG, req.getSlang() == SLangType.ENGLISH ? "en_US" : "ar");
        if (!AppVersionUtils.versionCheck(850, req)) {
            logger.info("checkPhone get req={} need update app", req);
            throw new CommonException(LoginHttpCode.UPDATE_APP);
        }
        BeforeLoginVO vo = fireBasePhoneLoginService.checkPhone(req);
        logger.info("check_phone vo={} timeMillis={}", vo, System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    private AbstractLoginService getService(int type) {
        AbstractLoginService abstractLoginService;
        switch (type) {
            case LoginConstant.GOOGLE_TYPE: {
                abstractLoginService = googleLoginService;
                break;
            }
            case LoginConstant.FACE_BOOK_TYPE: {
                abstractLoginService = faceBookLoginService;
                break;
            }
            case LoginConstant.FIRE_BASE_MAIL_TYPE: {
                abstractLoginService = fireBaseMailLoginService;
                break;
            }
            case LoginConstant.FIRE_BASE_PHONE_TYPE: {
                abstractLoginService = fireBasePhoneLoginService;
                break;
            }
            case LoginConstant.APPLE_TYPE: {
                abstractLoginService = appleJwtLoginService;
                break;
            }
            case LoginConstant.HUAWEI_TYPE: {
                abstractLoginService = huaWeiLoginService;
                break;
            }
            case LoginConstant.SPECIAL_GUST_TYPE: {
                abstractLoginService = specialGustLoginService;
                break;
            }
            case LoginConstant.HUAWEI_PHONE_TYPE: {
                abstractLoginService = huaWeiPhoneLoginService;
                break;
            }
            default:
                logger.info("type={} invalid", type);
                throw new CommonException(LoginHttpCode.PARAM_ERROR);
        }
        return abstractLoginService;
    }

    /**
     * 快速注册账号
     */
    @RequestMapping("registerHumanAccount")
    private String registerHumanAccount(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        RegisterPhoneAccountDTO req = RequestUtils.getSendData(request, RegisterPhoneAccountDTO.class);
        logger.info("registerHumanAccount get req={}", req);
        RegisterRespVO vo = testAccountLoginService.registerHumanAccount(req);
        logger.info("reqId={} timeMillis={}", req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 发送华为验证短信
     */
    @RequestMapping("sendSms")
    private String sendSms(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        CheckOutPhoneDTO req = RequestUtils.getSendDataFromNoCache(request, CheckOutPhoneDTO.class);
        if (req == null) {
            String remoteIp = RequestUtils.getIpAddress(request);
            logger.info("sendSms req is null. path={} remoteAddress={}", request.getRequestURI(), remoteIp);
            req = new CheckOutPhoneDTO();
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        logger.info("sendSms get req={}", req);
        huaWeiPhoneLoginService.sendSms(req);
        logger.info("sendSms -->timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, new Object());
    }

    /**
     * 检查设备发送短信剩余数
     */
//    @RequestMapping("checkSendNum")
//    private String checkSendNum(HttpServletRequest request) {
//        long millis = System.currentTimeMillis();
//        CheckOutPhoneDTO req = RequestUtils.getSendDataFromNoCache(request, CheckOutPhoneDTO.class);
//        if (req == null) {
//            String remoteIp = RequestUtils.getIpAddress(request);
//            logger.info("incSendNum req is null. path={} remoteAddress={}", request.getRequestURI(), remoteIp);
//            req = new CheckOutPhoneDTO();
//            return createResult(req, HttpCode.PARAM_ERROR, new Object());
//        }
//        req.setIp(RequestUtils.getIpAddress(request));
//        logger.info("checkSendNum get req={}", req);
//        BeforeLoginVO vo = fireBasePhoneLoginService.checkSendNum(req);
//        logger.info("checkSendNum BeforeLoginVO={} timeMillis={}", vo, System.currentTimeMillis() - millis);
//        return createResult(req, HttpCode.SUCCESS, vo);
//    }


    /**
     * 增加设备发送短信数
     */
    @RequestMapping("incSendNum")
    private String incSendNum(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        BeforeLoginDTO req = RequestUtils.getSendDataFromNoCache(request, BeforeLoginDTO.class);
        if (req == null) {
            String remoteIp = RequestUtils.getIpAddress(request);
            logger.info("incSendNum req is null. path={} remoteAddress={}", request.getRequestURI(), remoteIp);
            req = new BeforeLoginDTO();
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        req.setIp(RequestUtils.getIpAddress(request));
        logger.info("incSendNum get req={}", req);
        BeforeLoginVO vo = fireBasePhoneLoginService.incSendNum(req);
        logger.info("incSendNum BeforeLoginVO={} timeMillis={}", vo, System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }

    /**
     * 手动升级苹果登录token
     */
    @GetMapping("appleLoginTokenUpgrade")
    public String appleLoginTokenUpgrade(@RequestParam Integer rid) {
        logger.info("appleLoginTokenUpgrade rid={}", rid);
        return migrationTask.manualMigration(rid);
    }

    /**
     * 收集数美数据
     */
    @RequestMapping("shuMeiDeviceData")
    private String shuMeiDeviceData(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        ShuMeiDeviceDTO req = RequestUtils.getSendData(request, ShuMeiDeviceDTO.class);
        assert req != null;
        req.setIp(RequestUtils.getIpAddress(request));
//        logger.info("shuMeiDeviceData get req={}", req);
        if (StringUtils.isEmpty(req.getUid())) {
            return createResult(req, HttpCode.PARAM_ERROR, new Object());
        }
        req.setFromInterface("shuMeiDeviceData");
//        accountService.shuMeiDeviceData(req);
//        logger.info("shuMeiDeviceData timeMillis={}", System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, new Object());
    }


}
