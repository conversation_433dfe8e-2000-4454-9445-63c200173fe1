package com.quhong.controllers;


import com.quhong.data.vo.FirstKingdomVO;
import com.quhong.datas.HttpResult;
import com.quhong.handler.H5Controller;
import com.quhong.service.FirstKingdomService;
import com.quhong.vo.PageVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 第一王国
 */


@RestController
@RequestMapping(value = "/activity/", produces = MediaType.APPLICATION_JSON_VALUE)
public class FirstKingdomController extends H5Controller {

    private static final Logger logger = LoggerFactory.getLogger(FirstKingdomController.class);

    @Resource
    private FirstKingdomService firstKingdomService;


    @RequestMapping("firstKingdomConfig")
    public HttpResult<FirstKingdomVO> firstKingdomConfig(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(firstKingdomService.config(activityId, uid));
    }

    @RequestMapping("firstKingdomRankInfo")
    public HttpResult<FirstKingdomVO> firstKingdomRankInfo(@RequestParam String activityId, @RequestParam String uid) {
        return HttpResult.getOk(firstKingdomService.rankInfo(activityId, uid));
    }

    @RequestMapping("firstKingdomDraw")
    public HttpResult<FirstKingdomVO> firstKingdomDraw(@RequestParam String activityId, @RequestParam String uid, @RequestParam int amount, @RequestParam int drawType, @RequestParam int attackTeamType) {
        logger.info("firstKingdomDraw activityId: {}, uid:{}, amount={}, drawType={}, attackTeamType={}", activityId, uid, amount, drawType, attackTeamType);
        return HttpResult.getOk(firstKingdomService.draw(activityId, uid, amount, drawType, attackTeamType));
    }

    @RequestMapping("firstKingdomJoinTeam")
    public HttpResult<FirstKingdomVO> firstKingdomJoinTeam(@RequestParam String activityId, @RequestParam String uid, @RequestParam int teamType) {
        return HttpResult.getOk(firstKingdomService.joinTeam(activityId, uid, teamType));
    }

    @RequestMapping("firstKingdomHistoryList")
    public HttpResult<PageVO<FirstKingdomVO.ResourceMetaTmp>> firstKingdomHistoryList(@RequestParam String activityId, @RequestParam String uid, @RequestParam int page) {
        return HttpResult.getOk(firstKingdomService.getHistoryListPageRecord(activityId, uid, page));
    }

    @RequestMapping("firstKingdomCollectReward")
    public HttpResult<?> firstKingdomCollectReward(@RequestParam String activityId, @RequestParam String uid) {
        firstKingdomService.collectReward(activityId, uid);
        return HttpResult.getOk();
    }

}
