package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.data.MsgRecordRsp;
import com.quhong.data.dto.MsgRecordDTO;
import com.quhong.data.dto.MsgSendDTO;
import com.quhong.data.dto.SendChatMsgDTO;
import com.quhong.data.dto.TempToAidDTO;
import com.quhong.data.vo.MsgRecordListVO;
import com.quhong.data.vo.MsgRecordVO;
import com.quhong.data.vo.UnreadMsgVO;
import com.quhong.enums.*;
import com.quhong.handler.BaseController;
import com.quhong.managers.*;
import com.quhong.mysql.dao.ActivityShareConfigDao;
import com.quhong.mysql.data.ActivityShareConfigData;
import com.quhong.service.GreetMeetService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "inner/java_msg/", produces = MediaType.APPLICATION_JSON_VALUE)
public class InnerMsgController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(InnerMsgController.class);
    private static final Long TIME_OUT = 20 * 1000L;
    private static final String REPLACE_USERNAME = "${username}";

    @Autowired
    private SendChatMsgProcessor sendChatMsgProcessor;
    @Autowired
    private ChatMsgRecordProcessor chatMsgRecordProcessor;
    @Autowired
    private ChatMsgDeleteProcessor deleteProcessor;
    @Autowired
    private ActivityShareConfigDao activityShareConfigDao;
    @Autowired
    private ChatMsgUnreadProcessor chatMsgUnreadProcessor;
    @Resource
    private GreetMeetService greetMeetService;
    @Resource
    private ChatMsgListProcessor chatMsgListProcessor;

    @RequestMapping("send_msg")
    public String sendChatMsg(@RequestBody SendChatMsgDTO dto) {
        logger.info("pre inner send chat msg. {} {}", dto, dto.getRequestId());
        MsgSendDTO reqData = new MsgSendDTO();
        reqData.copyFrom(dto);
        reqData.setInner(true);
        if (MsgType.SHARE_ACTIVITY == reqData.getMsg_type()) {
            JSONObject msg_info = reqData.getMsg_info();
            if (null != msg_info && !StringUtils.isEmpty(msg_info.getIntValue("shareId"))) {
                int fillDone = msg_info.getIntValue("fillDone");
                if (fillDone <= 0){
                    ActivityShareConfigData shareConfigData = activityShareConfigDao.selectOne(msg_info.getIntValue("shareId"));
                    if (shareConfigData == null) {
                        return createError(reqData, MsgHttpCode.PARAM_ERROR);
                    }
                    setActivityShareInfo(msg_info, shareConfigData, dto.getSlang(), dto.getUid());
                }
            }
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                sendChatMsgProcessor.doSendMsg(reqData);
            }
        });
        return createResult(HttpCode.SUCCESS, null);
    }

    private void setActivityShareInfo(JSONObject msg_info, ActivityShareConfigData shareConfigData, int slang, String aid) {
        msg_info.put("activity_id", shareConfigData.getActivityId());
        msg_info.put("activity_name", slang == SLangType.ARABIC ? shareConfigData.getNameAr() : shareConfigData.getName());
        msg_info.put("activity_desc", slang == SLangType.ARABIC ? shareConfigData.getDescriptionAr() : shareConfigData.getDescription());
        msg_info.put("activity_icon", slang == SLangType.ARABIC ? shareConfigData.getIconAr() : shareConfigData.getIcon());
        msg_info.put("activity_banner", slang == SLangType.ARABIC ? shareConfigData.getBannerAr() : shareConfigData.getBanner());
        if (msg_info.getIntValue("helpType") == 1) {
            //2025国庆节助力
            msg_info.put("activity_url", shareConfigData.getUrl() + "&helpType=1&aid=" + aid);
        } else {
            msg_info.put("activity_url", shareConfigData.getUrl());
        }
    }

    @RequestMapping("msg_records")
    public String getRecordsList(HttpServletRequest request, @RequestBody MsgRecordDTO dto) {
        ChatMsgRecordProcessor.ChatMsgRecordReq reqData = new ChatMsgRecordProcessor.ChatMsgRecordReq();
        reqData.copyFrom(dto);
        ApiResult<ChatMsgRecordProcessor.MsgRecordListRsp> apiResult = chatMsgRecordProcessor.getList(reqData);
        return createResult(reqData, apiResult.getCode(), createMsgList(apiResult.getData()));
    }

    @RequestMapping("list/test/get")
    public String getMsgList(HttpServletRequest request, @RequestBody MsgRecordDTO dto) {
        ChatMsgListProcessor.ChatMsgListReq reqData = new ChatMsgListProcessor.ChatMsgListReq();
        reqData.setUid(dto.getUid());
        reqData.setSlang(1);
        reqData.setPage(dto.getPage());
        reqData.setPageSize(dto.getPageSize());
        long timeMillis = System.currentTimeMillis();
        ApiResult<ChatMsgListProcessor.MsgListRsp> apiResult = chatMsgListProcessor.getList(reqData);
        logger.info("get msg list cost={}", System.currentTimeMillis() - timeMillis);
        return createResult(reqData, apiResult.getCode(), apiResult.getData());
    }

    @RequestMapping("record/test/get")
    public String getTestRecordsList(HttpServletRequest request, @RequestBody MsgRecordDTO dto) {
        ChatMsgRecordProcessor.ChatMsgRecordReq reqData = new ChatMsgRecordProcessor.ChatMsgRecordReq();
        reqData.copyFrom(dto);
        ApiResult<ChatMsgRecordProcessor.MsgRecordListRsp> apiResult = chatMsgRecordProcessor.getList(reqData);
        return createResult(reqData, apiResult.getCode(), apiResult.getData());
    }

    private MsgRecordListVO createMsgList(ChatMsgRecordProcessor.MsgRecordListRsp rsp) {
        MsgRecordListVO vo = new MsgRecordListVO();
        List<MsgRecordVO> recordList = new ArrayList<>();
        for (MsgRecordRsp recordRsp : rsp.getList()) {
            MsgRecordVO recordVO = new MsgRecordVO();
            recordVO.setMsgId(recordRsp.getMsgId());
            recordVO.setMsgType(recordRsp.getMsgType());
            recordVO.setMsg(recordRsp.getMsg());
            recordVO.setFromUid(recordRsp.getFromUid());
            recordVO.setToUid(recordRsp.getToUid());
            recordVO.setTimestamp(recordRsp.getTimestamp());
            recordVO.setStatus(recordRsp.getStatus());
            recordVO.setInfo(recordRsp.getInfo());
            recordList.add(recordVO);
        }
        vo.setList(recordList);
        return vo;
    }

    // 获取与某账号未读数量
    @RequestMapping("msg_account_unread")
    public String getAccountUnreadMsg(HttpServletRequest request, @RequestBody TempToAidDTO dto) {
        logger.info("getAccountUnreadMsg. uid={} aid={}", dto.getUid(), dto.getAid());
        ApiResult<UnreadMsgVO> apiResult = chatMsgUnreadProcessor.getAccountUnreadMsg(dto);
        return createResult(dto, apiResult.getCode(), apiResult.getData());
    }

    @RequestMapping("msg_delete")
    public String deleteMsg(HttpServletRequest request, @RequestBody TempToAidDTO dto) {
        ApiResult<Object> apiResult = deleteProcessor.deleteAll(dto.getUid(), dto.getAid(), true);
        return createResult(dto, apiResult.getCode(), apiResult.getData());
    }

    @RequestMapping("both_msg_delete")
    public String deleteBothMsg(HttpServletRequest request, @RequestBody TempToAidDTO dto) {
        logger.info("delete both msg records. uid={} aid={}", dto.getUid(), dto.getAid());
        ApiResult<Object> apiResult = deleteProcessor.deleteBothMsg(dto.getUid(), dto.getAid());
        return createResult(dto, apiResult.getCode(), apiResult.getData());
    }

    // 删除某条消息记录
    @RequestMapping("msg_record_delete")
    public String deleteRecordMsg(HttpServletRequest request, @RequestBody TempToAidDTO dto) {
        ApiResult<Object> apiResult = deleteProcessor.deleteMsgRecord(dto.getMsgId(), dto.getUid());
        return createResult(dto, apiResult.getCode(), apiResult.getData());
    }

    // 更新对话为普通聊天
    @RequestMapping("updateNormalChat")
    public String updateNormalChat(@RequestBody TempToAidDTO dto) {
        greetMeetService.updateNormalChat(dto);
        return createResult(HttpCode.SUCCESS, null);
    }
}
