import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.zone.ZoneRules;
import java.util.Date;
import java.util.Objects;
import java.util.stream.LongStream;
import java.util.stream.Stream;


public class DateSupport {
    private static final int SECONDS_PER_DAY = 86400;

    public static final DateSupport UTC = new DateSupport(ZoneOffset.UTC);
    public static final DateSupport ARABIAN = new DateSupport(ZoneOffset.of("+3"));
    public static final DateSupport BEIJING = new DateSupport(ZoneOffset.of("+8"));

    private static DateTimeFormatter yyyy_mm_dd = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static DateTimeFormatter yyyy_mm = DateTimeFormatter.ofPattern("yyyy_MM");
    private static DateTimeFormatter yyyyMMdd = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static DateTimeFormatter yyyy_m_d = DateTimeFormatter.ofPattern("yyyy-M-d");

    public String yyyyMMdd() {
        return getToday().format(yyyyMMdd);
    }

    public static String yyyyMMdd(LocalDate localDate) {
        return localDate.format(yyyyMMdd);
    }

    public LocalDate parse_yyyyMMdd(String date) {
        return LocalDate.parse(date, yyyyMMdd);
    }

    /**
     * 解析，默认格式 yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static LocalDate parse(String date) {
        return LocalDate.parse(date, yyyy_mm_dd);
    }

    /**
     * 解析，yyyy-M-d
     *
     * @param date
     * @return
     */
    public static LocalDate parseYyyymd(String date) {
        return LocalDate.parse(date, yyyy_m_d);
    }

    /**
     * 格式化，默认格式 yyyy-MM-dd
     *
     * @param localDate
     * @return
     */
    public static String format(LocalDate localDate) {
        return localDate.format(yyyy_mm_dd);
    }

    /**
     * 解析 格式 yyyy_MM 用户分表
     *
     * @param date
     * @return
     */
    public static LocalDate parseYYYY_MM(String date) {
        return LocalDate.parse(date, yyyy_mm);
    }

    /**
     * 格式化 yyyy_MM
     *
     * @param localDate
     * @return
     */
    public static String formatYYYY_MM(LocalDate localDate) {
        return localDate.format(yyyy_mm);
    }

    public static Period getPeriod(LocalDate start, LocalDate end) {
        return Period.between(start, end);
    }

    /**
     * 如果start < end
     * duration。toDays 为负数
     *
     * @param start
     * @param end
     * @return
     */
    public static Duration getDuration(LocalDate start, LocalDate end) {
        return Duration.between(toLocalDateTime(start), toLocalDateTime(end));
    }

    /**
     * 转换成lcoalDateTime
     *
     * @param localDate
     * @return
     */
    public static LocalDateTime toLocalDateTime(LocalDate localDate) {
        return toLocalDateTime(localDate, LocalTime.of(0, 0));
    }

    public static LocalDateTime toLocalDateTime(LocalDate localDate, LocalTime localTime) {
        return LocalDateTime.of(localDate, localTime);
    }

    /**
     * 获取连续的日期stream
     *
     * @param start
     * @param endExclude 不包含end时间
     * @return
     */
    public static Stream<LocalDate> datesUntil(LocalDate start, LocalDate endExclude) {
        if (start.compareTo(endExclude) < 0) {
            return doDatesUntil(start, endExclude);
        } else {
            return doDatesUntil(endExclude, start);
        }

    }

    /**
     * java9之后有内置方法
     *
     * @param startDate
     * @param endExclusive
     * @return
     */
    public static Stream<LocalDate> doDatesUntil(LocalDate startDate, LocalDate endExclusive) {
        long end = endExclusive.toEpochDay();
        long start = startDate.toEpochDay();
        if (end < start) {
            throw new IllegalArgumentException(endExclusive + " < " + startDate);
        }
        return LongStream.range(start, end).mapToObj(LocalDate::ofEpochDay);
    }

    private ZoneOffset zoneOffset;

    public DateSupport(ZoneOffset zoneOffset) {
        this.zoneOffset = zoneOffset;
    }

    public LocalDate getLocalDate(Date date) {
        return ofInstant(date.toInstant(), zoneOffset);
    }


    /**
     * 获取LcoalDate
     *
     * @param epochMilli
     * @return
     */
    public LocalDate getLocalDate(long epochMilli) {
        Instant instant = Instant.ofEpochMilli(epochMilli);
        return ofInstant(instant, zoneOffset);
    }

    /**
     * java9 有本地方法
     *
     * @param instant
     * @param zone
     * @return
     */
    private LocalDate ofInstant(Instant instant, ZoneId zone) {
        Objects.requireNonNull(instant, "instant");
        Objects.requireNonNull(zone, "zone");
        ZoneRules rules = zone.getRules();
        ZoneOffset offset = rules.getOffset(instant);
        long localSecond = instant.getEpochSecond() + offset.getTotalSeconds();
        long localEpochDay = Math.floorDiv(localSecond, SECONDS_PER_DAY);
        return LocalDate.ofEpochDay(localEpochDay);
    }

    /**
     * 获取开始lcoalData
     *
     * @return
     */
    public LocalDate getToday() {
        return LocalDate.now(zoneOffset);
    }

    /**
     * 获取昨天开始date
     *
     * @return
     */
    public LocalDate getYesterday() {
        return getDayOffset(-1);
    }

    public LocalDate getDayOffset(int offset) {
        return getToday().plusDays(offset);
    }

    public long getTodayStartTime() {
        return getTimeSeconds(getToday());
    }

    public long getTimeSeconds(LocalDate localDate) {
        return getTimeSeconds(localDate, LocalTime.of(0, 0));
    }

    public long getTimeMillis(LocalDate localDate) {
        return getTimeSeconds(localDate, LocalTime.of(0, 0)) * 1000;
    }

    public Date getDate(LocalDate localDate) {
        return new Date(getTimeMillis(localDate));
    }

    /**
     * java9之后有内置方法
     *
     * @param localDate
     * @param localTime
     * @return
     */
    public long getTimeSeconds(LocalDate localDate, LocalTime localTime) {
        return toLocalDateTime(localDate, localTime).toEpochSecond(zoneOffset);
    }
}
