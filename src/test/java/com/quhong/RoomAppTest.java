package com.quhong;

import com.quhong.room.redis.RoomPlayerRedis;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertTrue;

/**
 * Unit test for simple App.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RoomAppTest {
    private static final Logger logger = LoggerFactory.getLogger(RoomAppTest.class);

    @Autowired
    private RoomPlayerRedis roomPlayerRedis;

    /**
     * Rigorous Test :-)
     */
    @Test
    public void shouldAnswerWithTrue() {
        assertTrue(true);
    }

    @Test
    public void testRedis() {
        String roomId = "r:abc";
        Set<String> uidSet = new HashSet<>();
        uidSet.add("a");
        uidSet.add("b");
        uidSet.add("c");
        roomPlayerRedis.dismissRoom("r:abc");
        Set<String> retSet = roomPlayerRedis.getRoomActors(roomId);
    }
//
//    @Test
//    public void testJSON(){
//        EnterUserData enterUserData = new EnterUserData();
//        String json = JSON.toJSONString(enterUserData);
//        logger.info("{}", json);
//    }

//    @Test
//    public void testDetection(){
//        textDetection.detect("fuckeveryone");
//    }
}
